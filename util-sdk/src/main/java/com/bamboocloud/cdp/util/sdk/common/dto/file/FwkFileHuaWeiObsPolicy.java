package com.bamboocloud.cdp.util.sdk.common.dto.file;

import com.alibaba.fastjson.annotation.JSONField;
import com.bamboocloud.cdp.util.sdk.common.constant.FwkFileHuaWeiObsPolicyConstant;
import com.huaweicloud.sdk.iam.v3.model.ServiceStatement;
import lombok.Data;

import java.util.List;

/**
 * 调用华为云IAM获取临时访问OBS凭证的请求参数
 */
@Data
public class FwkFileHuaWeiObsPolicy extends FwkFileBasePolicy {

    @JSONField(name = "Version")
    private String version = FwkFileHuaWeiObsPolicyConstant.VERSION;

    @JSONField(name = "Statement")
    private List<Statement> statement;

    @Data
    public static class Statement {

        @JSONField(name = "Effect")
        private ServiceStatement.EffectEnum effect = ServiceStatement.EffectEnum.ALLOW;

        @JSONField(name = "Action")
        private List<String> action;

        @JSONField(name = "Resource")
        private List<String> resource;
    }

}
