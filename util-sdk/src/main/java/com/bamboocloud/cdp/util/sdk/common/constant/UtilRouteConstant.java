/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: UtilRouteConstant.java
 * @createdDate: 2022/12/13 16:07:13
 *
 */

package com.bamboocloud.cdp.util.sdk.common.constant;


import com.bamboocloud.cdp.framework.core.constant.FwkRouteConstant;

/**
 * 记录Route信息
 *
 * <AUTHOR>
 */
public class UtilRouteConstant extends FwkRouteConstant {

    /**
     * Vendor
     */
    public static final String SECURITY_VENDOR_WX_MINI_AUTH_LOGIN = "v1/vendors/wx/security/actions/login";
    public static final String SECURITY_VENDOR_WX_APP_AUTH_LOGIN = "v1/vendors/wxApp/security/actions/login";

    public static final String SECURITY_VENDOR_WX_APP_GET_USER_INFO = "v1/vendors/wxApp/security/actions/getUserInfo";
    public static final String SECURITY_VENDOR_WX_MINI_EXTRACT_USER_MOBILE_INFO = "v1/vendors/wx/security/actions" +
            "/extractUserMobileInfo";

    /**
     * Vendor - product
     */
    public static final String VENDOR_PRODUCT_GENERATE_QRCODE = "v1/vendors/products/actions/generateQrCode";

    /**
     * Vendor - Shop
     */
    public static final String VENDOR_SHOP_GENERATE_QRCODE = "v1/vendors/shops/actions/generateQrCode";


    /**
     * Buyer
     */
    public static final String SECURITY_BUYER_WX_MINI_AUTH_LOGIN = "v1/buyers/wx/security/actions/login";

    public static final String SECURITY_BUYER_WX_APP_AUTH_LOGIN = "v1/buyers/wxApp/security/actions/login";

    public static final String SECURITY_BUYER_WX_APP_GET_USER_INFO = "v1/buyers/wxApp/security/actions/getUserInfo";
    public static final String SECURITY_BUYER_WX_MINI_EXTRACT_USER_MOBILE_INFO = "v1/buyers/wx/security/actions" +
            "/extractUserMobileInfo";

    /**
     * Buyer - SubscribeMessage
     */
    public static final String BUYER_SUBSCRIBE_MESSAGE_GET_TEMPLATE_LIST = "v1/buyers/subscribeMessages/actions/getTemplateList";
    public static final String BUYER_SUBSCRIBE_MESSAGE_GET_CATEGORY = "v1/buyers/subscribeMessages/actions/getCategory";
    public static final String BUYER_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_TITLE_LIST = "v1/buyers/subscribeMessages/actions/getPubTemplateTitleList";
    public static final String BUYER_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_KEYWORDS_BY_ID = "v1/buyers/subscribeMessages/actions/getPubTemplateKeyWordsById/{id}";
    public static final String BUYER_SUBSCRIBE_MESSAGE_ADD_TEMPLATE = "v1/buyers/subscribeMessages/actions/addTemplate";
    public static final String BUYER_SUBSCRIBE_MESSAGE_DELETE_TEMPLATE = "v1/buyers/subscribeMessages/actions/deleteTemplate/{id}";
    public static final String BUYER_SUBSCRIBE_MESSAGE_SEND_SUBSCRIBE_MESSAGE = "v1/buyers/subscribeMessages/actions/sendSubscribeMessage";

    /**
     * 天气接口
     */
    public static final String WEATHER_DATA_WEATHER_CITY_INFO = "v1/weather/data/getWeatherCityInfo";
    public static final String WEATHER_DATA_WEATHER_LIVE_INFO = "v1/weather/data/getWeatherLiveInfo";
    public static final String WEATHER_DATA_WEATHER_DETAIL_INFO = "v1/weather/data/getWeatherDetailInfo";
    public static final String WEATHER_DATA_YIKETIANQI_WEATHER_CITY_INFO = "v1/weather/data/yiketianqi/getWeatherCityInfo";






    public static final String BUYER_GET_WX_QRCODE_UN_LIMIT = "v1/buyers/actions/wx/qrcodeUnlimit";

    /**
     * Vendor - SubscribeMessage
     */
    public static final String VENDOR_SUBSCRIBE_MESSAGE_GET_TEMPLATE_LIST = "v1/vendors/subscribeMessages/actions/getTemplateList";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_GET_CATEGORY = "v1/vendors/subscribeMessages/actions/getCategory";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_TITLE_LIST = "v1/vendors/subscribeMessages/actions/getPubTemplateTitleList";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_KEYWORDS_BY_ID = "v1/vendors/subscribeMessages/actions/getPubTemplateKeyWordsById/{id}";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_ADD_TEMPLATE = "v1/vendors/subscribeMessages/actions/addTemplate";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_DELETE_TEMPLATE = "v1/vendors/subscribeMessages/actions/deleteTemplate/{id}";
    public static final String VENDOR_SUBSCRIBE_MESSAGE_SEND_SUBSCRIBE_MESSAGE = "v1/vendors/subscribeMessages/actions/sendSubscribeMessage";

    /**
     * Mama
     */
    public static final String SECURITY_MAMA_WX_MINI_AUTH_LOGIN = "v1/mamas/wx/security/actions/login";
    public static final String SECURITY_MAMA_WX_MINI_EXTRACT_USER_MOBILE_INFO = "v1/mamas/wx/security/actions/extractUserMobileInfo";

    public static final String SECURITY_MAMA_WX_MP_BUILD_AUTHORIZATION_URL = "v1/mamas/wx/mp/actions/buildAuthorizationUrl";
    public static final String SECURITY_MAMA_WX_MP_AUTH_LOGIN = "v1/mamas/wx/mp/actions/login";
    /*
    * 阿里云推送
    * */
    public static final String ALIYUN_APP_PUSH = "v1/aliyun/appPush";

    /**
     * 抖音接口
     */

    public static final String DOU_YIN_OAUTH_CLINT_TOKEN = "v1/douyin/oauth/client_token";
    public static final String DOU_YIN_GOODLIFE_FULFILMENT_CERTIFICATE_PREPARE = "v1/douyin/goodlife/fulfilment/certificate/prepare";
    public static final String DOU_YIN_GOODLIFE_FULFILMENT_CERTIFICATE_VERIFY = "v1/douyin/goodlife/fulfilment/certificate/verify";
    /**
     * 物流接口
     */
    public static final String GET_LOGISTICS_INFO = "v1/logistics/actions/getLogisticsInfo";
    public static final String GET_LOCATION_ADDRESS = "v1/location/actions/getLocationAddress";

    /*
     * 天眼查
     * */
    public static final String TIAN_YAN_CHA_ORGANIZATION = "v1/tianyancha/getOrganization";

    /*
     * 阿里云实名认证
     * */
    public static final String ALIYUN_IDCARDNUMBER_AUTH = "v1/aliyun/idCardNumberAuth";


    /*
     * email发送
     * */
    public static final String EMAIL_SEND = "v1/email/send";

    /*
     * email发送
     * */
    public static final String EMAIL_SEND_BY_ALI = "v1/email/sendByAli";


    /**
     * sms
     */
    public static final String ALIYUN_SMS = "v1/aliyun/sms";

    /*
     * file上传
     * */
    public static final String FILE_SAVE = "v1/file/save";

    /*
     * fileCopy
     * */
    public static final String FILE_COPY = "v1/file/copy";

    /*
     * file上传
     * */
    public static final String FILE_DOWNLOAD      = "v1/file/download";
    public static final String FILE_DOWNLOADA     = "v1/file/downloada";
    public static final String FILE_ADD_WATERMARK = "v1/file/addSlantedTextWatermark";

    public static final String FILE_DELETE = "v1/file/delete";

    public static final String GENERATE_AUTH_STS    = "v1/file/auth/generate";
    public static final String FILE_AUTH_GET_HUAWEI = "v1/file/auth/huawei/get";

}
