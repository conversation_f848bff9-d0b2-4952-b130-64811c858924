/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductQrCodeCreationVo.java
 * @createdDate: 2022/12/12 10:19:12
 *
 */

package com.bamboocloud.cdp.util.sdk.common.vo.product;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
public class ProductQrCodeCreationVo implements Serializable {

    /**
     * scene – 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~， 其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
     */
    @NotBlank(message = "scene不允许为空")
    private String scene;

    /**
     * page – 必须是已经发布的小程序页面，例如 "pages/index/index" ,如果不填写这个字段，默认跳主页面
     */
    private String page;

}
