/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: LogisticsResultDto.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.util.sdk.common.dto.logistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsResultDto implements Serializable {

    /**
     * 快递公司网址
     */
    private String companyUrl;

    /**
     * 订单编号
     */
    private String orderCode;

    /**
     * 物流运单号
     */
    private String logisticCode;
    /**
     * 快递公司编码
     */
    private String shipperCode;
    /**
     * 物流轨迹
     */
    private List<Trace> traces;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Trace implements Serializable {
        /**
         * 描述
         */
        private String acceptStation;
        /**
         * 时间
         */
        private String acceptTime;
    }

    /**
     * 物流状态：2-在途中,3-签收,4-问题件
     */
    private String state;

    /**
     * 用户ID
     */
    private String eBusinessId;

    /**
     * 成功与否
     */
    private Boolean success;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 备注
     */
    private String remark;
}
