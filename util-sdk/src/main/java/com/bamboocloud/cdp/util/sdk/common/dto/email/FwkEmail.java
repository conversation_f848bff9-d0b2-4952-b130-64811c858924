/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2019. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-framework-core
 * @file: FwkEmail.java
 * @createdDate: 2019/05/29 09:31:29
 */

package com.bamboocloud.cdp.util.sdk.common.dto.email;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FwkEmail {

    @NotNull
    private List<String> toAddresses;

    private List<String> ccAddresses;

    private List<String> bccAddresses;

    @NotBlank
    private String subject;

    @NotBlank
    private String text;

    @NotNull
    private boolean html;

    private List<FwkEmailAttachment> attachments;
}
