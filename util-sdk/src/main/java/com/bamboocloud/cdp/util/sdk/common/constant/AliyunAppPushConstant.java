/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: AliyunAppPushConstant.java
 * @createdDate: 2022/10/18 14:43:18
 *
 */

package com.bamboocloud.cdp.util.sdk.common.constant;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * <AUTHOR> Shu
 * @description:
 */
@AllArgsConstructor
@Getter
@EqualsAndHashCode
public class AliyunAppPushConstant {

    /**
     * 推送类型。取值：
     * MESSAGE：表示消息。
     * NOTICE：表示通知。
     */
    public static final AliyunAppPushConstant PUSH_TYPE_MESSAGE = new AliyunAppPushConstant("MESSAGE", "表示消息");
    public static final AliyunAppPushConstant PUSH_TYPE_NOTICE = new AliyunAppPushConstant("NOTICE", "表示通知");

    /**
     * 设备类型，取值范围为：
     * iOS：iOS设备
     * ANDROID：Android设备
     * ALL：全部设备类型
     * 说明 该参数仅对旧的不分端App有意义。新的分端App下，该参数填写“ALL”或与App分端类型对应的值均可。
     */
    public static final AliyunAppPushConstant DEVICE_TYPE_IOS = new AliyunAppPushConstant("iOS", "ios");
    public static final AliyunAppPushConstant DEVICE_TYPE_ANDROID = new AliyunAppPushConstant("ANDROID", "android");
    public static final AliyunAppPushConstant DEVICE_TYPE_ALL = new AliyunAppPushConstant("ALL", "全部设备类型");

    /**
     * 推送目标。可取值：
     * DEVICE：根据设备推送。
     * ACCOUNT：根据账号推送。
     * ALIAS：根据别名推送。
     * TAG：根据标签推送。
     * ALL：推送给全部设备（同一种DeviceType的两次全推的间隔至少为1秒）。
     * 说明 对iOS设备全推，会推送给24个月内活跃过但未卸载的设备，一旦APNs（苹果推送服务）接收到推送请求且未返回错误信息即为送达，导致活跃设备数暴增，从而产生大量费用，请您酌情使用。
     * TBD：初始化持续推送，推送目标由后续的ContinuouslyPush接口指定。
     */
    public static final AliyunAppPushConstant TARGET_DEVICE = new AliyunAppPushConstant("DEVICE", "根据设备推送");
    public static final AliyunAppPushConstant TARGET_ACCOUNT = new AliyunAppPushConstant("ACCOUNT", "根据账号推送");
    public static final AliyunAppPushConstant TARGET_ALIAS = new AliyunAppPushConstant("ALIAS", "根据别名推送");
    public static final AliyunAppPushConstant TARGET_TAG = new AliyunAppPushConstant("TAG", "根据标签推送");
    public static final AliyunAppPushConstant TARGET_ALL = new AliyunAppPushConstant("ALL", "推送给全部设备（同一种DeviceType的两次全推的间隔至少为1秒）");
    public static final AliyunAppPushConstant TARGET_TBD = new AliyunAppPushConstant("TBD", "初始化持续推送");

    /**
     * iOS的通知是通过APNs中心来发送的，需要填写对应的环境信息。
     * DEV：表示开发环境。
     * PRODUCT：表示生产环境。
     */
    public static final AliyunAppPushConstant IOS_APNS_ENV_DEV = new AliyunAppPushConstant("DEV", "表示开发环境");
    public static final AliyunAppPushConstant IOS_APNS_ENV_PRODUCT = new AliyunAppPushConstant("PRODUCT", "表示生产环境");

    /**
     * 点击通知后动作。可取值：
     * APPLICATION：打开应用（默认值）
     * ACTIVITY：打开应用AndroidActivity
     * URL：打开URL
     * NONE：无跳转
     */
    public static final AliyunAppPushConstant ANDROID_OPEN_TYPE_APPLICATION = new AliyunAppPushConstant("APPLICATION", "打开应用（默认值）");
    public static final AliyunAppPushConstant ANDROID_OPEN_TYPE_ACTIVITY = new AliyunAppPushConstant("ACTIVITY", "打开应用AndroidActivity");
    public static final AliyunAppPushConstant ANDROID_OPEN_TYPE_URL = new AliyunAppPushConstant("URL", "打开URL");
    public static final AliyunAppPushConstant ANDROID_OPEN_TYPE_NONE = new AliyunAppPushConstant("NONE", "无跳转");

    /**
     * 设置NotificationChannel参数
     */
    public static final AliyunAppPushConstant ANDROID_NOTIFICATION_CHANNEL_NOTIFICATION = new AliyunAppPushConstant("notification", "通知");
    public static final AliyunAppPushConstant ANDROID_NOTIFICATION_CHANNEL_TRADE = new AliyunAppPushConstant("trade", "订单通知");

    /**
     * AndroidExtParameters
     * 设定通知的扩展属性。当推送类型PushType设置为MESSAGE消息类型时，该属性不生效。
     * 该参数要以Json map的格式传入，否则会解析出错
     */
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_ACTIVITY = new AliyunAppPushConstant("ACTIVITY", "活动");
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_COUPON = new AliyunAppPushConstant("COUPON", "优惠券");
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_TRADE = new AliyunAppPushConstant("TRADE", "订单");
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_PRODUCT = new AliyunAppPushConstant("PRODUCT", "商品");
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_VOUCHER = new AliyunAppPushConstant("VOUCHER", "代金券");
    public static final AliyunAppPushConstant EXT_PARAMETERS_TYPE_VENDOR_COUPON = new AliyunAppPushConstant("VENDOR_COUPON", "商家优惠劵");

    private final String code;
    private final String name;
}
