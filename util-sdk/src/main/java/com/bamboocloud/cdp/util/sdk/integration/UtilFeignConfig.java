package com.bamboocloud.cdp.util.sdk.integration;

import feign.codec.Encoder;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

/**
 * <AUTHOR>
 * @date 2024/11/14
 * @description
 */
@Configuration
public class UtilFeignConfig {

    @Bean
    public Encoder feignFormEncoder() {
        // 因本地调用的时候，无法转化为JSON，需要Feign接口添加配置：@Headers("Content-Type: application/json")
        // return new SpringFormEncoder(new SpringEncoder(() -> new HttpMessageConverters()));
        return new SpringEncoder(() -> new HttpMessageConverters(
                new MappingJackson2HttpMessageConverter()
        ));
    }
/*    @Bean
    public Decoder customDecoder() {
        return new InputStreamResourceDecoder();
    }*/
}
