package com.bamboocloud.cdp.util.sdk.common.util;

import lombok.experimental.UtilityClass;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2024/7/31
 * @description
 */
@UtilityClass
public class MultipartFileConverter {

    public static MultipartFile convert(InputStream inputStream, String tempDirPath) throws IOException {
        return new MockMultipartFile(tempDirPath, inputStream);
    }

}
