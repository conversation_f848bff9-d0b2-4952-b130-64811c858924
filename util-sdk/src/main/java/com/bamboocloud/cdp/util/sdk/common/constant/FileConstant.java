/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: FileConstant.java
 * @createdDate: 2022/12/10 15:50:10
 *
 */

package com.bamboocloud.cdp.util.sdk.common.constant;


import com.bamboocloud.cdp.framework.core.util.FwkIdUtil;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 定义文件存储于OSS的ObjectName
 */
@Component
public class FileConstant {

    public static final String OSS_RESOURCE_PREFIX = "acs:oss:*:*:";
    public static final String OBS_RESOURCE_PREFIX = "obs:*:*:object:";
    public static final String PUBLIC_PATH = "/pub/";
    public static final String PRIVATE_PATH = "/pvt/";
    public static final String TEMP_PATH = "/temp/";
    public static final String FILES_TEMP_PATH = "files/temp/";
    public static final String FILES_CERT_PRIVATE_KEY_FILE = "files/certPrivateKey/certPrivateKey.txt";
    public static final String FILES_CERT_FILE = "files/cert/cert.txt";
    public static final String POINT = ".";
    public static final String SLASH = "/";
    public static final String TEMP_PATH_PREFIX    = "temp/";

    public static final String PDF_SUFFIX = ".pdf";
    public static final int TEN_KILO_BYTE = 10240;

    public static final String cacheKeyPvtObsAuthSts         = "pvt_obs_auth_sts_file";
    public static final String cacheKeyReadOnlyObsAuthSts    = "read_only_obs_auth_sts_file";
    public static final String cacheKeyReadOnlyAllObsAuthSts = "read_only_obs_auth_sts_all_file";

    public static final String GEO_PREFIX                = "geo";
    public static final String BANK_PREFIX               = "bank";
    public static final String PRODUCT_CATEGORY_PREFIX   = "productCategory";
    public static final String TRADE_CONFIG_PREFIX       = "tradeConfig";
    public static final String LUCKYBOX_AR_TARGET_PREFIX = "luckyBoxArTarget";
    public static final String COMMON                    = "common";

    // todo 未来应该删除fileFormat，在framecore中，通过前端提交的临时URL获取文件后缀再拼接成最后的fileName，后期使用V2版本

    public String generateUserObjectName(String userId, String fileFormat, boolean isPublic) {
        return isPublic ? userId + "/pub/" + FwkIdUtil.generateUUID() + "." + fileFormat :
            userId + "/pvt/" + FwkIdUtil.generateUUID() + "." + fileFormat;
    }

    public String generateOrganizationObjectName(String organizationId, String fileFormat, boolean isPublic) {
        return isPublic ? organizationId + "/pub/" + FwkIdUtil.generateUUID() :
            organizationId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    public String generateOrganizationReviewHistoryObjectName(String organizationId, String fileName,
        boolean isPublic) {
        return isPublic ? organizationId + "/pub/" + fileName : organizationId + "/pvt/" + fileName;
    }

    public String generateShopWxReviewHistoryObjectName(String shopId, String fileName, boolean isPublic) {
        return isPublic ? shopId + "/pub/" + fileName : shopId + "/pvt/" + fileName;
    }

    public String generateShopObjectName(String shopId, String fileFormat, boolean isPublic) {
        return isPublic ? shopId + "/pub/" + FwkIdUtil.generateUUID() : shopId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 客服文件OSS地址
     *
     * @param senderUserId
     * @param receiverUserId
     * @param isPublic
     * @return
     */
    public String generateChatObjectName(String senderUserId, String receiverUserId, boolean isPublic) {
        return isPublic ? senderUserId + "/pub/chat/" + receiverUserId + "/" + FwkIdUtil.generateUUID() :
            senderUserId + "/pvt/chat/" + receiverUserId + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 以下为V2版本
     */


    /**
     * 用户数据文件OSS地址
     *
     * @param userId
     * @param isPublic
     * @return
     */
    public String generateUserObjectNameV2(String userId, boolean isPublic) {
        return isPublic ? userId + "/pub/" + FwkIdUtil.generateUUID() : userId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 用户数据文件OSS地址 -指定文件 名称
     *
     * @param userId
     * @param isPublic
     * @return
     */
    public String generateUserObjectNameV2(String userId, String fileName, boolean isPublic) {
        return isPublic ? userId + "/pub/" + fileName : userId + "/pvt/" + fileName;
    }

    /**
     * 组织机构数据文件OSS地址
     *
     * @param organizationId
     * @param isPublic
     * @return
     */
    public String generateOrganizationObjectNameV2(String organizationId, boolean isPublic) {
        return isPublic ? organizationId + "/pub/" + FwkIdUtil.generateUUID() :
            organizationId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 商铺数据文件OSS地址
     *
     * @param shopId
     * @param isPublic
     * @return
     */
    public String generateShopObjectNameV2(String shopId, boolean isPublic) {
        return isPublic ? shopId + "/pub/" + FwkIdUtil.generateUUID() : shopId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 商品数据文件OSS地址
     *
     * @param shopId
     * @param productId
     * @return
     */
    public String generateShopProductObjectNameV2(String shopId, String productId, boolean isPublic) {
        return isPublic ? shopId + "/pub/product/" + productId + "/" + FwkIdUtil.generateUUID() : shopId + "/pvt/product" +
            "/" + productId + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 退换单文件OSS地址
     *
     * @param tradeId
     * @param tradeAfterSaleId
     * @param isPublic
     * @return
     */
    public String generateTradeAfterSaleObjectNameV2(String tradeId, String tradeAfterSaleId, boolean isPublic) {
        return isPublic ? tradeId + "/pub/tradeAfterSale/" + tradeAfterSaleId + "/" + FwkIdUtil.generateUUID() : tradeId + "/pvt/tradeAfterSale" +
            "/" + tradeAfterSaleId + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 订单商品OSS地址
     *
     * @param tradeId
     * @param productId
     * @param isPublic
     * @return
     */
    public String generateTradeProductObjectNameV2(String tradeId, String productId, boolean isPublic) {
        return isPublic ? tradeId + "/pub/tradeProduct/" + productId + "/" + FwkIdUtil.generateUUID() : tradeId + "/pvt/tradeProduct" +
            "/" + productId + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 工单文件OSS地址
     *
     * @param isPublic
     * @param userId
     * @return
     */
    public String generateTicketObjectNameV2(String userId, String ticketId, boolean isPublic) {
        return isPublic ? userId + "/pub/ticket/" + ticketId + "/" + FwkIdUtil.generateUUID() :
            userId + "/pvt/ticket/" + ticketId + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 分账单文件OSS地址
     *
     * @param isPublic
     * @param tradeProfitSharingId
     * @return
     */
    public String generateTradeProfitSharingObjectNameV2(String tradeProfitSharingId, boolean isPublic) {
        return isPublic ? tradeProfitSharingId + "/pub/" + FwkIdUtil.generateUUID() : tradeProfitSharingId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 公共图片文件OSS地址
     *
     * @param type
     * @return
     */
    public String generateCommonObjectNameV2(String type) {
        return COMMON + PUBLIC_PATH + type + "/" + FwkIdUtil.generateUUID();
    }

    /**
     * 临时文件OSS地址
     *
     * @return
     */
    public String generateTempObjectNameV2() {
        return "temp/" + FwkIdUtil.generateUUID();
    }

    /**
     * 省市区文件
     *
     * @return
     */
    public String generateGeoData() {
        return GEO_PREFIX + PUBLIC_PATH;
    }

    /**
     * 银行含支行文件
     *
     * @return
     */
    public String generateBankData() {
        return BANK_PREFIX + PUBLIC_PATH;
    }

    /**
     * 推荐内容
     *
     * @return
     */
    public String generateContentObjectNameV2() {
        return COMMON + "/pub/MediaContent/" + FwkIdUtil.generateUUID();

    }

    /**
     * 推文为富文本，文本含有图片，图片上传到OSS。
     *
     * @return
     */
    public String generateCommonPubModuleContentObjectNameV2(String moduleCode) {
        return COMMON + "/pub/" + moduleCode + "/" + FwkIdUtil.generateUUID();

    }

    /**
     * 爱好/内容分类 logo url
     *
     * @return
     */
    public String generateHobbyCategoryObjectNameV2() {
        return COMMON + "/pub/hobby/Category/" + FwkIdUtil.generateUUID();
    }

    /**
     * 商城数据文件OSS地址
     *
     * @param mamaUserId
     * @param isPublic
     * @return
     */
    public String generateShopMallObjectNameV2(String mamaUserId, boolean isPublic) {
        return isPublic ? mamaUserId + "/pub/" + FwkIdUtil.generateUUID() : mamaUserId + "/pvt/" + FwkIdUtil.generateUUID();
    }

    /**
     * 景点的图片或者视频
     *
     * @return
     */
    public String generateSpotObjectNameV2() {
        return COMMON + "/pub/spot/" + FwkIdUtil.generateUUID();
    }

    /**
     * 路线的图标地址
     *
     * @return
     */
    public String generateThemeRouteIconObjectNameV2() {
        return COMMON + "/pub/themeRoute/icon/" + FwkIdUtil.generateUUID();
    }

    /**
     * 推广活动的图片或者视频
     *
     * @return
     */
    public String generatePromotionObjectNameV1() {
        return COMMON + "/pub/Promotion/" + FwkIdUtil.generateUUID();
    }


    /**
     * 广告轮播的图片
     *
     * @return
     */
    public String generateDispSlideLinkThumbnailObjectNameV1() {
        return COMMON + "/pub/disp/slide/" + FwkIdUtil.generateUUID();
    }

    /**
     * 广告轮播的图片金刚区
     *
     * @return
     */
    public String generateDispKingKongLinkThumbnailObjectNameV1() {
        return COMMON + "/pub/disp/kingkong/" + FwkIdUtil.generateUUID();
    }

    /**
     * 商家/平台端宝箱识别图oss地址
     *
     * @param loginId
     * @param luckyBoxId
     * @param isPublic
     * @return
     */
    public String generateLuckyBoxObjectNameV1(String loginId, Long luckyBoxId, boolean isPublic) {
        return isPublic ? loginId + "/pub/" + LUCKYBOX_AR_TARGET_PREFIX + "/" + luckyBoxId + FwkIdUtil.generateUUID() :
            loginId + "/pvt/" + LUCKYBOX_AR_TARGET_PREFIX + "/" + luckyBoxId + FwkIdUtil.generateUUID();
    }

}
