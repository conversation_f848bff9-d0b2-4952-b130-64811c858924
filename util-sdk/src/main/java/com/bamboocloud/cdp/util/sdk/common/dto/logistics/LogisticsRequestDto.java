/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: LogisticsRequestDto.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.util.sdk.common.dto.logistics;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsRequestDto implements Serializable {

    /**
     * 订单编号
     */
    private String orderCode;
    /**
     * 快递公司编码
     */
    private String shipperCode;
    /**
     * 物流单号
     */
    private String logisticCode;

    /**
     * ShipperCode 为 SF 时必填，对应寄件人/
     * 收件人手机号后四位；
     * ShipperCode 为其他快递时，可不填或保
     * 留字段，不可传值
     */
    private String customerName;
}
