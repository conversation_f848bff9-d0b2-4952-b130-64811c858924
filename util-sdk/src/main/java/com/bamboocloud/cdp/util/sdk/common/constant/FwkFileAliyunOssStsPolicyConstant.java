package com.bamboocloud.cdp.util.sdk.common.constant;

import com.bamboocloud.cdp.framework.core.domain.model.basic.FwkPair;


/**
 * <AUTHOR> <PERSON>
 * @description:
 */
public class FwkFileAliyunOssStsPolicyConstant {

    public static final FwkPair ACTION_GET_OBJECT     = new FwkPair("oss:GetObject", "GetObject");
    public static final FwkPair ACTION_PUT_OBJECT     = new FwkPair("oss:PutObject", "PutObject");
    public static final FwkPair ACTION_LIST_OBJECTS   = new FwkPair("oss:ListObjects", "ListObjects");
    public static final FwkPair ACTION_DELETE_OBJECTS = new FwkPair("oss:DeleteObject", "DeleteObject");

    public static final String VERSION          = "1";
    public static final String STATEMENT_EFFECT = "Allow";
}
