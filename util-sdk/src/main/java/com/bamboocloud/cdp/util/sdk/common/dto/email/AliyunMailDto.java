package com.bamboocloud.cdp.util.sdk.common.dto.email;

import jakarta.mail.BodyPart;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/1
 * @description
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AliyunMailDto {

    String sendAddress;
    String sendPassword;
    String host;
    String port;
    String subject;

    String         content;
    String[]       to;
    List<BodyPart> attachParts;
    String         CC;
}
