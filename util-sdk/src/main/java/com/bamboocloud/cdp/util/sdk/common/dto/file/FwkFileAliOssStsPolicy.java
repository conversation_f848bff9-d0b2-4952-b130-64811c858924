package com.bamboocloud.cdp.util.sdk.common.dto.file;

import com.alibaba.fastjson.annotation.JSONField;
import com.bamboocloud.cdp.util.sdk.common.constant.FwkFileAliyunOssStsPolicyConstant;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> Mo
 * @description: 调用阿里云STS获取临时访问OSS凭证的请求参数
 */
@Data
public class FwkFileAliOssStsPolicy extends FwkFileBasePolicy {

    @JSONField(name = "Version")
    private String version = FwkFileAliyunOssStsPolicyConstant.VERSION;

    @JSONField(name = "Statement")
    private List<Statement> statement;

    @Data
    public static class Statement {

        @JSONField(name = "Effect")
        private String effect = FwkFileAliyunOssStsPolicyConstant.STATEMENT_EFFECT;

        @JSONField(name = "Action")
        private List<String> action;

        @JSONField(name = "Resource")
        private List<String> resource;
    }

}
