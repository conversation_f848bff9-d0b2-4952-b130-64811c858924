package com.bamboocloud.cdp.util.sdk.integration;

import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import feign.Response;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

@Slf4j
public class InputStreamResourceDecoder implements Decoder {

@Override
    public Object decode(Response response, Type type) throws IOException {

    // 判断是否是 ResponseEntity<InputStreamResource>
    if (type instanceof ParameterizedType) {
        ParameterizedType parameterizedType = (ParameterizedType) type;
        if (parameterizedType.getRawType() instanceof Class<?>) {
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            if (rawType.equals(ResponseEntity.class)) {
                Type actualType = parameterizedType.getActualTypeArguments()[0];
                if (actualType instanceof Class<?> && InputStreamResource.class.equals(actualType)) {
                    log.info("使用解码器1");
                    return ResponseEntity.ok(new InputStreamResource(response.body().asInputStream()));
                }
            }
        }
    }
    // 判断是否是 FwkApiResponse<ResponseEntity<InputStreamResource>>
    if (type instanceof ParameterizedType) {
        ParameterizedType parameterizedType = (ParameterizedType) type;
        if (parameterizedType.getRawType() instanceof Class<?>) {
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            if (rawType.equals(FwkApiResponse.class)) {
                Type actualType = parameterizedType.getActualTypeArguments()[0];
                if (actualType instanceof ParameterizedType) {
                    ParameterizedType actualParameterizedType = (ParameterizedType) actualType;
                    if (actualParameterizedType.getRawType() instanceof Class<?>) {
                        Class<?> actualRawType = (Class<?>) actualParameterizedType.getRawType();
                        if (actualRawType.equals(ResponseEntity.class)) {
                            Type actualActualType = actualParameterizedType.getActualTypeArguments()[0];
                            if (actualActualType instanceof Class<?> && InputStreamResource.class.equals(actualActualType)) {
                                log.info("使用解码器3");
                                return ResponseEntity.ok(new InputStreamResource(response.body().asInputStream()));
                            }
                        }
                    }
                }
            }
        }
    }
    // 判断是否是 FwkApiResponse<byte[]>
    if (type instanceof ParameterizedType) {
        ParameterizedType parameterizedType = (ParameterizedType) type;
        if (parameterizedType.getRawType() instanceof Class<?>) {
            Class<?> rawType = (Class<?>) parameterizedType.getRawType();
            if (rawType.equals(FwkApiResponse.class)) {
                Type actualType = parameterizedType.getActualTypeArguments()[0];
                if (actualType instanceof Class<?> && byte[].class.equals(actualType)) {
                    log.info("使用解码器4");
                    return response.body().asInputStream().readAllBytes();
                }
            }
        }
    }
   if (InputStreamResource.class.equals(type)){
       // 返回InputStreamResource
       log.info("使用解码器2");
       return new InputStreamResource(response.body().asInputStream());
   }
   // 否则返回默认的解码器
   return new feign.codec.StringDecoder().decode(response, type);
}
}
