package com.bamboocloud.cdp.util.sdk.common.dto.file;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/29
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileParam {

    private String fileUrl;

    private String tempFileUrl;

    /**
     * 是否从缓存中取auth
     */
    private String cacheKey;

    private String userId;

    FwkCommonPolicy policy;

    public FileParam(String cacheKey) {
        this.cacheKey = cacheKey;
    }

    public FileParam(String cacheKey, String fileUrl) {
        this.cacheKey = cacheKey;
        this.fileUrl = fileUrl;
    }

    public FileParam(String cacheKey, String fileUrl, String tempFileUrl) {
        this.cacheKey = cacheKey;
        this.fileUrl = fileUrl;
        this.tempFileUrl = tempFileUrl;
    }

    public FileParam(FwkCommonPolicy policy) {
        this.policy = policy;
    }

    public FileParam(FwkCommonPolicy policy, String fileUrl) {
        this.policy = policy;
        this.fileUrl = fileUrl;
    }

    public FileParam(FwkCommonPolicy policy, String fileUrl, String tempFileUrl) {
        this.policy = policy;
        this.fileUrl = fileUrl;
        this.tempFileUrl = tempFileUrl;
    }
}
