package com.bamboocloud.cdp.util.sdk.common.vo.douyin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DouYinCertificateVerifyVo implements Serializable {

    /**
     * 一次验券的标识 (用于短时间内的幂等)
     * 针对三方码订单，每次请求都需要保证幂等！（请求维度，非券码/订单维度）
     *
     */
    private String verify_token;

    /**
     * 核销的抖音门店id
     * 如何获得抖音门店id，可以参考：门店关联及匹配能力
     */
    private String poi_id;

    /**
     * 验券准备接口返回的加密抖音券码
     */
    private List<String> encrypted_codes;


}
