/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: IntegrationLocationGetResultDto.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.util.sdk.common.dto.location;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> Mo
 */
@Data
public class IntegrationLocationGetResultDto implements Serializable {
    /**
     * 状态码
     */
    private String status;

    /**
     * 状态说明
     */
    private String message;

    /**
     * 逆地址解析结果
     */
    private Result result;

    @Data
    public static class Result {

        /**
         * 地址描述
         */
        private String address;


        /**
         * 地址部件，address不满足需求时可自行拼接
         */
        private AddressComponent address_component;

        @Data
        public static class AddressComponent {

            /**
             * 国家
             */
            private String nation;

            /**
             * 省
             */
            private String province;

            /**
             * 市
             */
            private String city;

            /**
             * 区
             */
            private String district;

            /**
             * 街道
             */
            private String street;


            /**
             * 门牌
             */
            private String street_number;
        }

        private AdInfo ad_info;

        @Data
        public static class AdInfo {

            /**
             * 行政区划代码
             */
            private String nation_code;

            /**
             *
             */
            private String adcode;

            /**
             *
             */
            private String city_code;

            /**
             * 行政区划名称
             */
            private String name;
        }
    }

}
