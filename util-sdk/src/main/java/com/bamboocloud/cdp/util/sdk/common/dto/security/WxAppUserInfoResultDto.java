/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: WxAppUserInfoResultDto.java
 * @createdDate: 2022/12/10 15:50:10
 *
 */

package com.bamboocloud.cdp.util.sdk.common.dto.security;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 */
@Data
public class WxAppUserInfoResultDto implements Serializable {

    /**
     * openid	普通用户的标识，对当前开发者帐号唯一
     */
    private String openid;

    /**
     * nickname	普通用户昵称
     */
    private String nickname;

    /**
     * sex	普通用户性别，1为男性，2为女性
     */
    private Integer sex;

    /**
     * city	普通用户个人资料填写的城市
     */
    private String city;

    /**
     * province	普通用户个人资料填写的省份
     */
    private String province;

    /**
     * country	国家，如中国为CN
     */
    private String country;

    /**
     * headimgurl	用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），
     * 用户没有头像时该项为空
     */

    private String headImgUrl;
    /**
     * unionid	用户统一标识。针对一个微信开放平台帐号下的应用，同一用户的unionid是唯一的。
     */
    private String unionId;

    /**
     * privilege	用户特权信息，json数组，如微信沃卡用户为（chinaunicom）
     */
    private String[] privileges;

    /**
     * is_snapshotuser  是否为快照页模式虚拟账号，值为0时是普通用户，1时是虚拟帐号
     */
    private Integer snapshotUser;

}
