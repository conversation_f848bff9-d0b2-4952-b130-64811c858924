/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: WxMiniExtractUserMobileInfoVo.java
 * @createdDate: 2022/12/10 15:50:10
 *
 */

package com.bamboocloud.cdp.util.sdk.common.vo.security;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Data
public class WxMiniExtractUserMobileInfoVo implements Serializable {
    /**
     * 加密数据
     */
    private String encryptedData;

    /**
     * 偏移量
     */
    private String iv;

    /**
     * 数字签名
     */
    private String signature;

    /**
     * SessionKey
     */
    private String sessionKey;
}
