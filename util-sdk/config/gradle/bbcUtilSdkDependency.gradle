dependencies {
    configurations.all {
        exclude group: "org.mongodb", module: "mongo-java-driver"
    }
    implementation("com.github.binarywang:weixin-java-miniapp:${binarywangVersion}")
    implementation("com.aliyun:push20160801:${aliyunPushVersion}")
    implementation "com.huaweicloud:esdk-obs-java-bundle:3.23.9"
    implementation "com.huaweicloud.sdk:huaweicloud-sdk-bundle:3.1.62"
    implementation "org.springframework:spring-test:6.1.2"
    implementation "org.eclipse.angus:jakarta.mail:2.0.2"
}

shadowJar{
    classifier = ''

    include "**/**/*.class"

    includeEmptyDirs = false
}
