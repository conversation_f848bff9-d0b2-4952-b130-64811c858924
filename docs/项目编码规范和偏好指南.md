# Java Spring Boot 编码规范指南

## 文档概述

本文档定义了Java Spring Boot项目的编码规范、技术偏好和最佳实践，为团队开发提供统一的标准和指导。规范具有通用性，适用于各类Spring Boot项目。

**规范优先级：**
1. **本文档规范**：项目特定的编码规范和技术偏好
2. **阿里巴巴Java开发手册**：对于本文档未覆盖的部分，严格遵循《阿里巴巴Java开发手册》
3. **IDE默认规范**：使用IDE的默认代码格式化规则

**重要说明：** 所有代码必须严格按照上述优先级执行，确保代码质量和团队协作效率。

## 项目开发流程规范

### 1. 模块操作规范
- **基于指定模块操作**：开发时需要基于用户指定的模块进行操作，不能随意切换模块
- **跨模块需求判断**：根据项目结构判断是否需要跨模块操作，如依赖关系、SDK引用等
- **模块边界清晰**：明确各模块的职责边界，避免功能错位

### 2. 构建工具规范
- **使用Gradle构建**：项目使用gradle作为构建工具，本地已有gradle环境
- **直接使用gradle命令**：可以直接使用gradle命令进行编译、测试、打包等操作
- **编译验证必须**：所有代码完成后必须确保编译通过，使用`gradle compileJava`验证

### 3. SDK发布规范
- **SDK修改后发布**：如果修改了xxx-sdk模块的内容，需要发布到本地私服
- **发布命令**：在对应的sdk目录下执行`gradle publishToMavenLocal -x :test`
- **依赖更新**：发布后其他模块可以引用最新的SDK版本

### 4. 开发环境要求
- 使用IntelliJ IDEA作为开发IDE
- 确保项目能够正常编译和运行
- 遵循现有的代码结构和命名规范

## 查询优化规范

### 1. 数据库查询优化
- **避免多次查询**：不要先查询实体再关联查询，应使用JOIN一次性获取数据
- **按需查询字段**：使用QueryDSL的Projections.bean()直接查询需要的字段，避免查询完整实体
- **减少内存操作**：避免在内存中进行复杂的数据组装和转换

### 2. 枚举使用规范
- **Switch语句必须有default**：所有switch语句都必须包含default分支
- **枚举转换错误处理**：使用`Optional.ofNullable().orElseThrow()`处理枚举转换，给定无效值时直接抛出BusinessException
- **枚举驱动查询**：使用枚举类型驱动动态查询条件构建，提高代码可维护性

### 3. 搜索条件设计规范
- **类型切换模式**：将多个独立搜索字段合并为搜索类型+关键词模式
- **时间范围统一**：将多个时间字段合并为时间类型+时间范围模式
- **用户体验优先**：减少前端输入字段数量，提供更简洁的搜索界面

**示例：**
```java
// 优化前：多个独立字段
private String guestName;
private String guestMobile;
private String orderId;

// 优化后：类型切换模式
private String searchType; // GUEST_NAME/GUEST_MOBILE/ORDER_ID
private String keyword;
```

## 核心编码规范

### 1. 依赖注入规范

**✅ 推荐做法**
```java
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/example")
@Validated
@Slf4j
public class ExampleController extends BaseController {

    private final ExampleService exampleService;
    private final ExampleMapper exampleMapper;
}
```

**❌ 避免使用**
```java
public class BadController {
    @Autowired
    private SomeService someService;  // 不推荐使用@Autowired
}
```

**规范要点：**
- 新创建的代码必须 使用 `@RequiredArgsConstructor` + `final` 字段替代 `@Autowired` 已有代码按照现有格式
- 构造器注入比字段注入更安全，支持不可变对象
- 便于单元测试和依赖管理

### 2. 控制器设计规范

**继承关系**
```java
// 根据业务需要继承相应的基类
public class UserController extends BaseUserController {
    // 可以直接调用基类提供的通用方法
}

public class AdminController extends BaseAdminController {
    // 管理端特定逻辑
}
```

**接口参数设计**
```java
/**
 * 查询数据列表
 *
 * 根据查询条件返回分页数据列表
 *
 * @param queryReq 查询请求参数
 * @return 数据列表
 */
@PostMapping("/list")
public ApiResponse<List<DataDto.ListRes>> queryDataList(
        @Validated @RequestBody DataDto.QueryReq queryReq) {

    // 获取当前用户信息（如需要）
    UserInfo currentUser = getCurrentUser();

    // 业务逻辑...
}
```

**规范要点：**
- 单参数使用 `@PathVariable`，多参数使用 `@RequestBody` + `@Validated`
- 接口自动获取当前登录用户信息，提升安全性
- 使用JavaDoc而非Swagger注解进行接口文档

### 3. DTO设计规范

**包结构**
```
com.company.project.common.dto/
├── user/                      # 用户端DTO
│   ├── order/
│   │   └── UserOrderDto.java
│   └── profile/
│       └── UserProfileDto.java
└── admin/                     # 管理端DTO
    └── system/
        └── AdminSystemDto.java
```

**DTO结构设计**
```java
/**
 * 数据查询DTO
 *
 * <AUTHOR>
 * @date ${currentDateTime}
 */
public interface DataDto {

    @Data
    class QueryReq implements Serializable {
        @NotNull(message = "开始日期不能为空")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        private LocalDate startDate;

        @NotNull(message = "结束日期不能为空")
        @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
        private LocalDate endDate;

        private String dimension = "DAY";
    }

    @Data
    class ListRes implements Serializable {
        private LocalDate dataDate;
        private String id;
        private String name;
        // ... 业务字段
    }
}
```

**规范要点：**
- DTO定义在 `common.dto` 包下，使用接口+内部类形式减少文件创建
- **每个接口使用专用的请求和响应DTO**，避免使用宽泛的类,确保JavaDoc文档无歧义
- 请求类使用 `Req` 结尾，返回类使用 `Res` 结尾
- 如果参数完全相同可以复用
- 使用 `@Validated` 进行参数校验，添加合适的校验注解

### 4. 对象映射规范

**✅ 推荐：使用MapStruct**
```java
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DataMapper {

    /**
     * 请求DTO转换为查询DTO
     */
    @Mapping(target = "userIds", expression = "java(java.util.Collections.singletonList(userId))")
    @Mapping(target = "queryType", ignore = true)
    QueryDto toQueryDto(String userId, DataDto.QueryReq queryReq);

    /**
     * 实体转换为响应DTO
     * 字段名相同时不需要配置@Mapping，保持代码简洁
     */
    DataDto.ListRes toListRes(DataEntity entity);

    List<DataDto.ListRes> toListResList(List<DataEntity> entityList);
}
```



**规范要点：**
- 完全使用MapStruct替代手动DTO转换，不需要考虑旧DTO兼容性
- MapStruct字段相同时不用配置映射，保持代码简洁
- 配置 `componentModel = "spring"` 使映射器成为Spring组件
- Service层直接注入和使用MapStruct映射器，无需中间转换层

## 技术偏好

### 1. 数据访问层

**✅ 优先使用MyBatis Plus**
```java
@RequiredArgsConstructor
@Repository
public class DataRepositoryImpl implements DataRepository {

    private final DataMapper dataMapper;

    @Override
    public List<DataEntity> queryDataList(QueryDto queryDto) {
        LambdaQueryWrapper<DataEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(DataEntity::getUserId, queryDto.getUserIds())
               .between(DataEntity::getCreateTime, queryDto.getStartDate(), queryDto.getEndDate())
               .eq(DataEntity::getStatus, StatusEnum.ACTIVE.getCode());

        return dataMapper.selectList(wrapper);
    }
}
```

#### 1.1 批量操作规范

**✅ 优先使用批量操作替代循环单条操作**
```java
// ✅ 推荐：使用批量操作
@Transactional
public int batchUpsertBookingStatistics(LocalDate date, List<BookingStatisticsDto> batch) {
    if (batch.isEmpty()) return 0;

    String sql = "INSERT INTO t_hotel_order_daily_statistics " +
                 "(shop_id, statistics_date, booking_order_count, created_user_id, created_date) " +
                 "VALUES (?, ?, ?, 'SYSTEM', NOW()) " +
                 "ON DUPLICATE KEY UPDATE " +
                 "booking_order_count = VALUES(booking_order_count), " +
                 "updated_user_id = 'SYSTEM', updated_date = NOW()";

    int[] results = jdbcTemplate.batchUpdate(sql, batch, batch.size(),
        (ps, booking) -> {
            ps.setString(1, booking.getShopId());
            ps.setDate(2, Date.valueOf(date));
            ps.setInt(3, booking.getOrderCount().intValue());
        });

    return Arrays.stream(results).sum();
}

// ❌ 避免：循环单条操作
@Transactional
public void processBookingStatistics(LocalDate date, List<BookingStatisticsDto> batch) {
    for (BookingStatisticsDto result : batch) {  // 性能差
        repository.upsertBookingStatistics(result.getShopId(), date, ...);
    }
}
```
**批量操作规范要点：**
- 当需要处理大量数据时，优先考虑批量操作而非循环单条操作
- 批量操作应返回影响行数，便于性能监控和问题排查
- 合理控制批量大小，避免内存溢出（建议每批500-1000条）

### 2. 时间格式规范

**✅ 优先使用 yyyy-MM-dd HH:mm:ss 格式**
```java
@Data
class QueryReq implements Serializable {
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
```

**规范要点：**
- 项目统一使用 `yyyy-MM-dd HH:mm:ss` 时间格式
- 使用 `LocalDateTime` 类型而非 `LocalDate`
- 同时配置 `@DateTimeFormat` 和 `@JsonFormat` 注解
- 确保前后端时间格式的一致性

### 3. 常量定义

**✅ 优先使用Lombok枚举替代常量类**
```java
@Getter
@AllArgsConstructor
public enum StatusEnum {

    /**
     * 激活状态
     */
    ACTIVE("ACTIVE", "激活"),

    /**
     * 禁用状态
     */
    DISABLED("DISABLED", "禁用"),

    /**
     * 删除状态
     */
    DELETED("DELETED", "删除");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据代码获取枚举值
     */
    public static StatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(StatusEnum.values())
                .filter(status -> status.code.equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查给定的代码是否为有效状态
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
```

**枚举设计规范：**
- 使用 `@Getter` + `@AllArgsConstructor` 注解
- 提供 `getByCode(String code)` 和 `isValidCode(String code)` 静态方法
- 字段使用 `final` 修饰，包含 `code` 和 `description`
- 保持项目内枚举的一致性

**在Service中使用枚举替代魔法值**
```java
// ✅ 推荐：使用枚举
queryDto.setStatus(StatusEnum.ACTIVE.getCode());
queryDto.setType(TypeEnum.NORMAL.getCode());

// ❌ 避免：直接使用字符串常量
queryDto.setStatus("ACTIVE");  // 魔法值
queryDto.setType("NORMAL");    // 魔法值
```

**优先使用现有枚举，避免重复定义**
```java
// ✅ 推荐：使用现有枚举
StatisticsDimensionEnum dimensionEnum = StatisticsDimensionEnum.getByCode(dimension);
switch (dimensionEnum) {
    case DAY:
        // 处理逻辑
        break;
    case WEEK:
        // 处理逻辑
        break;
    default:
        // 默认处理
        break;
}

// ❌ 避免：重复定义常量
String DIMENSION_DAY = "DAY";  // 项目中已有StatisticsDimensionEnum.DAY
String DIMENSION_WEEK = "WEEK";  // 重复定义
```

**规范要点：**
- 优先使用项目中已定义的枚举
- 避免在不同类中重复定义相同的常量
- 使用枚举的getByCode()方法进行字符串转换
- switch语句使用枚举值而非字符串比较

**使用专用类替代Map返回复杂数据**
```java
// ✅ 推荐：使用专用的数据类
public PeriodDateRange calculatePeriodDates(StatisticsDimensionEnum dimension) {
    PeriodDateRange result = new PeriodDateRange();
    result.setCurrentStartDate(startDate);
    result.setCurrentEndDate(endDate);
    return result;
}

// ❌ 避免：使用Map返回复杂数据
public Map<String, LocalDate> calculatePeriodDates(String dimension) {
    Map<String, LocalDate> result = new HashMap<>();
    result.put("currentStart", startDate);  // 魔法字符串
    result.put("currentEnd", endDate);      // 类型不安全
    return result;
}
```

**使用项目自定义异常**
```java
// ✅ 推荐：使用BusinessException
if (dimensionEnum == null) {
    throw new BusinessException("INVALID_DIMENSION", "无效的统计维度: " + dimension);
}

// ❌ 避免：使用通用异常
if (dimensionEnum == null) {
    throw new IllegalArgumentException("无效的统计维度");  // 缺少错误码
}
```

**参数验证规范：**
- 方法入口处验证关键参数
- 使用BusinessException抛出业务异常
- 提供明确的错误码和错误信息
- 优先使用类型安全的参数类型

### 3. 接口文档

#### 3.1 接口类注释规范

**✅ 推荐格式：端类型/模块名/功能描述**
```java
/**
 * 用户端/api/订单管理
 *
 * <AUTHOR>
 * @date ${currentDateTime}
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/order")
@Slf4j
public class OrderController extends BaseController {
    // 实现逻辑...
}
```

**其他示例：**
```java
/**
 * 管理端/api/用户管理
 */
public class AdminUserController extends BaseAdminController {
    // 实现逻辑...
}

/**
 * 用户端/api/数据统计
 */
public class UserDataController extends BaseUserController {
    // 实现逻辑...
}

/**
 * 公共端/api/系统配置
 */
public class SystemConfigController extends BaseController {
    // 实现逻辑...
}
```

**❌ 避免使用技术术语**
```java
/**
 * 订单控制器           // ❌ 不要使用"控制器"
 * 用户管理Controller    // ❌ 不要使用"Controller"
 * UserController      // ❌ 不要直接使用类名
 */
```

**规范要点：**
- **端类型**：明确标识接口所属的用户端（用户端、管理端、公共端等）
- **模块名**：标识所属的业务模块或系统模块（api、service等）
- **功能描述**：用业务语言描述接口的核心功能，避免技术术语
- **格式统一**：使用斜杠"/"分隔，保持格式一致性

#### 3.2 接口方法注释规范

- 使用JavaDoc而非Swagger注解进行接口文档
- 方法注释包含功能描述、参数说明和返回值说明
- 避免使用 `@ApiOperation`、`@ApiParam` 等Swagger注解

## 架构设计原则

### 1. 分层架构

```
Controller层 -> Service层 -> Repository层
     ↓           ↓            ↓
   接口适配    业务逻辑      数据访问
```

### 2. 职责分离

- **公共模块**：通用的业务逻辑和数据访问
- **角色模块**：特定角色的接口适配和权限控制
- **DTO转换**：使用MapStruct进行高效转换

### 3. 扩展性设计

- 新增统计维度：在查询DTO中添加参数
- 新增统计指标：在结果DTO中添加字段
- 新增用户角色：创建新的角色特定模块

## 代码质量要求

### 1. 命名规范

- **类名**：使用PascalCase，如 `UserController`
- **方法名**：使用camelCase，如 `queryDataList`
- **常量**：使用UPPER_SNAKE_CASE，如 `DEFAULT_PAGE_SIZE`
- **包名**：使用小写，如 `com.company.project.user.controller`

### 2. 注释规范

- **类和接口注释**：必须有JavaDoc注释，包含功能描述、作者和创建时间
- **方法注释**：公共方法必须有JavaDoc注释，说明参数、返回值和功能
- **私有方法注释**：重要的私有方法也应添加注释说明，包含@param和@return标注
- **行内注释**：复杂业务逻辑添加行内注释
- **时间规范**：类注释中的@date字段必须使用当前互联网真实时间，格式为yyyy-MM-dd HH:mm:ss，而不是硬编码的时间

#### 2.1 方法注释详细规范

**✅ 推荐的方法注释格式**
```java
/**
 * 批量处理预定统计数据（使用真正的批量UPSERT）
 *
 * @param date 统计日期
 * @param batch 预定统计数据批次
 * @return 影响的行数
 */
@Transactional
public int processBatchBookingStatistics(LocalDate date, List<BookingStatisticsDto> batch) {
    // 实现逻辑
}

/**
 * 分批处理预定统计数据
 *
 * @param start 开始时间
 * @param end 结束时间
 * @param date 统计日期
 */
private void processBookingStatisticsBatch(LocalDateTime start, LocalDateTime end, LocalDate date) {
    // 实现逻辑
}
```

**规范要点：**
- 所有公共方法必须包含完整的JavaDoc注释
- 重要的私有方法也应添加注释说明
- 使用@param标注所有参数，包含参数含义
- 使用@return标注返回值（void方法除外）
- 注释应说明方法的核心功能和业务含义

**正确的时间使用示例：**
```java
/**
 * 用户端/api/数据管理
 *
 * <AUTHOR>
 * @date ${currentDateTime}  // ✅ 使用当前真实时间，格式：yyyy-MM-dd HH:mm:ss
 */
public class DataController extends BaseController {
    // 实现逻辑...
}
```

**❌ 错误的时间使用：**
```java
/**
 * 用户端/api/数据管理
 *
 * <AUTHOR>
 * @date 2025/01/27  // ❌ 不要使用过期或硬编码的时间，且格式不正确
 */
```

### 3. 包导入规范

**✅ 使用import导入，避免完整类名**
```java
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public class ExampleService {
    // ✅ 推荐：使用简短类名
    public Map<String, LocalDate> calculateDates() {
        List<String> items = new ArrayList<>();
        return new HashMap<>();
    }
}
```

**❌ 避免使用完整类名**
```java
public class BadService {
    // ❌ 不推荐：使用完整类名
    public java.util.Map<String, java.time.LocalDate> calculateDates() {
        java.util.List<String> items = new java.util.ArrayList<>();
        return new java.util.HashMap<>();
    }
}
```

**规范要点：**
- 所有外部类都应该通过import导入
- 避免在代码中使用完整的包名.类名格式
- 保持代码简洁易读
- IDE会自动处理import优化

**import检查清单：**
- 使用新类时必须添加对应的import语句
- 编译前检查是否有缺失的import
- 特别注意项目自定义类的import（如BusinessException）
- 使用IDE的自动import功能避免遗漏

**常见遗漏的import：**
```java
// 项目自定义异常
import com.bamboocloud.cdp.gateway.exception.BusinessException;

// 项目自定义枚举
import com.bamboocloud.cdp.sale.everyone.statistics.hotel.enums.StatisticsDimensionEnum;

// 项目自定义DTO
import com.bamboocloud.cdp.sale.everyone.statistics.hotel.dto.PeriodDateRange;
```

### 4. 异常处理

- 项目使用全局异常处理，Controller层无需添加try-catch
- Service层可根据业务需要添加特定异常处理
- 记录详细的错误日志，返回用户友好的错误信息

## 性能优化指导

### 1. 数据库查询优化

- 使用MyBatis Plus的条件构造器
- 避免N+1查询问题
- 合理使用索引和分页

### 2. 对象转换优化

- 使用MapStruct编译时生成转换代码
- 避免反射和手动转换
- 批量转换使用List映射方法

### 3. 缓存策略

- 对历史数据进行缓存
- 使用合理的缓存过期时间
- 考虑使用Redis缓存热点数据

## 测试要求

### 1. 单元测试

- Service层业务逻辑测试
- DTO转换逻辑测试
- 工具类方法测试

### 2. 集成测试

- Controller接口测试
- 数据库集成测试
- 完整调用链路测试

## 总结

本编码规范和偏好指南总结了项目开发中的最佳实践，主要特点：

1. **现代化依赖注入**：使用@RequiredArgsConstructor + final字段
2. **高效对象映射**：完全使用MapStruct，无手动转换
3. **规范化接口设计**：JavaDoc文档 + @RequestBody + @Validated
4. **清晰的架构分层**：Controller -> Service -> Repository
5. **优化的性能表现**：MyBatis Plus + MapStruct + 合理缓存

遵循这些规范可以确保代码质量、可维护性和团队协作效率。

---
*本规范为通用编码规范，适用于Java Spring Boot项目*
*版本：v2.0*
