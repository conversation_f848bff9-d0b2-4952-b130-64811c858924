[[overview]]
= Overview

[[overview-http-verbs]]
== HTTP verbs

RESTful notes tries to adhere as closely as possible to standard HTTP and REST conventions in its
use of HTTP verbs.

|===
| Verb | Usage

| `GET`
| 从服务器取出资源（一项或多项）。

| `POST`
| 在服务器新建一个资源。

| `PUT`
| 在服务器更新资源（客户端提供改变后的完整资源）。

| `PATCH`
| 部分更新, 在服务器更新资源（客户端提供改变的属性）。

| `DELETE`
| 从服务器删除资源。
|===

[[overview-http-status-codes]]
== HTTP status codes

RESTful notes tries to adhere as closely as possible to standard HTTP and REST conventions in its
use of HTTP status codes.

|===
| Status code | Usage

| `200 OK`
| The request completed successfully

| `201 Created`
| A new resource has been created successfully. The resource's URI is available from the response's
`Location` header

| `204 No Content`
| An update to an existing resource has been applied successfully

| `400 Bad Request`
| The request was malformed. The response body will include an error providing further information

| `404 Not Found`
| The requested resource did not exist
|===

[[overview-responses]]
== Response

=== Response headers
include::response/response-headers.adoc[]

=== Response fields
include::response/response-fields.adoc[]

=== Example response
include::response/http-response.adoc[]

