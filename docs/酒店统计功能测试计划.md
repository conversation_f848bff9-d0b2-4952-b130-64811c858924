# 酒店统计功能测试计划

## 测试概述

本文档描述了酒店数据统计功能的完整测试计划，包括单元测试、集成测试、接口测试和性能测试。

## 测试范围

### 1. 功能模块
- 公共统计服务 (everyone.statistics.hotel)
- 商家端统计服务 (vendor.statistics.hotel)
- 运营端统计服务 (mama.statistics.hotel)

### 2. 核心功能
- 预定数据统计
- 离店数据统计
- 综合数据统计
- 汇总数据统计
- 排行榜功能

## 测试策略

### 1. 单元测试 (Unit Tests)

#### 1.1 DTO测试
**测试类**: `HotelStatisticsDtoTest`
```java
// 测试内容：
- 基本属性设置和获取
- 增长率计算逻辑
- 边界值处理（除零、负数等）
- 数据类型转换
```

#### 1.2 Service层测试
**测试类**: `HotelStatisticsServiceTest`
```java
// 测试内容：
- 参数校验逻辑
- 业务逻辑处理
- 异常处理
- Mock Repository调用
```

#### 1.3 Repository层测试
**测试类**: `HotelStatisticsRepositoryTest`
```java
// 测试内容：
- SQL查询逻辑
- 数据映射正确性
- 分页和排序
- 数据库连接异常处理
```

### 2. 集成测试 (Integration Tests)

#### 2.1 服务集成测试
**测试类**: `HotelStatisticsIntegrationTest`
```java
// 测试内容：
- Spring容器Bean注入
- 服务间调用链路
- 事务处理
- 缓存机制
```

#### 2.2 数据库集成测试
**测试类**: `HotelStatisticsDataTest`
```java
// 测试内容：
- 真实数据库查询
- 数据一致性验证
- 并发访问测试
- 数据回滚测试
```

### 3. 接口测试 (API Tests)

#### 3.1 商家端接口测试
**测试类**: `VendorHotelStatisticsControllerTest`

**测试用例**:
```
GET /vendor/statistics/hotel/booking
- 正常查询：返回200和正确数据
- 参数缺失：返回400错误
- 日期格式错误：返回400错误
- 店铺权限验证：返回403错误
- 数据为空：返回200和空列表

GET /vendor/statistics/hotel/checkout
- 正常查询：返回200和正确数据
- 时间范围过大：返回400错误
- 统计维度错误：返回400错误

GET /vendor/statistics/hotel/comprehensive
- 正常查询：返回200和完整数据
- 同比数据验证：增长率计算正确

GET /vendor/statistics/hotel/summary
- 汇总数据正确性验证
- 多维度数据聚合测试
```

#### 3.2 运营端接口测试
**测试类**: `MamaHotelStatisticsControllerTest`

**测试用例**:
```
GET /mama/statistics/hotel/booking
- 全平台数据查询
- 多店铺过滤查询
- 大数据量性能测试

GET /mama/statistics/hotel/ranking
- 排行榜数据正确性
- 不同排行类型测试
- 分页限制测试
```

### 4. 性能测试 (Performance Tests)

#### 4.1 响应时间测试
```
目标指标：
- 单店铺查询：< 500ms
- 全平台查询：< 2s
- 排行榜查询：< 1s
```

#### 4.2 并发测试
```
测试场景：
- 100个并发用户同时查询
- 不同时间范围的查询压力
- 数据库连接池压力测试
```

#### 4.3 大数据量测试
```
测试数据：
- 100万订单数据
- 1000个店铺
- 1年时间跨度查询
```

## 测试数据准备

### 1. 基础测试数据
```sql
-- 创建测试店铺
INSERT INTO Shop (id, name, status) VALUES 
('test-shop-1', '测试酒店1', 'ACTIVE'),
('test-shop-2', '测试酒店2', 'ACTIVE');

-- 创建测试订单
INSERT INTO Trade (id, shopId, typeCode, statusCode, totalFee, paidFee, createdBuyerDate) VALUES
('trade-1', 'test-shop-1', 'TRADE_TYPE_HOTEL', 'HOTEL_CHECKED_OUT', 500.00, 500.00, '2025-01-01'),
('trade-2', 'test-shop-1', 'TRADE_TYPE_HOTEL', 'HOTEL_CHECKED_OUT', 800.00, 800.00, '2025-01-02');

-- 创建酒店订单信息
INSERT INTO t_trade_hotel (trade_id, nights, check_in_start_time, check_out_time) VALUES
('trade-1', 2, '2025-01-01 14:00:00', '2025-01-03 12:00:00'),
('trade-2', 1, '2025-01-02 15:00:00', '2025-01-03 11:00:00');
```

### 2. 边界测试数据
```sql
-- 零数据场景
-- 大数据量场景
-- 异常数据场景
```

## 测试环境

### 1. 开发环境测试
- 本地数据库
- Mock数据
- 快速反馈

### 2. 测试环境测试
- 完整数据集
- 真实业务场景
- 集成测试

### 3. 预生产环境测试
- 生产级数据量
- 性能压力测试
- 安全测试

## 当前状态和问题

### 编译问题
当前项目存在编译问题，主要是：
1. ProductSubTypeCodeEnum 和 ProductTypeCodeEnum 类找不到符号
2. 这些是现有项目的依赖问题，不影响我们新创建的统计功能

### 已完成的功能
✅ **核心架构完成**：
- HotelStatisticsQueryDto - 查询参数DTO
- HotelStatisticsDto - 统计结果DTO（含增长率计算）
- HotelStatisticsRepository - 数据访问接口
- HotelStatisticsService - 核心统计服务
- VendorHotelStatisticsService - 商家端服务
- MamaHotelStatisticsService - 运营端服务
- API Controllers - 完整的REST接口

### 下一步测试计划

#### 阶段1：修复编译问题 (0.5天)
1. 解决现有项目的依赖问题
2. 确保项目能正常编译

#### 阶段2：Repository实现 (1天)
1. 完善HotelStatisticsRepositoryImpl中的SQL查询逻辑
2. 实现具体的数据库查询方法

#### 阶段3：单元测试 (1天)
1. 测试DTO的计算逻辑
2. 测试Service的业务逻辑
3. Mock Repository进行服务测试

#### 阶段4：集成测试 (1天)
1. 测试完整的调用链路
2. 验证数据库查询结果
3. 测试API接口响应

## 测试工具

### 1. 单元测试
- JUnit 5
- Mockito
- AssertJ

### 2. 集成测试
- Spring Boot Test
- TestContainers
- H2 Database

### 3. 接口测试
- MockMvc
- RestAssured
- Postman

### 4. 性能测试
- JMeter
- Spring Boot Actuator
- Micrometer

## 验收标准

### 1. 功能验收
- 所有测试用例通过
- 业务需求完全覆盖
- 异常场景正确处理

### 2. 性能验收
- 响应时间满足要求
- 并发处理能力达标
- 资源使用合理

### 3. 质量验收
- 代码覆盖率 > 80%
- 无严重Bug
- 代码规范符合要求

## 风险评估

### 1. 技术风险
- 数据库查询性能
- 大数据量处理
- 并发安全问题

### 2. 业务风险
- 统计数据准确性
- 实时性要求
- 数据一致性

### 3. 缓解措施
- 分阶段测试
- 性能监控
- 回滚方案

---
*测试计划创建时间：2025/01/27*
*作者：liuzhaojun*
