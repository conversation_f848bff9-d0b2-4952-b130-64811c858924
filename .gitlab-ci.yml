stages:
  - build
  - check
  - upload
  - deploy

variables:
  AGILEACT_DOCKER_REGISTRY: docker.agileact.com
  AGILEACT_NAMESPACE: agileact/bamboocloud/bbc/bbc-server/bbc-server-support
  AGILEACT_DOCKER_REGISTRY_NAMESPACE: $AGILEACT_DOCKER_REGISTRY/$AGILEACT_NAMESPACE
  IMAGE: cdp-support-api
  IMAGE_VERSION_TEST: 1.0.0.test
  IMAGE_VERSION_PP: 1.0.0.pp
  IMAGE_VERSION_PROD: 1.0.0
  IMAGE_VERSION_TEST_32: 3.2.0.test

  PRODUCTION_DIR:  /agileact-products/${CI_PROJECT_PATH}

  agileactMavenUserName: $agileactMavenUserName
  agileactMavenPassword: $agileactMavenPassword
  AGILEACT_DOCKER_REGISTRY_USER_NAME: $AGILEACT_DOCKER_REGISTRY_USER_NAME
  AGILEACT_DOCKER_REGISTRY_PASSWORD: $AGILEACT_DOCKER_REGISTRY_PASSWORD

  OSS_PATH: builds/${CI_COMMIT_REF_NAME}/${CI_PROJECT_NAME}.${IMAGE_VERSION}.${CI_COMMIT_SHORT_SHA}.jar
  OSS_BUILD_TEST_REPORT_PATH: reports/${CI_COMMIT_REF_NAME}/${CI_PROJECT_NAME}/${CI_COMMIT_SHORT_SHA}
  OSS_BUCKET: bbc-builder-sz
  OSS_REGION: oss-cn-shenzhen
  OSS_REGION_INTERNAL: ${OSS_REGION}-internal

  ALIYUN_ACK_SZ: registry-vpc.cn-shenzhen.aliyuncs.com
  AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE: ${ALIYUN_ACK_SZ}/agileact-jzkj


#########################
##
## Build in AMD64/X86_64 Arch
##
#########################
build-in-x64:
  stage: build
  tags:
    - k8s
  image: registry-vpc.cn-shenzhen.aliyuncs.com/agileact/gradle:7.6.3-jdk17
  script:
    - mv ./repository.gradle ./repository.gradle.temp && envsubst '${agileactMavenUserName},${agileactMavenPassword}' < ./repository.gradle.temp > ./repository.gradle && rm -rf ./repository.gradle.temp
    - mv ./upload.gradle ./upload.gradle.temp && envsubst '${agileactMavenUserName},${agileactMavenPassword},${CI_COMMIT_REF_NAME}' < ./upload.gradle.temp > ./upload.gradle && rm -rf ./upload.gradle.temp
    - gradle uploadToAgileActMaven
    - gradle bootJar
    - ossutil -i ${BBC_ALIYUN_OSS_ACCESSKEYID}
              -k ${BBC_ALIYUN_OSS_ACCESSKEYSECRET}
              -e ${OSS_REGION_INTERNAL}.aliyuncs.com
              cp build/libs/*.jar oss://${OSS_BUCKET}/${OSS_PATH} -u
  only:
    - develop
    - pre-release
    - release
    - develop-3.2
    - /^springboot.*$/

upload-in-x64:
  stage: upload
  tags:
    - k8s
  script:
    - wget -O app.jar https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/${OSS_PATH}
    - docker login -u $AGILEACT_DOCKER_REGISTRY_USER_NAME -p $AGILEACT_DOCKER_REGISTRY_PASSWORD $AGILEACT_DOCKER_REGISTRY
    - >
      if [ "$CI_COMMIT_REF_NAME" == "develop" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_TEST}
      elif [ "$CI_COMMIT_REF_NAME" == "develop-3.2" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_TEST_32}
      elif [ "$CI_COMMIT_REF_NAME" == "pre-release" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PP}
      elif [ "$CI_COMMIT_REF_NAME" == "release" ]; then
        export DOCKER_IMAGE=${AGILEACT_ALIYUN_DOCKER_REGISTRY_NAMESPACE}/${IMAGE}:${IMAGE_VERSION_PROD}
      fi
    - docker login  -u ${ALIYUN_ACK_SZ_USERNAME}
                    -p ${ALIYUN_ACK_SZ_PASSWORD}
                       ${ALIYUN_ACK_SZ}
    - echo "Building ${DOCKER_IMAGE} ......"
    - docker build --pull -f ./api.dockerfile -t ${DOCKER_IMAGE} --force-rm --no-cache .
    - docker push ${DOCKER_IMAGE}
    - docker rmi ${DOCKER_IMAGE}
  after_script:
    - docker rmi -f $(docker images -f "dangling=true" -q)
  only:
    - develop
    - pre-release
    - release
    - develop-3.2
  needs:
    - build-in-x64

deploy-TEST-in-aliyun:
  stage: deploy
  tags:
    - office
  script:
    - kubectl --context=ali.agileact -n bbc-dev rollout restart deployment support-api
  when: manual
  only:
    - develop
    - develop-3.2
  needs:
    - upload-in-x64

deploy-PROD-in-aliyun:
  stage: deploy
  script:
    - kubectl --context=ali.bbc -n jzkj-prod rollout restart deployment support-api
  when: manual
  only:
    - release
  needs:
    - upload-in-x64


dependency-check:
  stage: check
  tags:
    - k8s
  image: registry-vpc.cn-shenzhen.aliyuncs.com/public-mirror-aa/dependency-check:7.4.4
  script:
    - wget -O /tmp/data.tar.gz https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/dependency-check/data.v744.tar.gz
    - tar zvxf /tmp/data.tar.gz -C /usr/share/dependency-check/
    - wget -O /src/${CI_PROJECT_NAME}.jar https://${OSS_BUCKET}.${OSS_REGION_INTERNAL}.aliyuncs.com/${OSS_PATH}
    - /usr/share/dependency-check/bin/dependency-check.sh -n
      --scan /src/${CI_PROJECT_NAME}.jar
      --format "ALL"
      --out /report
    - ossutil -i ${BBC_ALIYUN_OSS_ACCESSKEYID}
      -k ${BBC_ALIYUN_OSS_ACCESSKEYSECRET}
      -e ${OSS_REGION_INTERNAL}.aliyuncs.com
      cp -r /report oss://${OSS_BUCKET}/dependency-check/${CI_PROJECT_NAME}.${CI_COMMIT_REF_NAME}.${CI_COMMIT_SHORT_SHA} -u
    - echo "report --> http://builder.bbc.deer.art/dependency-check/${CI_PROJECT_NAME}.${CI_COMMIT_REF_NAME}.${CI_COMMIT_SHORT_SHA}/dependency-check-report.html"
  only:
    - develop-3.2
    - /^springboot.*$/
  needs:
    - build-in-x64
