dependencies {
    implementation("com.bamboocloud.cdp.glyb:cdp-framework-core:${agileactSpringbootCoreVersion}"){
        exclude group: "org.mongodb", module: "mongo-java-driver"
    }
    configurations.all {
        exclude group: "org.mongodb", module: "mongo-java-driver"
    }
    implementation("com.h2database:h2:${h2DatabaseVersion}")
    implementation("com.github.binarywang:weixin-java-miniapp:${wxJavaVersion}")
    implementation("com.github.binarywang:weixin-java-open:${wxJavaVersion}")
    implementation("com.github.binarywang:weixin-java-mp:${wxJavaVersion}")
    implementation("com.github.binarywang:weixin-java-pay:${wxJavaVersion}")
    implementation("redis.clients:jedis:${jedisVersion}")
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:${alibabaCloudVersion}")
    implementation("com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:${alibabaCloudVersion}")
    implementation("com.aliyun:aliyun-java-sdk-core:${aliyunSdkCoreVersion}") {
        exclude group: "pull-parser", module: "pull-parser"
    }
    implementation("com.aliyun.oss:aliyun-sdk-oss:${aliyunSdkOssVersion}") {
        exclude group: "pull-parser", module: "pull-parser"
    }
    implementation "com.huaweicloud:esdk-obs-java-bundle:3.23.9"
    implementation "com.huaweicloud.sdk:huaweicloud-sdk-bundle:3.1.62"
    implementation("com.bamboocloud.cdp.glyb:cdp-boot-starter-security:2.0.0-SNAPSHOT")
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")
    implementation("com.bamboocloud.cdp.glyb:cdp-util-sdk:${bbcUtilVersion}")
    implementation("com.bamboocloud.cdp.glyb:cdp-boot-starter-security:2.0.0-SNAPSHOT")
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")
    implementation "com.bamboocloud.cdp.glyb:cdp-common-sdk:1.0.0-SNAPSHOT"
}

shadowJar{
    classifier = ''

    include "**/entity/*.class"
    include "**/dto/**/*.class"
    include "**/dto/*.class"
    include "**/vo/**/*.class"
    include "**/common/constant/*.class"
    exclude "**/db/changelog/**/db.changelog-master.yaml"

    includeEmptyDirs = false
}
