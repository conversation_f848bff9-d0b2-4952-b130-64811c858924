/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2020. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-framework-core
 * @file: upload.gradle
 * @createdDate: 2019/05/06 23:52:06
 */

def getCurrentGitBranchName = {
    new ByteArrayOutputStream().withStream { os ->
        exec {
            executable = "git"
            args = ["rev-parse", "--abbrev-ref", "HEAD"]
            standardOutput = os
        }
        return os.toString()
    }
}

ext {
    agileactMavenVersion = thisVersion

    // 先从Gitlab CI中取branch名字 --- Gitlab CI中运行getCurrentGitBranchName()，会取不到branch name.
    currentGitBranchName = "${CI_COMMIT_REF_NAME}"

    // 本地非Gitlab环境运行时，取branch name
    if (currentGitBranchName.startsWith("NOT_SET")) {
        try {
            currentGitBranchName = "${branchName}"
        } catch (Exception e) {
            currentGitBranchName = getCurrentGitBranchName()
        }
    }

    println "currentGitBranchName is ${currentGitBranchName}"

//    if (!currentGitBranchName.startsWith("release")) {
//        agileactMavenVersion += "-SNAPSHOT"
//    }

    println " ||==============> 版本信息： ${agileactMavenVersion} \n"
}

publishing {
    repositories {
        maven {
            allowInsecureProtocol = true
            def releasesRepoUrl = "${agileactMavenUrl}/repository/chunxi-releases/"
            def snapshotsRepoUrl = "${agileactMavenUrl}/repository/chunxi-snapshot/"

            url = snapshotsRepoUrl
//            url = currentGitBranchName.startsWith('release') ? releasesRepoUrl : snapshotsRepoUrl

            println "repository url: ${url}"

            credentials {
                username "${agileactMavenUserName}"
                password "${agileactMavenPassword}"
               // username "andy"
               // password "andy@123"
            }
        }
    }

    publications {
        maven(MavenPublication) {
            groupId = "${thisGroup}"
            artifactId = "${thisArtifactId}"
            version = "${agileactMavenVersion}"

            from components.java
        }
    }


}
