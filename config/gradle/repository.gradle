/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2019. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-framework-core
 * @file: repository.gradle
 * @createdDate: 2019/05/07 09:40:07
 */

ext {
    agileactMavenUrl = "https://nexus.bamboocloud.com"
    mavenAliyunUrl = "https://maven.aliyun.com/nexus/content/groups/public"
    pluginGradleUrl = "https://maven.aliyun.com/repository/gradle-plugin"
}

def agileActRepo = {
    mavenLocal()

    maven {
        allowInsecureProtocol = true
        url "${agileactMavenUrl}/repository/chunxi/"

        credentials {
            username "${agileactMavenUserName}"
            password "${agileactMavenPassword}"
        }
    }

    maven {
        url mavenAliyunUrl
    }

    maven {
        url pluginGradleUrl
    }
}

project.buildscript.repositories(agileActRepo)

repositories(agileActRepo)

