/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorWxMiniAuthAppTest.java
 * @createdDate: 2022/12/12 16:00:12
 *
 */

package com.bamboocloud.cdp.util.test.vendor.application.security;

import cn.binarywang.wx.miniapp.bean.Watermark;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.framework.core.constant.FwkConstant;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.util.common.security.service.BaseWxMiniAuthService;
import com.bamboocloud.cdp.util.sdk.common.constant.UtilRouteConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxMiniLoginResultDto;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxMiniUserMobileInfoDto;
import com.bamboocloud.cdp.util.sdk.common.vo.security.WxMiniExtractUserMobileInfoVo;
import com.bamboocloud.cdp.util.sdk.common.vo.security.WxMiniLoginVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR> Mo
 * @description:
 */
@ExtendWith(SpringExtension.class)
public final class VendorWxMiniAuthAppTest extends FwkTestBaseApplication {

    @MockBean
    private BaseWxMiniAuthService baseWxMiniAuthService;

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testLoginVendorWxMini() throws Exception {
        String uri = getRoute(UtilRouteConstant.SECURITY_VENDOR_WX_MINI_AUTH_LOGIN);
        WxMiniLoginVo wxMiniLoginVo = new WxMiniLoginVo();
        wxMiniLoginVo.setCode("053fyk0w3ZL8nV21aC1w3l1Cvm2fyk07");
        WxMaJscode2SessionResult wxMaJscode2SessionResult = new WxMaJscode2SessionResult();
        WxMiniLoginResultDto wxMiniLoginResultDto = new WxMiniLoginResultDto();
        wxMaJscode2SessionResult.setOpenid("openId");
        wxMaJscode2SessionResult.setSessionKey("sessionKey");
        wxMaJscode2SessionResult.setUnionid("unionId");
        wxMiniLoginResultDto.setOpenId("openId");
        wxMiniLoginResultDto.setSessionKey("sessionKey");
        wxMiniLoginResultDto.setUnionId("unionId");
        when(baseWxMiniAuthService.loginWxMini(Mockito.any(), Mockito.any())).thenReturn(wxMiniLoginResultDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(wxMiniLoginVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorWxMiniAuthDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorWxMiniAuthDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(VendorWxMiniAuthDocsHelper.loginWxMiniRequestFields()),
                                                responseFields(VendorWxMiniAuthDocsHelper.loginWxMiniResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        wxMiniLoginResultDto = FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), WxMiniLoginResultDto.class);
        assertThat(wxMiniLoginResultDto.getOpenId()).isNotBlank();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testExtractVendorWxMiniMobileInfo() throws Exception {
        String uri = getRoute(UtilRouteConstant.SECURITY_VENDOR_WX_MINI_EXTRACT_USER_MOBILE_INFO);
        WxMiniExtractUserMobileInfoVo wxMiniExtractUserMobileInfoVo = new WxMiniExtractUserMobileInfoVo();
        wxMiniExtractUserMobileInfoVo.setEncryptedData("053fyk0w3ZL8nV21aC1w3l1Cvm2fyk07");
        wxMiniExtractUserMobileInfoVo.setIv("test");
        wxMiniExtractUserMobileInfoVo.setSessionKey("test");
        wxMiniExtractUserMobileInfoVo.setSignature("test");
        WxMiniUserMobileInfoDto wxMiniLoginResultDto = new WxMiniUserMobileInfoDto();
        wxMiniLoginResultDto.setCountryCode("86");
        wxMiniLoginResultDto.setPhoneNumber("861380000000");
        wxMiniLoginResultDto.setPurePhoneNumber("1380000000");
        Watermark watermark = new Watermark();
        watermark.setAppid("test");
        watermark.setTimestamp("2375642809946928650");
        wxMiniLoginResultDto.setWatermark(watermark);
        when(baseWxMiniAuthService.extractWxMiniMobileInfo(Mockito.any(), Mockito.any())).thenReturn(wxMiniLoginResultDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(wxMiniExtractUserMobileInfoVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorWxMiniAuthDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorWxMiniAuthDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(VendorWxMiniAuthDocsHelper.extractWxMiniMobileInfoRequestFields()),
                                                responseFields(VendorWxMiniAuthDocsHelper.extractWxMiniMobileInfoResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        wxMiniLoginResultDto = FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), WxMiniUserMobileInfoDto.class);
        assertThat(wxMiniLoginResultDto.getPurePhoneNumber()).isNotBlank();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }
}