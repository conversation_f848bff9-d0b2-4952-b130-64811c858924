/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorSubscribeMessageAppTest.java
 * @createdDate: 2022/12/12 16:00:12
 *
 */

package com.bamboocloud.cdp.util.test.vendor.application.subscribemessage;

import com.bamboocloud.cdp.framework.core.constant.FwkConstant;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.util.common.dto.subscribemessage.SubscribeMessageAddTemplateResultDto;
import com.bamboocloud.cdp.util.common.vo.subscribemessage.SubscribeMessageAddTemplateRequestVo;
import com.bamboocloud.cdp.util.common.vo.subscribemessage.SubscribeMessagePubTemplateTitleListRequestVo;
import com.bamboocloud.cdp.util.sdk.common.constant.UtilRouteConstant;
import com.bamboocloud.cdp.util.sdk.common.vo.subscribemessage.SubscribeMessageSendRequestVo;
import com.bamboocloud.cdp.util.vendor.subscribemessage.service.VendorSubscribeMessageService;
import com.fasterxml.jackson.databind.ObjectMapper;
import me.chanjar.weixin.common.bean.subscribemsg.CategoryData;
import me.chanjar.weixin.common.bean.subscribemsg.PubTemplateKeyword;
import me.chanjar.weixin.common.bean.subscribemsg.PubTemplateTitleListResult;
import me.chanjar.weixin.common.bean.subscribemsg.TemplateInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.*;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR> Shu
 * @description:
 */
@ExtendWith(SpringExtension.class)
public class VendorSubscribeMessageAppTest extends FwkTestBaseApplication {
    @MockBean
    private VendorSubscribeMessageService vendorSubscribeMessageService;

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testGetTemplateList() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_GET_TEMPLATE_LIST);
        TemplateInfo templateInfo = new TemplateInfo();
        templateInfo.setPriTmplId("9Aw5ZV1j9xdWTFEkqCpZ7mIBbSC34khK55OtzUPl0rU");
        templateInfo.setTitle("报名结果通知");
        templateInfo.setContent("会议时间:{{date2.DATA}}\\n会议地点:{{thing1.DATA}}\\n");
        templateInfo.setExample("会议时间:2016年8月8日\\n会议地点:TIT会议室\\n");
        templateInfo.setType(2);
        List<TemplateInfo> templateInfos = new ArrayList<>();
        templateInfos.add(templateInfo);
        when(vendorSubscribeMessageService.getTemplateList()).thenReturn(templateInfos);
        MockHttpServletResponse response =
                this.getMockMvc().perform(get(uri)
                                .headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                responseFields(VendorSubscribeMessageDocsHelper.getTemplateListResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testGetCategory() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_GET_CATEGORY);
        CategoryData categoryData = new CategoryData();
        categoryData.setId(1273);
        categoryData.setName("软件/建站/技术开发");
        List<CategoryData> categoryDataArrayList = new ArrayList<>();
        categoryDataArrayList.add(categoryData);
        when(vendorSubscribeMessageService.getCategory()).thenReturn(categoryDataArrayList);
        MockHttpServletResponse response =
                this.getMockMvc().perform(get(uri)
                                .headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                responseFields(VendorSubscribeMessageDocsHelper.getCategoryTemplateListResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testGetPubTemplateTitleList() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_TITLE_LIST);
        PubTemplateTitleListResult pubTemplateTitleListResult = new PubTemplateTitleListResult();
        pubTemplateTitleListResult.setCount(1772);
        List<PubTemplateTitleListResult.TemplateItem> templateItems = new ArrayList<>();
        PubTemplateTitleListResult.TemplateItem templateItem = new PubTemplateTitleListResult.TemplateItem();
        templateItem.setType(2);
        templateItem.setTid(374);
        templateItem.setCategoryId("782");
        templateItem.setTitle("订单发货提醒");
        templateItems.add(templateItem);
        pubTemplateTitleListResult.setData(templateItems);
        when(vendorSubscribeMessageService.getPubTemplateTitleList(Mockito.any())).thenReturn(pubTemplateTitleListResult);
        SubscribeMessagePubTemplateTitleListRequestVo subscribeMessagePubTemplateTitleListRequestVo = new SubscribeMessagePubTemplateTitleListRequestVo();
        List<String> ids = new ArrayList<>();
        ids.add("1273");
        subscribeMessagePubTemplateTitleListRequestVo.setIds(ids);
        MockHttpServletResponse response =
                this.getMockMvc().perform(post(uri)
                                .headers(this.getHeaders(true, true, true))
                                .content(new ObjectMapper().writeValueAsString(subscribeMessagePubTemplateTitleListRequestVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(VendorSubscribeMessageDocsHelper.getPubTemplateTitleListRequestFields()),
                                                responseFields(VendorSubscribeMessageDocsHelper.getPubTemplateTitleListResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testGetPubTemplateKeyWordsById() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_GET_PUB_TEMPLATE_KEYWORDS_BY_ID);
        PubTemplateKeyword pubTemplateKeyword = new PubTemplateKeyword();
        pubTemplateKeyword.setKid(1);
        pubTemplateKeyword.setName("商品名称");
        pubTemplateKeyword.setExample("手机壳");
        pubTemplateKeyword.setRule("thing");
        List<PubTemplateKeyword> pubTemplateKeywords = new ArrayList<>();
        pubTemplateKeywords.add(pubTemplateKeyword);
        when(vendorSubscribeMessageService.getPubTemplateKeyWordsById(Mockito.any())).thenReturn(pubTemplateKeywords);
        MockHttpServletResponse response =
                this.getMockMvc().perform(get(uri, "tid")
                                .headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(true,
                                                        false,
                                                        false)),
                                                responseFields(VendorSubscribeMessageDocsHelper.getPubTemplateKeyWordsByIdResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testAddTemplate() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_ADD_TEMPLATE);
        SubscribeMessageAddTemplateResultDto subscribeMessageAddTemplateResultDto = new SubscribeMessageAddTemplateResultDto();
        subscribeMessageAddTemplateResultDto.setPriTmplId("Jk-KKCizU-IdkP8vwad2kfcW6S-BboYnanw5qkXtT88");
        when(vendorSubscribeMessageService.addTemplate(Mockito.any())).thenReturn(subscribeMessageAddTemplateResultDto);
        SubscribeMessageAddTemplateRequestVo subscribeMessageAddTemplateRequestVo = new SubscribeMessageAddTemplateRequestVo();
        subscribeMessageAddTemplateRequestVo.setId("374");
        List<Integer> keywordIdList = new ArrayList<>();
        keywordIdList.add(1);
        subscribeMessageAddTemplateRequestVo.setKeywordIdList(keywordIdList);
        subscribeMessageAddTemplateRequestVo.setSceneDesc("服务场景描述");
        MockHttpServletResponse response =
                this.getMockMvc().perform(post(uri)
                                .headers(this.getHeaders(true, true, true))
                                .content(new ObjectMapper().writeValueAsString(subscribeMessageAddTemplateRequestVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(VendorSubscribeMessageDocsHelper.addTemplateRequestFields()),
                                                responseFields(VendorSubscribeMessageDocsHelper.addTemplateResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testDeleteTemplate() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_DELETE_TEMPLATE);
        MockHttpServletResponse response =
                this.getMockMvc().perform(delete(uri, "模板Id")
                                .headers(this.getHeaders(true, true, true)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(true,
                                                        false,
                                                        false)),
                                                responseFields(VendorSubscribeMessageDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testSendSubscribeMessage() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SUBSCRIBE_MESSAGE_SEND_SUBSCRIBE_MESSAGE);
        SubscribeMessageSendRequestVo subscribeMessageSendRequestVo = new SubscribeMessageSendRequestVo();
        subscribeMessageSendRequestVo.setToUser("openId");
        subscribeMessageSendRequestVo.setTemplateId("所需下发的订阅模板id");
        subscribeMessageSendRequestVo.setPage("点击模板卡片后的跳转页面，仅限本小程序内的页面。支持带参数,（示例index?foo=bar）。该字段不填则模板无跳转。");
        List<SubscribeMessageSendRequestVo.MsgData> data = new ArrayList<>();
        SubscribeMessageSendRequestVo.MsgData msgData = new SubscribeMessageSendRequestVo.MsgData("key1", "value1");
        data.add(msgData);
        subscribeMessageSendRequestVo.setData(data);
        MockHttpServletResponse response =
                this.getMockMvc().perform(post(uri)
                                .headers(this.getHeaders(true, true, true))
                                .content(new ObjectMapper().writeValueAsString(subscribeMessageSendRequestVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorSubscribeMessageDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorSubscribeMessageDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(VendorSubscribeMessageDocsHelper.sendSubscribeMessageRequestFields()),
                                                responseFields(VendorSubscribeMessageDocsHelper.getApiResponseFields())))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }
}
