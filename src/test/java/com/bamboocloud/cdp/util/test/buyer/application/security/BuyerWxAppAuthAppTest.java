/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BuyerWxAppAuthAppTest.java
 * @createdDate: 2022/12/12 16:00:12
 *
 */

package com.bamboocloud.cdp.util.test.buyer.application.security;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.framework.core.constant.FwkConstant;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.util.common.security.service.BaseWxAppAuthService;
import com.bamboocloud.cdp.util.sdk.common.constant.UtilRouteConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxAppLoginResultDto;
import com.bamboocloud.cdp.util.sdk.common.vo.security.WxAppLoginVo;
import com.fasterxml.jackson.databind.ObjectMapper;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.payload.PayloadDocumentation.responseFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR> Mo
 * @description:
 */
@ExtendWith(SpringExtension.class)
public final class BuyerWxAppAuthAppTest extends FwkTestBaseApplication {

    @MockBean
    private BaseWxAppAuthService baseWxAppAuthService;

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testLoginBuyerWxApp() throws Exception {
        String uri = getRoute(UtilRouteConstant.SECURITY_BUYER_WX_APP_AUTH_LOGIN);
        WxAppLoginVo wxAppLoginVo = new WxAppLoginVo();
        wxAppLoginVo.setCode("053fyk0w3ZL8nV21aC1w3l1Cvm2fyk07");
        WxOAuth2AccessToken wxOAuth2AccessToken = new WxOAuth2AccessToken();
        WxAppLoginResultDto wxAppLoginResultDto = new WxAppLoginResultDto();
        wxOAuth2AccessToken.setOpenId("openId");
        wxOAuth2AccessToken.setAccessToken("accessToken");
        wxOAuth2AccessToken.setRefreshToken("refreshToken");
        wxOAuth2AccessToken.setScope("scope");
        wxOAuth2AccessToken.setUnionId("unionId");
        wxOAuth2AccessToken.setExpiresIn(-1);
        wxAppLoginResultDto.setOpenId("openId");
        wxAppLoginResultDto.setAccessToken("accessToken");
        wxAppLoginResultDto.setRefreshToken("refreshToken");
        wxAppLoginResultDto.setScope("scope");
        wxAppLoginResultDto.setExpiresIn(-1);
        double minValue = Double.MIN_VALUE;
        when(baseWxAppAuthService.loginWxApp(Mockito.any(),Mockito.any())).thenReturn(wxAppLoginResultDto);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(wxAppLoginVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(BuyerWxAppAuthDocsHelper.getRequestHeaders(true)),
                                                pathParameters(BuyerWxAppAuthDocsHelper.getPathParameters(false, false,
                                                        false)),
                                                requestFields(BuyerWxAppAuthDocsHelper.loginWxAppRequestFields()),
                                                responseFields(BuyerWxAppAuthDocsHelper.loginWxAppResponseFields())))
                        .andReturn()
                        .getResponse();
        FwkApiResponse fwkApiResponse = FwkJsonUtil.toObject(response.getContentAsString(), FwkApiResponse.class);
        wxAppLoginResultDto = FwkJsonUtil.toObject((JSONObject) fwkApiResponse.getData(), WxAppLoginResultDto.class);
        assertThat(wxAppLoginResultDto.getOpenId()).isNotBlank();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}