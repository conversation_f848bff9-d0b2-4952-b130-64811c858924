/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorProductDocsHelper.java
 * @createdDate: 2022/12/10 15:50:10
 *
 */

package com.bamboocloud.cdp.util.test.vendor.application.product;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.util.sdk.common.vo.shop.ShopQrCodeCreationVo;
import org.springframework.http.MediaType;
import org.springframework.restdocs.headers.ResponseHeadersSnippet;
import org.springframework.restdocs.payload.FieldDescriptor;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.headers.HeaderDocumentation.headerWithName;
import static org.springframework.restdocs.headers.HeaderDocumentation.responseHeaders;

/**
 * <AUTHOR>
 * @description:
 */
public class VendorProductDocsHelper extends FwkTestBaseDocsHelper {

    public static List<FieldDescriptor> generateQrCodeRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(ShopQrCodeCreationVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("scene").description("scene – " +
                "最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~， 其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode " +
                "处理，请使用其他编码方式）"));
        fieldDescriptors.add(constrainedFields.withPath("page").description("page – 必须是已经发布的小程序页面，例如 " +
                "\"pages/index/index\" ,如果不填写这个字段，默认跳主页面"));
        return fieldDescriptors;
    }

    public static ResponseHeadersSnippet generateQrCodeResponseHeadersSnippet() {
        return responseHeaders(headerWithName("Content-Type").description(MediaType.APPLICATION_OCTET_STREAM.toString()));
    }
}
