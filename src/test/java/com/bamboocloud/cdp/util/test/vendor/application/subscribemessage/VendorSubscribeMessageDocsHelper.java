/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorSubscribeMessageDocsHelper.java
 * @createdDate: 2022/12/10 15:50:10
 *
 */

package com.bamboocloud.cdp.util.test.vendor.application.subscribemessage;

import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseDocsHelper;
import com.bamboocloud.cdp.util.common.vo.subscribemessage.SubscribeMessageAddTemplateRequestVo;
import com.bamboocloud.cdp.util.common.vo.subscribemessage.SubscribeMessagePubTemplateTitleListRequestVo;
import com.bamboocloud.cdp.util.sdk.common.vo.subscribemessage.SubscribeMessageSendRequestVo;
import org.springframework.restdocs.payload.FieldDescriptor;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.restdocs.payload.PayloadDocumentation.fieldWithPath;

/**
 * <AUTHOR> Shu
 * @description:
 */
public class VendorSubscribeMessageDocsHelper extends FwkTestBaseDocsHelper {

    public static List<FieldDescriptor> sendSubscribeMessageRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(SubscribeMessageSendRequestVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("toUser").description("接收者（用户）的 openid"));
        fieldDescriptors.add(constrainedFields.withPath("templateId").description("所需下发的模板消息的id"));
        fieldDescriptors.add(constrainedFields.withPath("page").description("点击模板卡片后的跳转页面，仅限本小程序内的页面"));
        fieldDescriptors.add(constrainedFields.withPath("data[]").description("模板内容，不填则下发空模板"));
        fieldDescriptors.add(constrainedFields.withPath("data[].name").description("模板内容."));
        fieldDescriptors.add(constrainedFields.withPath("data[].value").description("模板内容."));
        fieldDescriptors.add(constrainedFields.withPath("miniProgramState").description(" 跳转小程序类型：developer为开发版；trial为体验版；formal为正式版；默认为正式版."));
        fieldDescriptors.add(constrainedFields.withPath("lang").description("进入小程序查看的语言类型，支持zh_CN(简体中文)、en_US(英文)、zh_HK(繁体中文)、zh_TW(繁体中文)，默认为zh_CN."));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getTemplateListResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]priTmplId").description("添加至帐号下的模板 id，发送小程序订阅消息时所需"));
        fieldDescriptors.add(fieldWithPath("data.[]title").description("模版标题"));
        fieldDescriptors.add(fieldWithPath("data.[]content").description("模版内容"));
        fieldDescriptors.add(fieldWithPath("data.[]example").description("模板内容示例"));
        fieldDescriptors.add(fieldWithPath("data.[]type").description("模版类型，2 为一次性订阅，3 为长期订阅"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getCategoryTemplateListResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]id").description("类目id，查询公共库模版时需要"));
        fieldDescriptors.add(fieldWithPath("data.[]name").description("类目的中文名"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getPubTemplateKeyWordsByIdResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.[]kid").description("关键词 id，选用模板时需要"));
        fieldDescriptors.add(fieldWithPath("data.[]name").description("关键词内容"));
        fieldDescriptors.add(fieldWithPath("data.[]example").description("关键词内容对应的示例"));
        fieldDescriptors.add(fieldWithPath("data.[]rule").description("参数类型"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getPubTemplateTitleListRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(SubscribeMessagePubTemplateTitleListRequestVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("ids[]").description("类目 id数组"));
        fieldDescriptors.add(constrainedFields.withPath("start").description("用于分页，表示从 start 开始。从 0 开始计数"));
        fieldDescriptors.add(constrainedFields.withPath("limit").description("用于分页，表示拉取 limit 条记录。最大为 30"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> addTemplateRequestFields() {
        ConstrainedFields constrainedFields = new ConstrainedFields(SubscribeMessageAddTemplateRequestVo.class);
        List<FieldDescriptor> fieldDescriptors = new ArrayList<>();
        fieldDescriptors.add(constrainedFields.withPath("id").description("模板标题 id，可通过接口获取，也可登录小程序后台查看获取"));
        fieldDescriptors.add(constrainedFields.withPath("keywordIdList[]").description("开发者自行组合好的模板关键词列表，关键词顺序可以自由搭配（例如 [3,5,4] 或 [4,5,3]），最多支持5个，最少2个关键词组合"));
        fieldDescriptors.add(constrainedFields.withPath("sceneDesc").description("服务场景描述，15个字以内"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> getPubTemplateTitleListResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.count").description("数量"));
        fieldDescriptors.add(fieldWithPath("data.data[]").description("模板数组"));
        fieldDescriptors.add(fieldWithPath("data.data[].tid").description("模版标题 id"));
        fieldDescriptors.add(fieldWithPath("data.data[].categoryId").description("模版所属类目 id"));
        fieldDescriptors.add(fieldWithPath("data.data[].title").description("模版标题"));
        fieldDescriptors.add(fieldWithPath("data.data[].type").description("模版类型，2 为一次性订阅，3 为长期订阅"));
        return fieldDescriptors;
    }

    public static List<FieldDescriptor> addTemplateResponseFields() {
        List<FieldDescriptor> fieldDescriptors = getApiResponseFields();
        fieldDescriptors.add(fieldWithPath("data.priTmplId").description("添加至帐号下的模板id，发送小程序订阅消息时所需"));
        return fieldDescriptors;
    }
}
