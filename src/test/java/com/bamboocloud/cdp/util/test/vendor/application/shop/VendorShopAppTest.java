/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: VendorShopAppTest.java
 * @createdDate: 2022/12/13 16:07:13
 *
 */

package com.bamboocloud.cdp.util.test.vendor.application.shop;

import com.bamboocloud.cdp.framework.core.constant.FwkConstant;
import com.bamboocloud.cdp.framework.core.test.common.base.FwkTestBaseApplication;
import com.bamboocloud.cdp.util.sdk.common.constant.UtilRouteConstant;
import com.bamboocloud.cdp.util.sdk.common.vo.shop.ShopQrCodeCreationVo;
import com.bamboocloud.cdp.util.vendor.shop.service.VendorShopService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.File;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.headers.HeaderDocumentation.requestHeaders;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.post;
import static org.springframework.restdocs.payload.PayloadDocumentation.requestFields;
import static org.springframework.restdocs.request.RequestDocumentation.pathParameters;

/**
 * <AUTHOR> Shu
 * @description:
 */
@ExtendWith(SpringExtension.class)
public class VendorShopAppTest extends FwkTestBaseApplication {
    @MockBean
    private VendorShopService vendorShopService;

    @Test
    @WithMockUser(username = FwkConstant.SUPER_ADMIN_USERNAME)
    public void testGenerateQrCode() throws Exception {
        String uri = getRoute(UtilRouteConstant.VENDOR_SHOP_GENERATE_QRCODE);
        ShopQrCodeCreationVo shopQrCodeCreationVo = new ShopQrCodeCreationVo();
        shopQrCodeCreationVo.setScene("test");
        shopQrCodeCreationVo.setPage("test");
        File file = new File(".gitignore");
        when(vendorShopService.generateQrCode(Mockito.any())).thenReturn(file);
        MockHttpServletResponse response =
                this.getMockMvc()
                        .perform(
                                post(uri)
                                        .headers(this.getHeaders(true, true, true))
                                        .content(new ObjectMapper().writeValueAsString(shopQrCodeCreationVo)))
                        .andDo(
                                this.getDocumentHandler()
                                        .document(
                                                requestHeaders(VendorShopDocsHelper.getRequestHeaders(true)),
                                                pathParameters(VendorShopDocsHelper.getPathParameters(false,
                                                        false,
                                                        false)),
                                                requestFields(VendorShopDocsHelper.generateQrCodeRequestFields()),
                                                VendorShopDocsHelper.generateQrCodeResponseHeadersSnippet()))
                        .andReturn()
                        .getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}
