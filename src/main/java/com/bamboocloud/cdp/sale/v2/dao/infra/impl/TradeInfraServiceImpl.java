package com.bamboocloud.cdp.sale.v2.dao.infra.impl;

import com.bamboocloud.cdp.sale.common.dto.buyer.trade.TradDistributorDto;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.ITradeInfraService;
import com.bamboocloud.cdp.sale.v2.dao.mapper.ITradeMapper;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class TradeInfraServiceImpl extends ServiceImpl<ITradeMapper, TradeEntity> implements ITradeInfraService {


    @Autowired
    private ITradeMapper tradeMapper;
    @Override
    public TradeEntity getByTradeProductId(Integer tradeProductId) {
        return tradeMapper.getByTradeProductId(tradeProductId);
    }

    @Override
    public List<String> listBuyerIdByPaidFee(BigDecimal minPaidFee, BigDecimal maxPaidFee) {
        return tradeMapper.listBuyerIdByPaidFee(minPaidFee,maxPaidFee);
    }

    @Override
    public TradeEntity findByOrderId(String orderId) {
        return this.getOne(new LambdaQueryWrapper<TradeEntity>()
                .eq(TradeEntity::getOrderId,orderId)
                .last("limit 1"));
    }

    @Override
    public List<TradeEntity> getByTradeGroupId(String tradeGroupId) {
        return this.list(new LambdaQueryWrapper<TradeEntity>()
                .eq(TradeEntity::getTradeGroupId,tradeGroupId));
    }

    @Override
    public Integer countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate) {

        return tradeMapper.countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(shopId, startDate, endDate);
    }

    @Override
    public Integer countBuyerIdByShopIdAndCreatedBuyerDateBetween(String shopId, LocalDateTime startDate, LocalDateTime endDate) {
        return tradeMapper.countBuyerIdByShopIdAndCreatedBuyerDateBetween(shopId, startDate, endDate);

    }

    @Override
    public Integer countByBuyerIdAndCreatedDateAndStatusCodes(String buyerId, LocalDateTime startDate, LocalDateTime endDate) {
        long count = this.count(new LambdaQueryWrapper<TradeEntity>()
            .eq(TradeEntity::getBuyerId, buyerId)
            .eq(TradeEntity::getDeletedBuyer, false)
            .between(TradeEntity::getCreatedBuyerDate, startDate, endDate));
        return (int) count;
    }


    @Override
    public List<TradeEntity> findAllByBuyerIdAndDeletedBuyerIsFalse(String buyerId) {
        if (StringUtils.isBlank(buyerId)) {
            return Collections.emptyList();
        }

        return tradeMapper.selectList(new LambdaQueryWrapper<TradeEntity>()
            .eq(TradeEntity::getBuyerId, buyerId)
            .eq(TradeEntity::getDeletedBuyer, false));
    }

    @Override
    public TradeEntity findFirstByDouYinOrderIdAndStatusCodeIn(String douYinOrderId, List<String> doneStatus) {
        if (StringUtils.isBlank(douYinOrderId) || CollectionUtils.isEmpty(doneStatus)) {
            return null;
        }
        return this.getOne(new LambdaQueryWrapper<TradeEntity>()
            .eq(TradeEntity::getDouYinOrderId, douYinOrderId)
            .in(TradeEntity::getStatusCode, doneStatus)
            .last("limit 1"));
    }

    @Override
    public List<TradeEntity> findAllByShopId(String shopId) {
        if (StringUtils.isBlank(shopId)) {
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<TradeEntity>().eq(TradeEntity::getShopId, shopId));

    }

    @Override
    public String getIdByShopId(String shopId, List<String> statusCodeList) {
        List<TradeEntity> tradeEntities = findAllByShopId(shopId);
        List<TradeEntity> filteredList = tradeEntities.stream().filter(tradeEntity -> !statusCodeList.contains(tradeEntity.getStatusCode()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filteredList)) {
            return null;
        }
        return filteredList.get(0).getId();
    }

    @Override
    public TradeEntity getUnfinishedTradeByBuyerIdAndStatusCodes(String buyerId, List<String> statusCodes) {
        if (StringUtils.isBlank(buyerId) || CollectionUtils.isEmpty(statusCodes)) {
            return null;
        }

        return this.getOne(new LambdaQueryWrapper<TradeEntity>()
            .eq(TradeEntity::getBuyerId, buyerId)
            .in(TradeEntity::getStatusCode, statusCodes)
            .last("limit 1"));
    }

    @Override
    public List<TradeEntity> getByShopIdAndPosOrderId(String shopId, String posOrderId) {
        return this.list(new LambdaQueryWrapper<TradeEntity>()
            .eq(TradeEntity::getShopId, shopId)
            .eq(TradeEntity::getPosOrderId, posOrderId));
    }
    @Override
    public IPage<TradDistributorDto> searchTradDistributorList(Page<TradDistributorDto> pageParam, List<String> tradIds) {
        return tradeMapper.searchTradDistributorList(pageParam, tradIds);
    }

    @Override
    public List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds) {
        return this.baseMapper.getAlcoholRedemptionCount(shopIds);
    }


}
