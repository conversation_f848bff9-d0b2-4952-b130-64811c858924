package com.bamboocloud.cdp.sale.v2.dao.infra;


import com.bamboocloud.cdp.boot.user.common.bo.vendor.LoginVendorBo;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.category.BuyerProductCategoryDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaProductDto;
import com.bamboocloud.cdp.sale.sdk.domain.bo.MamaProductAuctionInfoBo;
import com.bamboocloud.cdp.sale.sdk.domain.bo.MamaProductInfoBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface IProductInfraService extends IService<ProductEntity> {
    List<ProductEntity> getCivilList(String shopId);
    void updateProduct(List<String> ids, Integer stockQuantity, LoginVendorBo loginVendorBo);

    List<ProductEntity>findAllByIdIn(List<String> ids);
    void updateProductCateGoryId(Integer sourceId, Integer targetId);
    List<MamaProductDto> listByProductCategoryId(Integer productCategoryId);
    MamaProductDto getFirstByProductCategoryId(Integer productCategoryId);
    Integer countByProductCategoryId(Integer productCategoryId);
    String getFileUrl(String productId);
    String getNameById(String id);
    void updateProductAuctionForOffShow(String productId, String statusCode);
    void updateProductAuctionStatus(String productId, String statusCode);
    List<MamaProductInfoBo> listByOnShowAndTypeCodeAndShopId(boolean onShow, String typeCode, String shopId);
    void updateStatusForOnShow(List<MamaProductInfoBo> mamaProductInfoBos, boolean onShow) ;
    void updateProductExhibitionStatus(String productId, String statusCode);
    void updateProductFixedPriceStatus(String productId, String statusCode);
    List<MamaProductAuctionInfoBo> listProductAuctionByProductIds(List<String> productIds);
    void delete(List<String> ids, LoginVendorBo loginVendorBo);
    void updateProductShowStatus(List<String> ids, Boolean onShow);
    long countByShopIdAndStatusCode(String shopId, String statusCode);

    ProductEntity findFirstByExposedId(String exposedId);

    List<Integer> getAllProductCategory(BuyerProductCategoryDto dto);

    List<ProductEntity> findByShopIdAndDistribute(String lastLoginShopId);

    long countByShopIdAndDistribute(String lastLoginShopId);

    /**
     * 查询商品库存占用的兑酒券
     * @param shopIds
     * @return
     */
    List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds);
}
