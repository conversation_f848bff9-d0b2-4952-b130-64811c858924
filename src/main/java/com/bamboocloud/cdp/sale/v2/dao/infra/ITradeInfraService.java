package com.bamboocloud.cdp.sale.v2.dao.infra;

import com.bamboocloud.cdp.sale.common.dto.buyer.trade.TradDistributorDto;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
public interface ITradeInfraService extends IService<TradeEntity> {

    TradeEntity getByTradeProductId(Integer tradeProductId);

    List<String> listBuyerIdByPaidFee(BigDecimal minPaidFee, BigDecimal maxPaidFee);

    TradeEntity findByOrderId(String orderId);

    List<TradeEntity> getByTradeGroupId(String tradeGroupId);

    Integer countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(String shopId, LocalDateTime startDate, LocalDateTime endDate);

    Integer countBuyerIdByShopIdAndCreatedBuyerDateBetween(String shopId, LocalDateTime startDate, LocalDateTime endDate);

    Integer countByBuyerIdAndCreatedDateAndStatusCodes(String buyerId, LocalDateTime startDate, LocalDateTime endDate);

    List<TradeEntity> findAllByBuyerIdAndDeletedBuyerIsFalse(String buyerId);

    TradeEntity findFirstByDouYinOrderIdAndStatusCodeIn(String douYinOrderId, List<String> doneStatus);

    List<TradeEntity> findAllByShopId(String shopId);

    String getIdByShopId(String shopId, List<String> statusCodeList);

    TradeEntity getUnfinishedTradeByBuyerIdAndStatusCodes(String buyerId, List<String> statusCodes);

    List<TradeEntity> getByShopIdAndPosOrderId(String shopId, String posOrderId);

    IPage<TradDistributorDto> searchTradDistributorList(Page<TradDistributorDto> pageParam, List<String> collect);

    /**
     * 店铺已售兑酒券数量
     * @param shopIds
     * @return
     */
    List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds);
}
