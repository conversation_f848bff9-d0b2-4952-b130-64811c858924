package com.bamboocloud.cdp.sale.v2.dao.mapper;

import com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.buyerview.BuyerProductBuyerViewDto;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.category.BuyerProductCategoryDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaBuyerProductListDto;
import com.bamboocloud.cdp.sale.common.dto.vendor.product.VendorProductCountListDto;
import com.bamboocloud.cdp.sale.common.vo.buyer.product.BuyerProductSearchVo;
import com.bamboocloud.cdp.sale.common.vo.mama.product.MamaBuyerProductSearchVo;
import com.bamboocloud.cdp.sale.common.vo.vendor.product.VendorProductCountVo;
import com.bamboocloud.cdp.sale.domain.param.HotelProductParam;
import com.bamboocloud.cdp.sale.domain.param.ProductPageParam;
import com.bamboocloud.cdp.sale.domain.vo.ProductList4cDTO;
import com.bamboocloud.cdp.sale.domain.vo.ProductListVO;
import com.bamboocloud.cdp.sale.sdk.domain.dto.MamaProductSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductSimpleDto;
import com.bamboocloud.cdp.sale.sdk.domain.vo.MamaProductEquityBoxUpdateStatusVo;
import com.bamboocloud.cdp.sale.v2.convert.BuyerProductPageBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface IProductMapper extends BaseMapper<ProductEntity> {

    List<String> findProductIds(
            @Param("request") MamaProductEquityBoxUpdateStatusVo mamaProductEquityBoxUpdateStatusVo);

    void updateStatus(@Param("productIds") List<String> productIds, @Param("statusCode") String statusCode);

    IPage<MamaBuyerProductListDto> search(@Param("pageParam") Page<MamaBuyerProductListDto> pageParam,
                                          @Param("searchVo") MamaBuyerProductSearchVo searchVo);

    MamaProductSimpleDto getSimpleInfo(@Param("productId") String productId);

    String getByDispShopProductCategoryId(@Param("dispShopProductCategoryId") Long dispShopProductCategoryId);

    String getVirtualProductByShopIdAndSubTypeCode(@Param("shopId") String shopId,
                                                   @Param("subTypeCode") String subTypeCode);


    BuyerProductSimpleDto getBuyerSimpleInfo(@Param("productId") String productId);


    String getShopIdById(@Param("productId") String productId);

    void updateOnShow(@Param("id") String id, @Param("onShow") boolean onShow);

    List<BuyerProductDto> getByIds(@Param("ids") List<String> ids);

    Page<BuyerProductPageBo> searchBuyer(@Param("pageParam") Page<MamaBuyerProductListDto> pageParam,
                                         @Param("searchVo") BuyerProductSearchVo searchVo,
                                         @Param("neShopIds") List<String> neShopIds);

    List<VendorProductCountListDto> countByStatusCode(
            @Param("vendorProductCountVo") VendorProductCountVo vendorProductCountVo);

    BuyerProductBuyerViewDto getBuyerProductView(@Param("productId") String productId);

    List<Integer> getAllProductCategory(@Param("param") BuyerProductCategoryDto param);


    void minusDistributeCount(@Param("productId") String productId, @Param("productQuantity") Integer productQuantity);

    void addDistributeCount(@Param("productId") String productId, @Param("productQuantity") Integer productQuantity);

    /**
     * 分页查询商品信息
     *
     * @param param 分页查询信息
     * @return 商品分页数据
     */
    IPage<ProductListVO> page4Hotel(IPage<?> page, @Param("param") ProductPageParam param);

    /**
     * 查询店铺下的商品列表
     *
     * @param param 参数信息
     * @return 商品列表
     */
    List<ProductList4cDTO> list4Hotel(@Param("param") HotelProductParam param);

    /**
     * 商品库存占用的兑酒券
     * @param shopIds
     * @return
     */
    List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(@Param("shopIds") List<String> shopIds);
}
