package com.bamboocloud.cdp.sale.v2.dao.mapper;

import com.bamboocloud.cdp.sale.common.bo.vendor.trade.VendorTradeStatisticalDataBo;
import com.bamboocloud.cdp.sale.common.dto.base.analysis.trade.TradeStatisticsDTO;
import com.bamboocloud.cdp.sale.common.dto.buyer.trade.TradDistributorDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaActivityDataProductDto;
import com.bamboocloud.cdp.sale.common.dto.mama.trade.*;
import com.bamboocloud.cdp.sale.common.dto.vendor.trade.VendorActivityDataProductDto;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.MamaBuyerTradeSearchVo;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.MamaDistributorTradeSearchVo;
import com.bamboocloud.cdp.sale.common.vo.mama.trade.MamaShopOrderSearchVo;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.trade.VendorTradeGetBuyerIdDto;
import com.bamboocloud.cdp.sale.sdk.domain.param.TradeStatisticsParam;
import com.bamboocloud.cdp.sale.sdk.domain.vo.TradeAndPointTotalVo;
import com.bamboocloud.cdp.sale.v2.bo.TradeBo;
import com.bamboocloud.cdp.sale.v2.dao.entity.TradeEntity;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-22
 */
@Mapper
public interface ITradeMapper extends BaseMapper<TradeEntity> {

    TradeEntity getByTradeProductId(@Param("tradeProductId") Integer tradeProductId);

    List<String> listBuyerIdByPaidFee(@Param("minPaidFee") BigDecimal minPaidFee,
                                      @Param("maxPaidFee") BigDecimal maxPaidFee);

    Integer countBuyerIdByShopIdAndPaidDateBetweenAndPaidAlreadyIsTrue(@Param("shopId") String shopId,
                                                                       @Param("startDate") LocalDateTime startDate,
                                                                       @Param("endDate") LocalDateTime endDate);

    Integer countBuyerIdByShopIdAndCreatedBuyerDateBetween(@Param("shopId") String shopId,
                                                           @Param("startDate") LocalDateTime startDate,
                                                           @Param("endDate") LocalDateTime endDate);


    TradeBo getAllByIdAndCreatedBuyerId(@Param("id") String id, @Param("createdBuyerId") String createdBuyerId);

    Page<TradeEntity> search(@Param("pageParam") Page<TradeEntity> pageParam, @Param("searchVo") MamaBuyerTradeSearchVo searchVo);

    List<TradeEntity> findTradeStatisticalData(@Param("couponIds") Set<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate, @Param("activityId") Long activityId);

    Long countByProductIdAndBuyerIdAndStatusCodes(@Param("productId") String productId, @Param("buyerId") String buyerId, @Param("deviceId") String deviceId,
                                                  @Param("statusCodes") List<String> statusCodes);

    List<MamaActivityDataProductDto> findTradeProductStatisticalData(@Param("couponIds") Set<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                                     @Param("endDate") LocalDateTime endDate);

    List<VendorActivityDataProductDto> findVendorTradeProductStatisticalData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                                             @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("sortPaidFee") Boolean sortPaidFee,
                                                                             @Param("sortPaidCount") Boolean sortPaidCount);


    MamaCouponTradeDataDto findCouponTradeData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                               @Param("activityId") Long activityId);

    BigDecimal getShopCouponFeeData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                    @Param("activityId") Long activityId);

    BigDecimal getMamaCouponFeeData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                    @Param("activityId") Long activityId);

    List<MamaStoreSalesRevenueDataDto> findStoreSalesRevenueData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                                 @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                                                 @Param("activityId") Long activityId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    Long countStoreSalesRevenueData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                    @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                    @Param("activityId") Long activityId);

    BigDecimal totalFeeOfStoreSalesRevenueData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                               @Param("activityId") Long activityId);

    List<MamaCouponPaymentChannelDataDto> findPaymentChannelTradeData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                                      @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                                                      @Param("activityId") Long activityId, @Param("offset") Integer offset, @Param("limit") Integer limit);

    BigDecimal totalFeeOfPaymentChannelTradeData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                                 @Param("activityId") Long activityId);

    MamaCouponReceiveDataDto findCouponReceiveData(@Param("couponIds") Set<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                   @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId, @Param("shopIds") List<String> shopIds,
                                                   @Param("activityId") Long activityId);

    List<VendorTradeStatisticalDataBo> findVendorTradeStatisticalData(@Param("couponIds") List<Long> couponIds, @Param("startDate") LocalDateTime startDate,
                                                                      @Param("endDate") LocalDateTime endDate, @Param("shopId") String shopId);

    List<VendorTradeGetBuyerIdDto> getBuyers(@Param("shopId") String shopId, @Param("statusCodeList") List<String> statusCodeList);

    MamaCouponTradeDataTopDto getCouponTradeDataTopTotal(@Param("endDateTime") LocalDateTime endDateTime, @Param("typeNum") int typeNum);

    IPage<TradDistributorDto> searchTradDistributorList(@Param("pageParam") Page<TradDistributorDto> pageParam,
                                                        @Param("tradIds") List<String> tradIds);

    BigDecimal getDistributeTotalFeeSum(@Param("lastLoginShopId") String lastLoginShopId, @Param("countTradeStatus") List<String> countTradeStatus);

    Integer getDistributeTradeCount(@Param("lastLoginShopId") String lastLoginShopId, @Param("countTradeStatus") List<String> countTradeStatus);


    Page<MamaDistributorTradeDto> searchDistributorTrades(@Param("pageParam") Page<MamaDistributorTradeDto> pageParam,
                                                          @Param("mamaDistributorTradeSearchVo") MamaDistributorTradeSearchVo mamaDistributorTradeSearchVo);

    Page<MamaDistributorTradeDto> searchDistributorTradesALL(@Param("pageParam") Page<MamaDistributorTradeDto> pageParam,
                                                             @Param("mamaDistributorTradeSearchVo") MamaDistributorTradeSearchVo mamaDistributorTradeSearchVo);

    /**
     * 统计分析分销员订单积分
     *
     * @param param 分销员用户ID及订单状态
     * @return 分销员用户ID及积分
     */
    List<TradeStatisticsDTO> orderStatistics(@Param("param") TradeStatisticsParam param);

    TradeAndPointTotalVo.TotalData totalTradeAndPoint();

    List<TradeAndPointTotalVo.TotalData> totalTradeAndPointListFromCd(LocalDateTime startTime, LocalDateTime endTime);

    Page<MamaShopOrderDto> searchPointTradeALL(@Param("pageDtoPage") Page<MamaShopOrderDto> pageDtoPage,
                                               @Param("mamaShopOrderSearchVo") MamaShopOrderSearchVo mamaShopOrderSearchVo);

    /**
     * 店铺已售兑酒券数量
     * @param shopIds
     * @return
     */
    List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(@Param("shopIds") List<String> shopIds);
}
