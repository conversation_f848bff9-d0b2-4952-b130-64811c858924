package com.bamboocloud.cdp.sale.v2.dao.infra.impl;

import com.bamboocloud.cdp.boot.user.common.bo.vendor.LoginVendorBo;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.common.dto.buyer.product.category.BuyerProductCategoryDto;
import com.bamboocloud.cdp.sale.common.dto.mama.product.MamaProductDto;
import com.bamboocloud.cdp.sale.sdk.constant.ProductConstant;
import com.bamboocloud.cdp.sale.sdk.domain.bo.MamaProductAuctionInfoBo;
import com.bamboocloud.cdp.sale.sdk.domain.bo.MamaProductInfoBo;
import com.bamboocloud.cdp.sale.sdk.enums.ProductStatus4FixedPriceEnum;
import com.bamboocloud.cdp.sale.sdk.enums.ProductTypeCodeEnum;
import com.bamboocloud.cdp.sale.v2.dao.entity.*;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductAuctionInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductExhibitionInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductFixedPriceInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductInfraService;
import com.bamboocloud.cdp.sale.v2.dao.mapper.IProductMapper;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class ProductInfraServiceImpl extends ServiceImpl<IProductMapper, ProductEntity> implements IProductInfraService {
    @Autowired
    private ProductFileInfraServiceImpl productFileInfraService;

    @Autowired
    private IProductAuctionInfraService productAuctionInfraService;
    @Autowired
    private IProductExhibitionInfraService productExhibitionInfraService;
    @Autowired
    private IProductFixedPriceInfraService productFixedPriceInfraService;
    @Autowired
    private IProductMapper productMapper;

    @Override
    public List<ProductEntity> getCivilList(String shopId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getShopId,shopId);
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProduct(List<String> ids, Integer stockQuantity, LoginVendorBo loginVendorBo) {
        for (String id : ids) {
            LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(ProductEntity::getId,id);
            queryWrapper.set(ProductEntity::getUpdatedDate, LocalDateTime.now());
            queryWrapper.set(ProductEntity::getUpdatedUserId,loginVendorBo.getId());
            queryWrapper.set(ProductEntity::getUpdatedUserName,loginVendorBo.getName());
            queryWrapper.set(ProductEntity::getUpdatedUserNickName,loginVendorBo.getNickName());
            queryWrapper.set(ProductEntity::getUpdatedUserType,loginVendorBo.getUserTypeCode());
            queryWrapper.set(!ObjectUtils.isEmpty(stockQuantity),ProductEntity::getStockQuantity,stockQuantity);
            queryWrapper.setSql("version=version+1");
            this.update(queryWrapper);
        }
    }

    @Override
    public List<ProductEntity> findAllByIdIn(List<String> ids) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductEntity::getId,ids);
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProductCateGoryId(Integer sourceId, Integer targetId) {
        LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ProductEntity::getProductCategoryId,sourceId);
        queryWrapper.set(ProductEntity::getProductCategoryId, targetId);
        queryWrapper.setSql("version=version+1");
        this.update(queryWrapper);
    }

    @Override
    public List<MamaProductDto> listByProductCategoryId(Integer productCategoryId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getProductCategoryId,productCategoryId);
        queryWrapper.eq(ProductEntity::isDeleted,false);
        List<ProductEntity> entities=this.list(queryWrapper);
        return entities.stream().map(o-> new MamaProductDto(o.getId())).collect(Collectors.toList());
    }

    @Override
    public MamaProductDto getFirstByProductCategoryId(Integer productCategoryId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getProductCategoryId,productCategoryId);
        queryWrapper.eq(ProductEntity::isDeleted,false).last("limit 1 ");
        ProductEntity entity=this.getOne(queryWrapper);
        if (entity == null) {
            return null;
        }
        return new MamaProductDto(entity.getId());
    }

    @Override
    public Integer countByProductCategoryId(Integer productCategoryId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getProductCategoryId,productCategoryId);
        return Integer.parseInt(String.valueOf(this.count(queryWrapper)));
    }

    @Override
    public String getFileUrl(String productId) {
        LambdaQueryWrapper<ProductFileEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductFileEntity::getProductId,productId).last("limit 1 ");
        ProductFileEntity entity=productFileInfraService.getOne(queryWrapper);
        return ObjectUtils.isEmpty(entity)?null:entity.getFileUrl();
    }

    @Override
    public String getNameById(String id) {
        ProductEntity entity=this.getById(id);
        return ObjectUtils.isEmpty(entity)?null:entity.getName();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateProductAuctionForOffShow(String productId, String statusCode) {
        LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ProductEntity::getId,productId);
        queryWrapper.set(ProductEntity::getOnShow, false);
        queryWrapper.set(ProductEntity::getShowEndDate, LocalDateTime.now());
        queryWrapper.setSql("version=version+1");
        this.update(queryWrapper);
        updateProductAuctionStatus(productId, statusCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductAuctionStatus(String productId, String statusCode) {
        LambdaUpdateWrapper<ProductAuctionEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ProductAuctionEntity::getProductId,productId);
        queryWrapper.set(ProductAuctionEntity::getStatusCode, statusCode);
        productAuctionInfraService.update(queryWrapper);
    }

    @Override
    public List<MamaProductInfoBo> listByOnShowAndTypeCodeAndShopId(boolean onShow, String typeCode, String shopId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getOnShow,onShow);
        queryWrapper.eq(FwkStringUtil.isNotBlank(typeCode),ProductEntity::getTypeCode,typeCode);
        queryWrapper.eq(FwkStringUtil.isNotBlank(shopId),ProductEntity::getShopId,shopId);
        List<ProductEntity> entities=this.list(queryWrapper);
        return entities.stream().map(o-> new MamaProductInfoBo( o.getId(),  o.getTypeCode(),  o.getPredefinedShowStartDate())).collect(Collectors.toList());
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusForOnShow(List<MamaProductInfoBo> mamaProductInfoBos, boolean onShow) {
        if (CollectionUtils.isEmpty(mamaProductInfoBos)) {
            return;
        }
        String typeExhibition = ProductTypeCodeEnum.EXHIBITION.getCode();
        String typeAuction = ProductTypeCodeEnum.AUCTION.getCode();
        String typeFixedPrice = ProductTypeCodeEnum.FIXED_PRICE.getCode();
        for (MamaProductInfoBo mamaProductInfoBo : mamaProductInfoBos) {
            String typeCode = mamaProductInfoBo.getTypeCode();
            String productId = mamaProductInfoBo.getProductId();
            LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
            queryWrapper.eq(ProductEntity::getId,productId);
            queryWrapper.set(ProductEntity::getOnShow, onShow);
            queryWrapper.set(ProductEntity::getShowStartDate, onShow ? LocalDateTime.now() : mamaProductInfoBo.getShowStartDate());
            queryWrapper.set(ProductEntity::getPredefinedShowStartDate, onShow ? null : mamaProductInfoBo.getPredefinedShowStartDate());
            queryWrapper.set(ProductEntity::getShowEndDate, onShow ? mamaProductInfoBo.getShowEndDate() : LocalDateTime.now());
            queryWrapper.setSql("version=version+1");
            this.update(queryWrapper);
            if (typeExhibition.equals(typeCode)) {
                updateProductExhibitionStatus(productId, onShow ? ProductConstant.PRODUCT_EXHIBITION_SHOW.getCode() :
                        ProductConstant.PRODUCT_EXHIBITION_STORE.getCode());
            }
            if (typeFixedPrice.equals(typeCode)) {
                updateProductFixedPriceStatus(productId, onShow ? ProductStatus4FixedPriceEnum.SALE_ONGOING.getCode() :
                        ProductStatus4FixedPriceEnum.STORE.getCode());
            }
            if (typeAuction.equals(typeCode)) {
                updateProductAuctionStatus(productId, onShow ? ProductConstant.PRODUCT_AUCTION_SALE_SHOW.getCode() :
                        ProductConstant.PRODUCT_AUCTION_STORE.getCode());
            }
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductExhibitionStatus(String productId, String statusCode) {
        LambdaUpdateWrapper<ProductExhibitionEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ProductExhibitionEntity::getProductId,productId);
        queryWrapper.set(ProductExhibitionEntity::getStatusCode, statusCode);
        productExhibitionInfraService.update(queryWrapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductFixedPriceStatus(String productId, String statusCode) {
        LambdaUpdateWrapper<ProductFixedPriceEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(ProductFixedPriceEntity::getProductId,productId);
        queryWrapper.set(ProductFixedPriceEntity::getStatusCode, statusCode);
        productFixedPriceInfraService.update(queryWrapper);
    }

    @Override
    public List<MamaProductAuctionInfoBo> listProductAuctionByProductIds(List<String> productIds) {
        LambdaQueryWrapper<ProductAuctionEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProductAuctionEntity::getProductId,productIds);
        List<ProductAuctionEntity> entities=productAuctionInfraService.list(queryWrapper);
        return entities.stream().map(o-> new MamaProductAuctionInfoBo( o.getProductId(),  o.getStartDate(),  o.getEndDate(),o.getStartPrice(),o.getCurrentPrice())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<String> ids, LoginVendorBo loginVendorBo) {
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.in(ProductEntity::getId,ids);
        queryWrapper.set(ProductEntity::isDeleted,true);
        queryWrapper.set(ProductEntity::getDeletedDate,LocalDateTime.now());
        queryWrapper.set(ProductEntity::getDeletedUserId,loginVendorBo.getId());
        queryWrapper.set(ProductEntity::getDeletedUserName,loginVendorBo.getName());
        queryWrapper.set(ProductEntity::getDeletedUserNickName,loginVendorBo.getNickName());
        queryWrapper.set(ProductEntity::getDeletedUserType,loginVendorBo.getUserTypeCode());
        queryWrapper.setSql("version=version+1");
        this.update(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProductShowStatus(List<String> ids, Boolean onShow) {
        if(CollectionUtils.isEmpty(ids)){
            return;
        }
        LambdaUpdateWrapper<ProductEntity> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.in(ProductEntity::getId,ids);
        queryWrapper.set(ProductEntity::getOnShow,onShow);
        queryWrapper.set(onShow,ProductEntity::getShowStartDate,LocalDateTime.now());
        queryWrapper.set(onShow,ProductEntity::getPredefinedShowStartDate,null);
        queryWrapper.set(!onShow,ProductEntity::getShowEndDate,LocalDateTime.now());
        queryWrapper.setSql("version=version+1");
        this.update(queryWrapper);
    }

    @Override
    public long countByShopIdAndStatusCode(String shopId, String statusCode) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FwkStringUtil.isNotBlank(shopId),ProductEntity::getShopId,shopId);
        queryWrapper.eq(ProductEntity::getStatusCode,statusCode);
        queryWrapper.eq(ProductEntity::isDeleted,false);
        return this.count(queryWrapper);
    }

    @Override
    public ProductEntity findFirstByExposedId(String exposedId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getExposedId,exposedId)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public List<Integer> getAllProductCategory(BuyerProductCategoryDto dto) {
        return productMapper.getAllProductCategory(dto);
    }

    @Override
    public List<ProductEntity> findByShopIdAndDistribute(String lastLoginShopId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getShopId, lastLoginShopId)
            .eq(ProductEntity::getDistribute, true);
        return this.list(queryWrapper);
    }

    @Override
    public long countByShopIdAndDistribute(String lastLoginShopId) {
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductEntity::getShopId, lastLoginShopId)
            .eq(ProductEntity::getDistribute, true);
        return this.count(queryWrapper);
    }

    @Override
    public List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds) {
        return this.baseMapper.getAlcoholRedemptionCount(shopIds);
    }
}
