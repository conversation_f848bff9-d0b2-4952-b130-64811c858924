/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductService.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.everyone.product.service;

import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description: 商品
 */
public interface ProductService {

    /**
     * 查看
     *
     * @param id
     * @return
     */
    ProductEntity get(String id);

    /**
     * 修改
     *
     * @param product
     */
    void update(ProductEntity product);

    /**
     * 兑酒券统计
     * @param shopIds
     * @return
     */
    List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds);
}
