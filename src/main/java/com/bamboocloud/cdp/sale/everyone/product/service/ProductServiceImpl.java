/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductServiceImpl.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.everyone.product.service;

import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.sale.v2.dao.infra.IProductInfraService;
import com.bamboocloud.cdp.sale.v2.dao.infra.ITradeInfraService;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Autowired
    private IProductInfraService productInfraService;
    @Autowired
    private ITradeInfraService tradeInfraService;

    @Override
    public ProductEntity get(String id) {
        return productInfraService.getOptById(id).orElseThrow();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductEntity product) {
        productInfraService.updateById(product);
    }

    @Override
    public List<AlcoholRedemptionCountRes> getAlcoholRedemptionCount(List<String> shopIds) {
        //库存占用的兑酒券
        List<AlcoholRedemptionCountRes> stockCounts = this.productInfraService.getAlcoholRedemptionCount(shopIds);
        List<AlcoholRedemptionCountRes> saleCountS = this.tradeInfraService.getAlcoholRedemptionCount(shopIds);
        return List.of();
    }

}
