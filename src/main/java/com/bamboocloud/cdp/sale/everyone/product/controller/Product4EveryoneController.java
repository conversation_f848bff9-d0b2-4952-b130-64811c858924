/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: ProductController.java
 * @createdDate: 2022/07/26 13:56:26
 *
 */

package com.bamboocloud.cdp.sale.everyone.product.controller;

import com.bamboocloud.cdp.boot.security.annotation.Inner;
import com.bamboocloud.cdp.boot.user.common.bo.base.BaseLoginUserBo;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.base.controller.FwkBaseController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.sale.common.dto.everyone.product.category.ProductCategoryDto.SearchReq;
import com.bamboocloud.cdp.sale.common.dto.mama.product.category.MamaProductCategoryPageDto;
import com.bamboocloud.cdp.sale.common.exception.ExceptionCode4BuyerEnum;
import com.bamboocloud.cdp.sale.common.util.BeanHelper;
import com.bamboocloud.cdp.sale.common.vo.everyone.product.EveryoneProductUpdateVo;
import com.bamboocloud.cdp.sale.everyone.product.service.ProductService;
import com.bamboocloud.cdp.sale.mama.product.service.MamaProductCategoryService;
import com.bamboocloud.cdp.sale.sdk.constant.EveryoneRouteConstant;
import com.bamboocloud.cdp.sale.sdk.enums.ProductTypeCodeEnum;
import com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.BuyerSimpleDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto.AlcoholRedemptionCountRes;
import com.bamboocloud.cdp.user.sdk.feign.IntegrationUserService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 通用/sale-api/商品接口
 * <AUTHOR> Mo
 * @description:
 */
@Slf4j
@RestController
public class Product4EveryoneController extends FwkBaseController {

    @Autowired
    private ProductService productService;
    @Autowired
    private CacheConstant cacheConstant;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private BaseService baseService;
    @Autowired
    private MamaProductCategoryService mamaProductCategoryService;
    @Autowired
    private IntegrationUserService integrationUserService;

    /**
     * 获取商品类型常量
     *
     * @return
     */
    @GetMapping(EveryoneRouteConstant.EVERYONE_PRODUCT_LIST_TYPE_CODE_V1)
    public FwkApiResponse<List<BaseTypeDto>> listTypeCodes() {
        log.debug("ProductController - listTypeCodes");
        List<BaseTypeDto> list = Arrays.stream(ProductTypeCodeEnum.values())
                .map(item -> new BaseTypeDto(item.getCode(), item.getMsg()))
                .toList();
        return FwkApiResponse.success(list);
    }

    /**
     * 修改
     *
     * @param everyoneProductUpdateVo
     * @return
     */
    @PutMapping(EveryoneRouteConstant.EVERYONE_PRODUCT_UPDATE_V1)
    public FwkApiResponse<String> update(@Validated @RequestBody EveryoneProductUpdateVo everyoneProductUpdateVo) throws InterruptedException {
        log.debug("ProductController - update");
        ProductEntity product = productService.get(everyoneProductUpdateVo.getId());
        String keyProductLock = cacheConstant.getKeyProductLock(product.getId());
        RLock lock = redissonClient.getLock(keyProductLock);
        boolean tryLock = lock.tryLock(10, TimeUnit.SECONDS);
        if (tryLock) {
            try {
                BeanHelper.copyProperties(product,everyoneProductUpdateVo);
                productService.update(product);
            } catch (Exception exception) {
                log.error("exception", exception);
                throw exception;
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("ProductController - update - tryLock - false" + LocalDateTime.now());
            throw new BusinessException(ExceptionCode4BuyerEnum.THE_SYSTEM_IS_TOO_BUSY);
        }
        return FwkApiResponse.success();
    }

    /**
     * 查询商品种类 -含有商品的
     *
     * @return
     */
    @PostMapping(EveryoneRouteConstant.EVERYONE_PRODUCT_CATEGORY_SEARCH_OWNED_PRODUCT_V1)
    public FwkApiResponse<MamaProductCategoryPageDto> searchOwnedProduct(@RequestBody @Validated SearchReq req) {
        log.debug("MamaProductCategoryController - searchOwnedProduct");
        BuyerSimpleDto buyerSimpleDto = null;
        try {
            BaseLoginUserBo loginUser = baseService.getLoginUser();
            if (loginUser instanceof LoginBuyerBo) {
                buyerSimpleDto = integrationUserService.buyerGetBuyerSimpleById(loginUser.getId()).getData();
                req.setBuyerSimpleDto(buyerSimpleDto);
            }
        } catch (Exception ignore) {
            log.debug("searchOwnedProduct-商品种类列表api无登录");
        }
        return FwkApiResponse.success(mamaProductCategoryService.searchOwnedProduct(req));
    }

    /**
     * feign查询店铺兑酒券统计
     * @param shopIds
     * @return
     */
    @Inner
    @PostMapping(EveryoneRouteConstant.EVERYONE_SHOP_ALCOHOL_REDEMPTION_COUNT)
    public FwkApiResponse<List<AlcoholRedemptionCountRes>> getAlcoholRedemptionCount(@RequestBody List<String> shopIds) {
        List<AlcoholRedemptionCountRes> res = this.productService.getAlcoholRedemptionCount(shopIds);
        return null;
    }
}
