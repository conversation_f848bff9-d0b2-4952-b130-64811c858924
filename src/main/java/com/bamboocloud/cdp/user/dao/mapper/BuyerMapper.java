package com.bamboocloud.cdp.user.dao.mapper;

import com.bamboocloud.cdp.user.dao.entity.BuyerEntity;
import com.bamboocloud.cdp.user.sdk.domain.param.DistUserParam;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/30  17:05
 */
@Mapper
public interface BuyerMapper extends BaseMapper<BuyerEntity> {

    /**
     * 分销达人邀请的新用户列表
     *
     * @param page  分页数据
     * @param param 查询参数
     * @return 用户列表
     */
    IPage<DistUserVO> page(IPage<?> page, @Param("param") DistUserParam param);

}
