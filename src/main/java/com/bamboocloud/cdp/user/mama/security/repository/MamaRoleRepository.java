/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaRoleRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.security.repository;


import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.mama.MamaRole;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface MamaRoleRepository extends FwkBaseRepository<MamaRole, Integer> {
    MamaRole findByName(String name);

    Mama<PERSON><PERSON> findByNameAndIdNot(String name, Integer id);
}
