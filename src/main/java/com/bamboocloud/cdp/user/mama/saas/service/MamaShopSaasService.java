/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaShopSaasService.java
 * @createdDate: 2023/04/18 11:25:18
 *
 */

package com.bamboocloud.cdp.user.mama.saas.service;

import com.bamboocloud.cdp.user.common.constant.ShopSaasConstant;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasLoginInfoDto;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasTrueInfoDto;
import com.bamboocloud.cdp.user.common.entity.vendor.saas.ShopSaas;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasCheckVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasLoginVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasSearchVo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MamaShopSaasService {


    /**
     * 分页查询
     * @param mamaShopSaasSearchVo
     * @return
     */
    MamaShopSaasPageDto search(MamaShopSaasSearchVo mamaShopSaasSearchVo);


    /**
     * 状态集合
     * @return
     */
    List<ShopSaasConstant> statusList();


    /**
     * 查看资料
     * @param shopId
     * @return
     */
    MamaShopSaasTrueInfoDto getByShopId(String shopId);

    /**
     * 审核
     * @param mamaShopSaasCheckVos
     */
    void bulkCheckSaas(List<MamaShopSaasCheckVo> mamaShopSaasCheckVos);

    /**
     * 录入账号，修改账号
     * @param mamaShopSaasLoginVo
     * @return
     */
    void updateLoginSaas(MamaShopSaasLoginVo mamaShopSaasLoginVo);

    /**
     * 查看录入的账号信息
     * @param id
     * @return
     */
    MamaShopSaasLoginInfoDto get(Long id);

    /**
     * 创建
     * @param shopSaas
     * @return
     */
    ShopSaas create(ShopSaas shopSaas);
}
