/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopVendorService.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.shop.service;

import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopVendorNickNameDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopVendorPageDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopVendorGetByShopIdAndVendorIdVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopVendorSearchVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopVendorUpdateEnabledVo;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.vendor.MamaVendorDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopVendor;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface MamaShopVendorService {
    /**
     * 新增
     *
     * @param shopVendor
     * @return
     */
    ShopVendor create(ShopVendor shopVendor);

    /**
     * 账号列表
     *
     * @param mamaShopVendorSearchVo
     * @return
     */
    MamaShopVendorPageDto search(MamaShopVendorSearchVo mamaShopVendorSearchVo);

    /**
     * 通过店铺Id和是否为管理员获得
     *
     * @param shopId
     * @param admin
     * @return
     */
    MamaVendorDto getVendorByShopIdAndAdmin(String shopId, boolean admin);

    /**
     * 查看昵称（根据店铺Id和商户id）
     *
     * @param mamaShopVendorGetByShopIdAndVendorIdVo
     * @return
     */
    MamaShopVendorNickNameDto getNickNameByShopIdAndVendorId(MamaShopVendorGetByShopIdAndVendorIdVo mamaShopVendorGetByShopIdAndVendorIdVo);

    /**
     * 发送短信给商家
     *
     * @param shop
     */
    void sendShopStatusSmsForShopVendorAdmin(Shop shop);

    /**
     * 查询数量根据vendorId和roleId
     *
     * @param vendorId
     * @param roleId
     * @return
     */
    List<Long> listCountByVendorIdAndRoleId(String vendorId, Integer roleId);

    /**
     * 启用/停用店员
     *
     * @param mamaShopVendorUpdateEnabledVo
     */
    ShopVendor updateEnabled(MamaShopVendorUpdateEnabledVo mamaShopVendorUpdateEnabledVo);
}
