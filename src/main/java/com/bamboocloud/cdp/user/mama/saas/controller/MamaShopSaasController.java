/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopAddressController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.saas.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.constant.ShopSaasConstant;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasLoginInfoDto;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shopsaas.MamaShopSaasTrueInfoDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasCheckVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasLoginVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.saas.MamaShopSaasSearchVo;
import com.bamboocloud.cdp.user.config.property.AppProperty;
import com.bamboocloud.cdp.user.mama.saas.service.MamaShopSaasService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.util.RsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@Slf4j
public class MamaShopSaasController extends BaseMamaController {

    @Autowired
    private MamaShopSaasService mamaShopSaasService;

    @Autowired
    private AppProperty appProperty;

    /**
     * 分页查询
     *
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SAAS_APPLICATION_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_SHOP_SAAS_SEARCH_V1)
    public FwkApiResponse<MamaShopSaasPageDto> search(@Validated @RequestBody MamaShopSaasSearchVo mamaShopSaasSearchVo) {
        log.debug("VendorShopMallController -- getSaasApplicationInfo");
        MamaShopSaasPageDto search = mamaShopSaasService.search(mamaShopSaasSearchVo);
        return FwkApiResponse.success(search);
    }

    /**
     * 查看资料
     *
     * @param shopId
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SAAS_APPLICATION_GET.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_SHOP_SAAS_GET_V1)
    public FwkApiResponse<MamaShopSaasTrueInfoDto> getByShopId(@PathVariable String shopId) {
        log.debug("VendorShopController - getByShopId");
        MamaShopSaasTrueInfoDto byShopId = mamaShopSaasService.getByShopId(shopId);
        return FwkApiResponse.success(byShopId);
    }

    /**
     * 批量审核
     *
     * @param mamaShopSaasCheckVos
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SAAS_APPLICATION_APPROVE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_SHOP_SAAS_BULK_UPDATE_V1)
    public FwkApiResponse<String> bulkUpdate(@RequestBody List<MamaShopSaasCheckVo> mamaShopSaasCheckVos) {
        log.debug("VendorShopController - bulkUpdate");
        mamaShopSaasService.bulkCheckSaas(mamaShopSaasCheckVos);
        return FwkApiResponse.success();
    }

    /**
     * 查看状态集合
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_SHOP_SAAS_GET_STATUS_LIST_V1)
    public FwkApiResponse<List<ShopSaasConstant>> getStatusList() {
        log.debug("VendorShopController - getByShopId");
        List<ShopSaasConstant> shopSaasConstants = mamaShopSaasService.statusList();
        return FwkApiResponse.success(shopSaasConstants);
    }

    /**
     * 查看录入的账号信息
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SAAS_APPLICATION_GET.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_SHOP_SAAS_GET_ACCOUNT_INFO_V1)
    public FwkApiResponse<MamaShopSaasLoginInfoDto> getAccountInfo(@PathVariable Long id) {
        log.debug("VendorShopController - getAccountInfo");
        MamaShopSaasLoginInfoDto mamaShopSaasLoginInfoDto = mamaShopSaasService.get(id);
        if (FwkStringUtil.isNotBlank(mamaShopSaasLoginInfoDto.getAccount())) {
            mamaShopSaasLoginInfoDto.setAccount(RsaUtils.encryptByPrivateKey(mamaShopSaasLoginInfoDto.getAccount(), appProperty.getRsa().getPrivateKey()));
        }
        if (FwkStringUtil.isNotBlank(mamaShopSaasLoginInfoDto.getPassword())) {
            mamaShopSaasLoginInfoDto.setPassword(RsaUtils.encryptByPrivateKey(mamaShopSaasLoginInfoDto.getPassword(), appProperty.getRsa().getPrivateKey()));
        }
        return FwkApiResponse.success(mamaShopSaasLoginInfoDto);
    }

    /**
     * 修改和录入账号信息
     *
     * @param mamaShopSaasLoginVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SAAS_APPLICATION_UPDATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_SHOP_SAAS_UPDATE_ACCOUNT_INFO_V1)
    public FwkApiResponse<String> updateAccountInfo(@Validated @RequestBody MamaShopSaasLoginVo mamaShopSaasLoginVo) {
        log.debug("VendorShopController - updateAccountInfo");
        mamaShopSaasService.updateLoginSaas(mamaShopSaasLoginVo);
        return FwkApiResponse.success();
    }


}
