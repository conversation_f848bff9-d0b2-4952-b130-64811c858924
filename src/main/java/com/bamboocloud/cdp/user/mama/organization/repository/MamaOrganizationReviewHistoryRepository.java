/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaOrganizationReviewHistoryRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.organization.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.OrganizationReviewHistory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface MamaOrganizationReviewHistoryRepository extends FwkBaseRepository<OrganizationReviewHistory, Integer> {
    OrganizationReviewHistory findFirstByOrganizationIdAndReviewStatusCodeNotOrderByReviewUpdatedDateDesc(String organizationId,
                                                                                                          String reviewStatusCode);

    OrganizationReviewHistory findFirstByOrganizationIdOrderByBatchIdDesc(String organizationId);

    List<OrganizationReviewHistory> findAllByOrganizationIdOrderByReviewUpdatedDateDesc(String organizationId);
}
