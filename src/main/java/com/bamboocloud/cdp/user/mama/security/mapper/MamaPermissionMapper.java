/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaPermissionMapper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.security.mapper;

import com.bamboocloud.cdp.user.common.dto.mama.permission.MamaPermissionModuleDto;
import com.bamboocloud.cdp.user.common.entity.mama.permission.MamaPermissionModule;
import org.mapstruct.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Mapper
@Component
public interface MamaPermissionMapper {
    List<MamaPermissionModuleDto> toModuleDtos(List<MamaPermissionModule> mamaPermissionModules);
}
