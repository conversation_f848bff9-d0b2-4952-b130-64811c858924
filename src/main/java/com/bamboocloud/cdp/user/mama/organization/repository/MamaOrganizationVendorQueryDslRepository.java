/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorOrganizationVendorQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.organization.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.bo.vendor.VendorSimpleBo;
import com.bamboocloud.cdp.user.sdk.constant.OrganizationConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QOrganization;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QOrganizationVendor;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class MamaOrganizationVendorQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    @Autowired
    private MamaOrganizationQueryDslRepository mamaOrganizationQueryDslRepository;

    public Integer countByVendorNameAndDefaultAdminAndOrganizationTypeCode(String vendorName, boolean defaultAdmin, String organizationTypeCode) {
        BooleanBuilder builder = new BooleanBuilder();
        QOrganization qOrganization = QOrganization.organization;
        QOrganizationVendor qOrganizationVendor = QOrganizationVendor.organizationVendor;
        builder.and(qOrganizationVendor.defaultAdmin.eq(defaultAdmin));
        if (FwkStringUtil.isNotBlank(vendorName)) {
            builder.and(qOrganizationVendor.vendor.name.eq(vendorName));
        }
        builder.and(qOrganization.typeCode.eq(organizationTypeCode));
        builder.and(qOrganization.statusCode.eq(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));
        long count = blazeJpaQueryFactory.select(qOrganizationVendor.id)
                .from(qOrganizationVendor)
                .leftJoin(qOrganization).on(qOrganization.id.eq(qOrganizationVendor.organizationId))
                .where(builder).fetchCount();
        return (int) count;
    }

    public List<String> listVendorIdsByOrganizationTypeCode(String organizationTypeCode) {
        BooleanBuilder builder = new BooleanBuilder();
        QOrganizationVendor qOrganizationVendor = QOrganizationVendor.organizationVendor;
        builder.and(qOrganizationVendor.admin.isTrue());
        List<String> organizationIds = mamaOrganizationQueryDslRepository.listIdsByTypeCode(organizationTypeCode);
        builder.and(qOrganizationVendor.organizationId.in(organizationIds));
        return queryFactory.select(qOrganizationVendor.vendorId)
                .from(qOrganizationVendor)
                .where(builder)
                .fetch();
    }

    public VendorSimpleBo getVendorSimpleBoByOrganizationIdAndDefaultAdmin(String organizationId, boolean defaultAdmin) {
        BooleanBuilder builder = new BooleanBuilder();
        QOrganizationVendor qOrganizationVendor = QOrganizationVendor.organizationVendor;
        if (FwkStringUtil.isNotBlank(organizationId)) {
            builder.and(qOrganizationVendor.organizationId.eq(organizationId));
        }
        builder.and(qOrganizationVendor.defaultAdmin.eq(defaultAdmin));
        return queryFactory.select(Projections.constructor(
                        VendorSimpleBo.class,
                        qOrganizationVendor.vendor.name,
                        qOrganizationVendor.vendor.mobile,
                        qOrganizationVendor.vendor.certNumber))
                .from(qOrganizationVendor).where(builder).fetchFirst();
    }
}
