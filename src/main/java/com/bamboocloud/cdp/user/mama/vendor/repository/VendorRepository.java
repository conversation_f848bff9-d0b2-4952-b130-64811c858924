/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.vendor.repository;


import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.Vendor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Repository
public interface VendorRepository extends FwkBaseRepository<Vendor, String> {
    Vendor findByMobileAndDeletedIsFalse(String mobile);

    Vendor findByWxMiniOpenId(String openId);

    Vendor findByEmail(String email);

    Vendor findByWxUnionId(String wxUnionId);

    Vendor findFirstByAppleId(String appleId);
}
