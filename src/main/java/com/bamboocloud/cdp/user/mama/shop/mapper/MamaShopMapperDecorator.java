/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMapperDecorator.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.shop.mapper;

import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.pay.sdk.common.constant.WxOrganizationConstant;
import com.bamboocloud.cdp.pay.sdk.common.dto.wx.organization.OrganizationCompanyCreationApplyDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.wx.organization.OrganizationLegalPersonCreationApplyDto;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.shop.ShopBankCreationApplyVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.shop.ShopCreationApplyVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.shop.ShopOrganizationCreationApplyVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.wx.shop.ShopVendorCreationApplyVo;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDto;
import com.bamboocloud.cdp.user.mama.organization.service.MamaOrganizationBankService;
import com.bamboocloud.cdp.user.mama.organization.service.MamaOrganizationVendorService;
import com.bamboocloud.cdp.user.mama.shop.service.*;
import com.bamboocloud.cdp.user.sdk.constant.OrganizationConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.Vendor;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.*;
import com.bamboocloud.cdp.user.sdk.enums.ShopStatusCodeEnum;
import com.bamboocloud.cdp.user.sdk.util.DesUtils;
import com.bamboocloud.cdp.user.vendor.constant.VendorConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.ObjectUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public abstract class MamaShopMapperDecorator implements MamaShopMapper {

    @Autowired
    @Lazy
    private MamaShopService mamaShopService;

    @Autowired
    private MamaOrganizationVendorService mamaOrganizationVendorService;

    @Autowired
    private MamaShopBuyerService mamaShopBuyerService;

    @Autowired
    private MamaShopWxReviewHistoryService mamaShopWxReviewHistoryService;
    @Autowired
    private MamaShopCategoryService mamaShopCategoryService;
    @Autowired
    private MamaOrganizationBankService mamaOrganizationBankService;
    @Autowired
    private MamaOrganizationBankShopService mamaOrganizationBankShopService;

    @Autowired
    private DesUtils desUtils;

    @Autowired
    @Qualifier("delegate")
    private MamaShopMapper delegate;

    @Override
    public MamaShopDto toDto(Shop shop) {
        if (shop == null) {
            return null;
        }
        MamaShopDto mamaShopDto = delegate.toDto(shop);
        ShopStatusCodeEnum shopStatusCodeEnum = ShopStatusCodeEnum.get(shop.getStatusCode());
        mamaShopDto.setStatus(new BaseTypeDto(shop.getStatusCode(), ObjectUtils.isEmpty(shopStatusCodeEnum) ? "" : shopStatusCodeEnum.getName()));
        mamaShopDto.setWxPayApplymentStatus(mamaShopService.generateBaseTypeDto(mamaShopService.listShopWxPayApplymentStatusTypes(), shop.getWxPayApplymentStatusCode()));
        mamaShopDto.setExtPayUserStatus(mamaShopService.generateBaseTypeDto(mamaShopService.listShopExtPayUserStatus(), shop.getExtPayUserStatusCode()));
        mamaShopDto.setFavoriteTotalCount(mamaShopBuyerService.countByShopIdAndFavorite(mamaShopDto.getId(), true));
        //ocr影印件状态
        mamaShopDto.setExtPayIDCardOcrStatus(mamaShopService.generateBaseTypeDto(mamaShopService.listExtPayIDCardOcrStatusTypes(), shop.getExtPayIDCardOcrStatusCode()));
        mamaShopDto.setExtPayIDCardOcrRejectReason(shop.getExtPayIDCardOcrRejectReason());
        OrganizationBank organizationBank = mamaOrganizationBankService.getByOrganizationId(shop.getOrganizationId());
        if (organizationBank !=null) {
            //商户绑定银行卡状态
            mamaShopDto.setAccountBindSuccess(organizationBank.getAccountBindSuccess());
            mamaShopDto.setAccountBindStatus(mamaShopService.generateAccountBindDto(organizationBank.getAccountBindSuccess()));


            List<OrganizationBankShop> organizationBankShops = mamaOrganizationBankShopService.listByShopIdAndOrganizationBankId(shop.getId(),organizationBank.getId());
            if (!FwkCollectionUtil.isEmpty(organizationBankShops)) {
                OrganizationBankShop organizationBankShop = organizationBankShops.get(0);
                //提现签约状态
                mamaShopDto.setExtPaySignStatusCode(organizationBankShop.getExtPaySignStatusCode());
                mamaShopDto.setExtPaySignStatus(
                    mamaShopService.generateBaseTypeDto(mamaShopService.listExtPaySignStatusTypes(), organizationBankShop.getExtPaySignStatusCode()));
            }
        }



        String wxReviewDocUrl = mamaShopWxReviewHistoryService.getFirstWxReviewDocUrlByShopIdAndWxReviewDocUrlIsNotNull(mamaShopDto.getId());
        if (FwkStringUtil.isNotBlank(wxReviewDocUrl)) {
            mamaShopDto.setWxReviewDocUrl(wxReviewDocUrl);
        }
        if(!ObjectUtils.isEmpty(shop.getShopCategoryId())){
            MamaShopCategoryDto mamaShopCategoryDto=mamaShopCategoryService.get(shop.getShopCategoryId());
            mamaShopDto.setShopCategoryName(ObjectUtils.isEmpty(mamaShopCategoryDto)?"":mamaShopCategoryDto.getName());
        }
        return mamaShopDto;
    }

    @Override
    public List<MamaShopDto> toDtos(List<Shop> shops) {
        if (shops == null) {
            return null;
        }

        List<MamaShopDto> list = new ArrayList<MamaShopDto>(shops.size());
        for (Shop shop : shops) {
            list.add(toDto(shop));
        }

        return list;
    }

    @Override
    public ShopOrganizationCreationApplyVo toShopOrganizationCreationApplyDto(Shop shop) throws Exception {
        if (shop == null) {
            return null;
        }

        ShopOrganizationCreationApplyVo shopOrganizationCreationApplyDto = new ShopOrganizationCreationApplyVo();

        shopOrganizationCreationApplyDto.setShopId(shop.getId());
        if (OrganizationConstant.ORGANIZATION_TYPE_COMPANY.getCode().equals(shop.getOrganization().getTypeCode())) {
            shopOrganizationCreationApplyDto.setOrganizationType(WxOrganizationConstant.ORGANIZATION_TYPE_COMPANY.getCode());
        } else if (OrganizationConstant.ORGANIZATION_TYPE_INDIVIDUAL.getCode().equals(shop.getOrganization().getTypeCode())) {
            shopOrganizationCreationApplyDto.setOrganizationType(WxOrganizationConstant.ORGANIZATION_TYPE_INDIVIDUAL.getCode());
        } else if (OrganizationConstant.ORGANIZATION_TYPE_COMPANY_SELF_EMPLOYED.getCode().equals(shop.getOrganization().getTypeCode())) {
            shopOrganizationCreationApplyDto.setOrganizationType(WxOrganizationConstant.ORGANIZATION_TYPE_COMPANY_SELF_EMPLOYED.getCode());
        } else if (OrganizationConstant.ORGANIZATION_TYPE_SOCIAL.getCode().equals(shop.getOrganization().getTypeCode())) {
            shopOrganizationCreationApplyDto.setOrganizationType(WxOrganizationConstant.ORGANIZATION_TYPE_SOCIAL.getCode());
        }
        OrganizationVendor organizationVendor = mamaOrganizationVendorService.getByOrganizationIdAndDefaultAdmin(shop.getId(), true);
        shopOrganizationCreationApplyDto.setOrganizationLegalPerson(organizationLegalPersonToOrganizationLegalPersonCreationApplyDto(shop.getOrganization().getOrganizationLegalPerson(), organizationVendor.getVendor()));
        shopOrganizationCreationApplyDto.setOrganizationCompany(organizationCompanyToOrganizationCompanyCreationApplyDto(shop.getOrganization().getOrganizationCompany(), shopOrganizationCreationApplyDto.getOrganizationLegalPerson().getName()));
        shopOrganizationCreationApplyDto.setOrganizationBank(organizationBankToShopBankCreationApplyDto(shop.getOrganization().getOrganizationBank()));
        shopOrganizationCreationApplyDto.setVendor(vendorToShopVendorCreationApplyDto(shop.getOrganization().getOrganizationLegalPerson(), organizationVendor.getVendor()));
        shopOrganizationCreationApplyDto.setShop(new ShopCreationApplyVo(shop.getName(), shop.getQrCodeUrl()));
        shopOrganizationCreationApplyDto.setMerchantShortname(shop.getName());
        return shopOrganizationCreationApplyDto;
    }

    protected ShopVendorCreationApplyVo vendorToShopVendorCreationApplyDto(OrganizationLegalPerson organizationLegalPerson, Vendor vendor) throws Exception {
        if (vendor == null) {
            return null;
        }
        if (organizationLegalPerson == null) {
            organizationLegalPerson = vendorToOrganizationLegalPerson(vendor);
            if (organizationLegalPerson == null) {
                return null;
            }
        }
        ShopVendorCreationApplyVo organizationVendorCreationApplyDto = new ShopVendorCreationApplyVo();
        organizationVendorCreationApplyDto.setVendorId(vendor.getId());
        organizationVendorCreationApplyDto.setContactType(WxOrganizationConstant.CONTACT_TYPE_PRINCIPAL.getCode());
        if (vendor.getCertNumber().equals(organizationLegalPerson.getCertNumber())) {
            organizationVendorCreationApplyDto.setContactType(WxOrganizationConstant.CONTACT_TYPE_LEGAL_PERSON.getCode());
        }
        organizationVendorCreationApplyDto.setName(vendor.getName());
        if (VendorConstant.VENDOR_CERT_TYPE_ID_CARD.getCode().equals(vendor.getCertTypeCode())) {
            organizationVendorCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_MAINLAND_IDCARD.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_INLAND_PASS_CERT.getCode().equals(vendor.getCertTypeCode())) {
            organizationVendorCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_HONGKONG.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_MAINLAND_PASS_CERT.getCode().equals(vendor.getCertTypeCode())) {
            organizationVendorCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_TAIWAN.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_PASSPORT.getCode().equals(vendor.getCertTypeCode())) {
            organizationVendorCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_OVERSEA_PASSPORT.getCode());
        }
        organizationVendorCreationApplyDto.setCertNumber(desUtils.decrypt(vendor.getCertNumber()));
        organizationVendorCreationApplyDto.setMobile(vendor.getMobile());
        organizationVendorCreationApplyDto.setEmail(vendor.getEmail());

        return organizationVendorCreationApplyDto;
    }

    protected ShopBankCreationApplyVo organizationBankToShopBankCreationApplyDto(OrganizationBank organizationBank) {
        if (organizationBank == null) {
            return null;
        }

        ShopBankCreationApplyVo vendorOrganizationBankCreationApplyDto = new ShopBankCreationApplyVo();

        if (VendorConstant.ORGANIZATION_BANK_TYPE_PRIVATE.getCode().equals(organizationBank.getTypeCode())) {
            vendorOrganizationBankCreationApplyDto.setTypeCode(WxOrganizationConstant.ORGANIZATION_BANK_TYPE_PRIVATE.getCode());
        }
        if (VendorConstant.ORGANIZATION_BANK_TYPE_PUBLIC.getCode().equals(organizationBank.getTypeCode())) {
            vendorOrganizationBankCreationApplyDto.setTypeCode(WxOrganizationConstant.ORGANIZATION_BANK_TYPE_PUBLIC.getCode());
        }
        vendorOrganizationBankCreationApplyDto.setAccountName(organizationBank.getAccountName());
        vendorOrganizationBankCreationApplyDto.setAccountNumber(organizationBank.getAccountNumber());
        vendorOrganizationBankCreationApplyDto.setBankName(organizationBank.getBankName());
        vendorOrganizationBankCreationApplyDto.setBankAddressCode(organizationBank.getWxBankCityCode());
        vendorOrganizationBankCreationApplyDto.setBankAddressName(organizationBank.getWxSearchBankCityName());
        vendorOrganizationBankCreationApplyDto.setBankBranchCode(organizationBank.getBankBranchCode());
        vendorOrganizationBankCreationApplyDto.setBankBranchName(organizationBank.getBankBranchName());

        return vendorOrganizationBankCreationApplyDto;
    }

    protected OrganizationCompanyCreationApplyDto organizationCompanyToOrganizationCompanyCreationApplyDto(OrganizationCompany organizationCompany, String organizationLegalPersonName) {
        if (organizationCompany == null) {
            return null;
        }

        OrganizationCompanyCreationApplyDto vendorOrganizationCompanyCreationApplyDto = new OrganizationCompanyCreationApplyDto();

        vendorOrganizationCompanyCreationApplyDto.setCertImageUrl(organizationCompany.getCertImageUrl());
        vendorOrganizationCompanyCreationApplyDto.setCertNumber(organizationCompany.getCertNumber());
        vendorOrganizationCompanyCreationApplyDto.setName(organizationCompany.getName());
        vendorOrganizationCompanyCreationApplyDto.setOrganizationLegalPersonName(organizationLegalPersonName);
        vendorOrganizationCompanyCreationApplyDto.setAddress(organizationCompany.getAddress());
        if (organizationCompany.getTermEndDate() != null) {
            vendorOrganizationCompanyCreationApplyDto.setTermEndDate(DateTimeFormatter.ISO_LOCAL_DATE.format(organizationCompany.getTermEndDate()));
        }

        return vendorOrganizationCompanyCreationApplyDto;
    }

    protected OrganizationLegalPerson vendorToOrganizationLegalPerson(Vendor vendor) throws Exception {
        if (vendor == null) {
            return null;
        }

        OrganizationLegalPerson organizationLegalPerson = new OrganizationLegalPerson();

        organizationLegalPerson.setName(vendor.getName());
        organizationLegalPerson.setCertTypeCode(vendor.getCertTypeCode());
        organizationLegalPerson.setCertNumber(desUtils.decrypt(vendor.getCertNumber()));
        organizationLegalPerson.setCertFrontImageUrl(vendor.getCertFrontImageUrl());
        organizationLegalPerson.setCertRealImageUrl(vendor.getCertRealImageUrl());
        organizationLegalPerson.setCertPersonImageUrl(vendor.getCertPersonImageUrl());
        organizationLegalPerson.setTermEndDate(vendor.getTermEndDate());
        organizationLegalPerson.setTermStartDate(vendor.getTermStartDate());

        return organizationLegalPerson;
    }

    protected OrganizationLegalPersonCreationApplyDto organizationLegalPersonToOrganizationLegalPersonCreationApplyDto(OrganizationLegalPerson organizationLegalPerson, Vendor vendor) throws Exception {
        if (organizationLegalPerson == null) {
            organizationLegalPerson = vendorToOrganizationLegalPerson(vendor);
            if (organizationLegalPerson == null) {
                return null;
            }
        }

        OrganizationLegalPersonCreationApplyDto organizationLegalPersonCreationApplyDto = new OrganizationLegalPersonCreationApplyDto();

        organizationLegalPersonCreationApplyDto.setName(organizationLegalPerson.getName());
        if (VendorConstant.VENDOR_CERT_TYPE_ID_CARD.getCode().equals(organizationLegalPerson.getCertTypeCode())) {
            organizationLegalPersonCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_MAINLAND_IDCARD.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_INLAND_PASS_CERT.getCode().equals(organizationLegalPerson.getCertTypeCode())) {
            organizationLegalPersonCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_HONGKONG.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_MAINLAND_PASS_CERT.getCode().equals(organizationLegalPerson.getCertTypeCode())) {
            organizationLegalPersonCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_TAIWAN.getCode());
        }
        if (VendorConstant.VENDOR_CERT_TYPE_PASSPORT.getCode().equals(organizationLegalPerson.getCertTypeCode())) {
            organizationLegalPersonCreationApplyDto.setCertTypeCode(WxOrganizationConstant.IDENTIFICATION_TYPE_OVERSEA_PASSPORT.getCode());
        }
        organizationLegalPersonCreationApplyDto.setCertNumber(desUtils.decrypt(organizationLegalPerson.getCertNumber()));
        organizationLegalPersonCreationApplyDto.setCertFrontImageUrl(organizationLegalPerson.getCertFrontImageUrl());
        organizationLegalPersonCreationApplyDto.setCertRealImageUrl(organizationLegalPerson.getCertRealImageUrl());
        organizationLegalPersonCreationApplyDto.setCertPersonImageUrl(organizationLegalPerson.getCertPersonImageUrl());
        if (organizationLegalPerson.getTermEndDate() != null) {
            organizationLegalPersonCreationApplyDto.setTermEndDate(DateTimeFormatter.ISO_LOCAL_DATE.format(organizationLegalPerson.getTermEndDate()));
        }

        return organizationLegalPersonCreationApplyDto;
    }
}
