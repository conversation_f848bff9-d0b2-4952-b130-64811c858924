/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMallRepository.java
 * @createdDate: 2022/08/22 14:02:22
 *
 */

package com.bamboocloud.cdp.user.mama.shop.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopOfHotelinfo;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface MamaShopOfHotelinfoRepository extends FwkBaseRepository<ShopOfHotelinfo,Long> {
    List<ShopOfHotelinfo> findAllByShopId(@Param("shopId") String shopId);
}
