package com.bamboocloud.cdp.user.mama.shop.handler;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopNameImportDto;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MamaShopNameImportExcelHandler extends AnalysisEventListener<MamaShopNameImportDto> {

    private final List<String> mobiles = new ArrayList<>();

    private List<MamaShopNameImportDto> failList = new ArrayList<>();

    private List<MamaShopNameImportDto> successList = new ArrayList<>();

    public MamaShopNameImportExcelHandler() {
        super();
        mobiles.clear();
        failList.clear();
        successList.clear();
    }

    @Override
    public void invoke(MamaShopNameImportDto mamaShopNameImportDto, AnalysisContext context) {
        String resultError = "";
        if (FwkStringUtil.isNotBlank(mamaShopNameImportDto.getShopName())) {
            mamaShopNameImportDto.setShopName(mamaShopNameImportDto.getShopName().trim());
            // 判断Excel数据是否重复
            if (!CollectionUtils.isEmpty(mobiles)) {
                if (mobiles.contains(mamaShopNameImportDto.getShopName())) {
                    resultError += "店铺名称重复,";
                }
            }
            mobiles.add(mamaShopNameImportDto.getShopName());
        }
        if (FwkStringUtil.isBlank(resultError)) {
            mobiles.add(mamaShopNameImportDto.getShopName());
            successList.add(mamaShopNameImportDto);
        } else {
            mamaShopNameImportDto.setErrorMsg(resultError);
            failList.add(mamaShopNameImportDto);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
    }

    public List<MamaShopNameImportDto> getFailList() {
        return failList;
    }

    public List<MamaShopNameImportDto> getSuccessList() {
        return successList;
    }
}
