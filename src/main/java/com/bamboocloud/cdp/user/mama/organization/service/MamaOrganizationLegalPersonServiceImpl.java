/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaOrganizationLegalPersonServiceImpl.java
 * @createdDate: 2022/12/06 15:19:06
 *
 */

package com.bamboocloud.cdp.user.mama.organization.service;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.bo.mama.LoginMamaBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.vo.base.organization.BaseOrganizationLegalPersonCreationVo;
import com.bamboocloud.cdp.user.mama.organization.mapper.MamaOrganizationLegalPersonMapper;
import com.bamboocloud.cdp.user.mama.organization.repository.MamaOrganizationLegalPersonRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.OrganizationLegalPerson;
import com.bamboocloud.cdp.user.sdk.util.DesUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaOrganizationLegalPersonServiceImpl extends BaseMamaService implements MamaOrganizationLegalPersonService {
    @Autowired
    private MamaOrganizationLegalPersonMapper mamaOrganizationLegalPersonMapper;

    @Autowired
    @Lazy
    private MamaOrganizationService mamaOrganizationService;

    @Autowired
    private MamaOrganizationLegalPersonRepository mamaOrganizationLegalPersonRepository;

    @Autowired
    private DesUtils desUtils;

    @Override
    public OrganizationLegalPerson update(LoginMamaBo loginMama, OrganizationLegalPerson organizationLegalPerson,
        BaseOrganizationLegalPersonCreationVo baseOrganizationLegalPersonCreationVo)
        throws ClientException {
        String certFrontImageUrl = organizationLegalPerson.getCertFrontImageUrl();
        String certRealImageUrl = organizationLegalPerson.getCertRealImageUrl();
        String certPersonImageUrl = organizationLegalPerson.getCertPersonImageUrl();
        if (FwkStringUtil.isNotBlank(baseOrganizationLegalPersonCreationVo.getTermEndDate()) &&
                "长期".equals(baseOrganizationLegalPersonCreationVo.getTermEndDate())) {
            baseOrganizationLegalPersonCreationVo.setTermEndDate("9999-01-01");
        }
        if (FwkStringUtil.isNotBlank(baseOrganizationLegalPersonCreationVo.getCertNumber())) {
            baseOrganizationLegalPersonCreationVo.setCertNumber(desUtils.encryption(baseOrganizationLegalPersonCreationVo.getCertNumber()));
        }
        organizationLegalPerson = mamaOrganizationLegalPersonMapper.toEntityForUpdate(
                baseOrganizationLegalPersonCreationVo, organizationLegalPerson);
        if (FwkStringUtil.isNotBlank(baseOrganizationLegalPersonCreationVo.getCertFrontImageUrl())) {
            certFrontImageUrl = mamaOrganizationService.uploadFile(organizationLegalPerson.getOrganizationId(),
                    baseOrganizationLegalPersonCreationVo.getCertFrontImageUrl(), certFrontImageUrl, false);
            organizationLegalPerson.setCertFrontImageUrl(certFrontImageUrl);
        }
        if (FwkStringUtil.isNotBlank(baseOrganizationLegalPersonCreationVo.getCertRealImageUrl())) {
            certRealImageUrl = mamaOrganizationService.uploadFile(organizationLegalPerson.getOrganizationId(),
                    baseOrganizationLegalPersonCreationVo.getCertRealImageUrl(), certRealImageUrl, false);
            organizationLegalPerson.setCertRealImageUrl(certRealImageUrl);
        }
        if (FwkStringUtil.isNotBlank(baseOrganizationLegalPersonCreationVo.getCertPersonImageUrl())) {
            certPersonImageUrl = mamaOrganizationService.uploadFile(organizationLegalPerson.getOrganizationId(),
                    baseOrganizationLegalPersonCreationVo.getCertPersonImageUrl(), certPersonImageUrl, false);
            organizationLegalPerson.setCertPersonImageUrl(certPersonImageUrl);
        }
        return organizationLegalPerson;
    }

    @Override
    public List<OrganizationLegalPerson> list() {
        return mamaOrganizationLegalPersonRepository.findAll();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bulkUpdate(List<OrganizationLegalPerson> organizationLegalPeople) {
        if (!FwkCollectionUtil.isEmpty(organizationLegalPeople)) {
            mamaOrganizationLegalPersonRepository.saveAll(organizationLegalPeople);
        }
    }
}
