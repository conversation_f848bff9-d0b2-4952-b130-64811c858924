/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaVendorPermissionModuleConst.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.vendor.permission;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR> Mo
 * @description:
 */
@AllArgsConstructor
@Data
public class MamaVendorPermissionModuleConst {
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_GENERAL =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_GENERAL", "概况", 1);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_SHOP =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_SHOP", "店铺管理", 2);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_PRODUCT =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_PRODUCT", "商品管理", 3);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_PRODUCT_VOUCHER =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_PRODUCT_VOUCHER", "代金券", 4);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_COUPON =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_COUPON", "优惠券", 5);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_SALE =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_SALE", "营销工具", 6);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_RECEIPT_CODE =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_RECEIPT_CODE", "收款码", 7);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_WRITE_OFF =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_WRITE_OFF", "我要核销", 8);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_TRADE =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_TRADE", "订单管理", 9);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_POINT_PRODUCT =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_POINT_PRODUCT", "积分商城", 10);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_MEMBER =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_MEMBER", "会员中心", 11);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_MESSAGE =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_MESSAGE", "信息管理", 12);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_DATA_ANALYSIS =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_DATA_ANALYSIS", "数据分析", 13);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_ASSET =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_ASSET", "财务管理", 14);
    public static MamaVendorPermissionModuleConst VENDOR_PERMISSION_MODULE_SHOP_VENDOR =
            new MamaVendorPermissionModuleConst("VENDOR_PERMISSION_MODULE_SHOP_VENDOR", "店员管理", 15);

    private final String code;
    private final String name;
    private final Integer orderId;
}
