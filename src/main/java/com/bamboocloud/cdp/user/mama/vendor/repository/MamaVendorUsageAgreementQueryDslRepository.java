/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaBuyerUsageAgreementQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.vendor.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.mama.vendor.MamaVendorUsageAgreementInfoDto;
import com.bamboocloud.cdp.user.common.dto.mama.vendor.MamaVendorUsageAgreementInfoPageDto;
import com.bamboocloud.cdp.user.common.entity.mama.QUsageAgreement;
import com.bamboocloud.cdp.user.common.entity.vendor.QVendorUsageAgreement;
import com.bamboocloud.cdp.user.common.vo.mama.usageagreement.MamaUsageAgreementInfoSearchVo;
import com.bamboocloud.cdp.user.sdk.constant.UserAgreementConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.QVendor;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QOrganization;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QOrganizationCompany;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QOrganizationVendor;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class MamaVendorUsageAgreementQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    public MamaVendorUsageAgreementInfoPageDto search(MamaUsageAgreementInfoSearchVo mamaUsageAgreementInfoSearchVo,List<String> organizationIds) {
        QVendorUsageAgreement vendorUsageAgreement = QVendorUsageAgreement.vendorUsageAgreement;
        QUsageAgreement usageAgreement = QUsageAgreement.usageAgreement;
        QOrganization organization = QOrganization.organization;
        BooleanBuilder builder = new BooleanBuilder();
        if (FwkStringUtil.isNotBlank(mamaUsageAgreementInfoSearchVo.getOrganizationName())) {
            builder.and(vendorUsageAgreement.organizationId.in(organizationIds));
        }
        if (!ObjectUtils.isEmpty(mamaUsageAgreementInfoSearchVo.getStartDate())
                && !ObjectUtils.isEmpty(mamaUsageAgreementInfoSearchVo.getEndDate())) {
            builder.and(vendorUsageAgreement.acceptedDate.between(mamaUsageAgreementInfoSearchVo.getStartDate(), mamaUsageAgreementInfoSearchVo.getEndDate()));
        }
        builder.and(vendorUsageAgreement.accepted.isTrue());
        builder.and(vendorUsageAgreement.usageAgreementCode.eq(UserAgreementConstant.USAGE_AGREEMENT_CODE_LETTER_OF_COMMITMENT_FROM_HOST_MERCHANT_VENDOR.getCode()));
        BlazeJPAQuery<MamaVendorUsageAgreementInfoDto> jpaQuery = blazeJpaQueryFactory.select(Projections.constructor(
                        MamaVendorUsageAgreementInfoDto.class,
                        vendorUsageAgreement.organizationId,
                        vendorUsageAgreement.usageAgreementVersion,
                        usageAgreement.id,
                        vendorUsageAgreement.acceptedDate,
                        organization.typeCode
                )).from(vendorUsageAgreement)
                .leftJoin(organization).on(vendorUsageAgreement.organizationId.eq(organization.id))
                .leftJoin(usageAgreement).on(vendorUsageAgreement.usageAgreementVersion.eq(usageAgreement.version)
                        .and(vendorUsageAgreement.usageAgreementCode.eq(usageAgreement.code)))
                .where(builder);
        MamaVendorUsageAgreementInfoPageDto mamaVendorUsageAgreementInfoPageDto = new MamaVendorUsageAgreementInfoPageDto();
        mamaVendorUsageAgreementInfoPageDto.setTotalCount(String.valueOf(jpaQuery.fetchCount()));
        if (!ObjectUtils.isEmpty(mamaUsageAgreementInfoSearchVo.getLimit()) &&
                !ObjectUtils.isEmpty(mamaUsageAgreementInfoSearchVo.getOffset())) {
            jpaQuery.offset((long) mamaUsageAgreementInfoSearchVo.getLimit() * mamaUsageAgreementInfoSearchVo.getOffset()).limit(mamaUsageAgreementInfoSearchVo.getLimit());
        }
        List<MamaVendorUsageAgreementInfoDto> fetch = jpaQuery.orderBy(vendorUsageAgreement.acceptedDate.desc()).fetch();
        mamaVendorUsageAgreementInfoPageDto.setVendorUsageAgreements(fetch);
        return mamaVendorUsageAgreementInfoPageDto;
    }


    public List<String> getOrganizationIds(String organizationName) {
        QVendorUsageAgreement vendorUsageAgreement = QVendorUsageAgreement.vendorUsageAgreement;
        QVendor vendor = QVendor.vendor;
        QOrganizationVendor organizationVendor = QOrganizationVendor.organizationVendor;
        QOrganization organization = QOrganization.organization;
        QOrganizationCompany organizationCompany = QOrganizationCompany.organizationCompany;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(vendor.name.contains(organizationName).or(organizationCompany.name.contains(organizationName)));
        builder.and(organizationVendor.defaultAdmin.isTrue());
        builder.and(vendorUsageAgreement.usageAgreementCode.eq(UserAgreementConstant.USAGE_AGREEMENT_CODE_LETTER_OF_COMMITMENT_FROM_HOST_MERCHANT_VENDOR.getCode()));
        JPAQuery<String> jpaQuery = queryFactory.select(
                        vendorUsageAgreement.organizationId
                ).from(vendorUsageAgreement)
                .leftJoin(organization).on(vendorUsageAgreement.organizationId.eq(organization.id))
                .leftJoin(organizationVendor).on(organizationVendor.organizationId.eq(organization.id))
                .leftJoin(vendor).on(vendor.id.eq(organizationVendor.vendorId))
                .leftJoin(organizationCompany).on(organizationCompany.organizationId.eq(organization.id))
                .where(builder).distinct();

        return jpaQuery.fetch();
    }

    public String getVendorNameByOrganizationId(String organizationId) {
        QVendor vendor = QVendor.vendor;
        QOrganizationVendor organizationVendor = QOrganizationVendor.organizationVendor;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(organizationVendor.organizationId.eq(organizationId));
        builder.and(organizationVendor.defaultAdmin.isTrue());
        JPAQuery<String> jpaQuery = queryFactory.select(
                        vendor.name
                ).from(vendor)
                .leftJoin(organizationVendor).on(organizationVendor.vendorId.eq(vendor.id))
                .where(builder);
        return jpaQuery.fetchFirst();
    }

    public String getOrganizationCompanyNameByOrganizationId(String organizationId) {
        QOrganizationCompany organizationCompany = QOrganizationCompany.organizationCompany;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(organizationCompany.organizationId.eq(organizationId));
        JPAQuery<String> jpaQuery = queryFactory.select(
                        organizationCompany.name
                ).from(organizationCompany)
                .where(builder);
        return jpaQuery.fetchFirst();
    }
}
