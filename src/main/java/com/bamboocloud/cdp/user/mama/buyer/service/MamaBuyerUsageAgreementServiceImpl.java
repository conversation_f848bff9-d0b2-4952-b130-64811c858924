/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaBuyerUsageAgreementServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.buyer.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.user.mama.buyer.repository.MamaBuyerUsageAgreementQueryDslRepository;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.buyer.MamaBuyerUsageAgreementDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> Shu
 */
@Service
@Slf4j
public class MamaBuyerUsageAgreementServiceImpl extends BaseMamaService implements MamaBuyerUsageAgreementService {

    @Autowired
    private MamaBuyerUsageAgreementQueryDslRepository mamaBuyerUsageAgreementQueryDslRepository;

    @Override
    public MamaBuyerUsageAgreementDto getByBuyerIdAndCodeAndAccepted(String buyerId, String code, boolean accepted) {
        return mamaBuyerUsageAgreementQueryDslRepository.getByBuyerIdAndCodeAndAccepted(buyerId, code, accepted);
    }

}
