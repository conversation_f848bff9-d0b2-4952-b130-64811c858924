/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaVendorPermissionConst.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.vendor.permission;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;


/**
 * <AUTHOR> <PERSON>
 * @description:
 */
@AllArgsConstructor
@Getter
@EqualsAndHashCode
public class MamaVendorPermissionConst {
    /**
     * 概况
     */
    public static final MamaVendorPermissionConst SHOP_DATA_VIEW =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_GENERAL, "SHOP_DATA_VIEW", "店铺数据", false, 1);

    /**
     * 店铺信息
     */
    public static final MamaVendorPermissionConst SHOP_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP, "SHOP_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP, "SHOP_UPDATE", "编辑", false, 2);
    /**
     * 主体认证
     */
    public static final MamaVendorPermissionConst SHOP_ORGANIZATION_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ORGANIZATION, "SHOP_ORGANIZATION_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_ORGANIZATION_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ORGANIZATION, "SHOP_ORGANIZATION_UPDATE", "修改", false, 3);
    /**
     * 店铺上下架设置
     */
    public static final MamaVendorPermissionConst SHOP_ON_OFF_SHOW_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ON_OFF_SHOW, "SHOP_ON_OFF_SHOW_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_ON_OFF_SHOW_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ON_OFF_SHOW, "SHOP_ON_OFF_SHOW_UPDATE", "修改", false, 2);
    /**
     * 店铺菜单栏设置
     */
    public static final MamaVendorPermissionConst SHOP_PRODUCT_CATEGORY_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_PRODUCT_CATEGORY, "SHOP_PRODUCT_CATEGORY_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_PRODUCT_CATEGORY_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_PRODUCT_CATEGORY, "SHOP_PRODUCT_CATEGORY_UPDATE", "修改", false, 2);
    /**
     * 店铺经营设置
     */
    public static final MamaVendorPermissionConst SHOP_MANAGE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_MANAGE, "SHOP_MANAGE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_MANAGE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_MANAGE, "SHOP_MANAGE_UPDATE", "修改", false, 2);
    /**
     * 店铺地址库
     */
    public static final MamaVendorPermissionConst SHOP_ADDRESS_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ADDRESS, "SHOP_ADDRESS_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_ADDRESS_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ADDRESS, "SHOP_ADDRESS_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst SHOP_ADDRESS_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ADDRESS, "SHOP_ADDRESS_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst SHOP_ADDRESS_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_ADDRESS, "SHOP_ADDRESS_DELETE", "删除", false, 4);
    /**
     * 商品管理
     */
    public static final MamaVendorPermissionConst PRODUCT_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT, "PRODUCT_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst PRODUCT_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT, "PRODUCT_CREATE", "发布", false, 2);
    public static final MamaVendorPermissionConst PRODUCT_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT, "PRODUCT_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst PRODUCT_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT, "PRODUCT_DELETE", "删除", false, 5);

    /**
     * 代金券
     */
    public static final MamaVendorPermissionConst PRODUCT_VOUCHER_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT_VOUCHER, "PRODUCT_VOUCHER_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst PRODUCT_VOUCHER_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT_VOUCHER, "PRODUCT_VOUCHER_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst PRODUCT_VOUCHER_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT_VOUCHER, "PRODUCT_VOUCHER_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst PRODUCT_VOUCHER_STOP_BUY =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT_VOUCHER, "PRODUCT_VOUCHER_STOP_BUY", "停购", false, 4);
    public static final MamaVendorPermissionConst PRODUCT_VOUCHER_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_PRODUCT_VOUCHER, "PRODUCT_VOUCHER_DELETE", "删除", false, 5);

    /**
     * 优惠券
     */
    public static final MamaVendorPermissionConst COUPON_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_COUPON, "COUPON_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst COUPON_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_COUPON, "COUPON_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst COUPON_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_COUPON, "COUPON_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst COUPON_STOP_RECEIVE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_COUPON, "COUPON_STOP_RECEIVE", "停领", false, 4);
    public static final MamaVendorPermissionConst COUPON_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_COUPON, "COUPON_DELETE", "删除", false, 5);

    /**
     * 满减优惠
     */
    public static final MamaVendorPermissionConst DISCOUNT_DSE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_DSE, "DISCOUNT_DSE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst DISCOUNT_DSE_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_DSE, "DISCOUNT_DSE_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst DISCOUNT_DSE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_DSE, "DISCOUNT_DSE_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst DISCOUNT_DSE_CANCEL =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_DSE, "DISCOUNT_DSE_CANCEL", "取消", false, 4);
    public static final MamaVendorPermissionConst DISCOUNT_DSE_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_DSE, "DISCOUNT_DSE_DELETE", "删除", false, 5);
    /**
     * 满折优惠
     */
    public static final MamaVendorPermissionConst DISCOUNT_RSE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_RSE, "DISCOUNT_RSE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst DISCOUNT_RSE_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_RSE, "DISCOUNT_RSE_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst DISCOUNT_RSE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_RSE, "DISCOUNT_RSE_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst DISCOUNT_RSE_CANCEL =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_RSE, "DISCOUNT_RSE_CANCEL", "取消", false, 4);
    public static final MamaVendorPermissionConst DISCOUNT_RSE_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DISCOUNT_RSE, "DISCOUNT_RSE_DELETE", "删除", false, 5);
    /**
     * 红包
     */
    public static final MamaVendorPermissionConst RED_PACKET_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RED_PACKET, "RED_PACKET_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst RED_PACKET_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RED_PACKET, "RED_PACKET_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst RED_PACKET_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RED_PACKET, "RED_PACKET_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst RED_PACKET_CANCEL =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RED_PACKET, "RED_PACKET_CANCEL", "取消", false, 4);
    public static final MamaVendorPermissionConst RED_PACKET_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RED_PACKET, "RED_PACKET_DELETE", "删除", false, 5);
    /**
     * 宝箱
     */
    public static final MamaVendorPermissionConst LUCKY_BOX_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_LUCKY_BOX, "LUCKY_BOX_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst LUCKY_BOX_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_LUCKY_BOX, "LUCKY_BOX_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst LUCKY_BOX_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_LUCKY_BOX, "LUCKY_BOX_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst LUCKY_BOX_STOP_RECEIVE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_LUCKY_BOX, "LUCKY_BOX_STOP_RECEIVE", "停领", false, 4);
    public static final MamaVendorPermissionConst LUCKY_BOX_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_LUCKY_BOX, "LUCKY_BOX_DELETE", "删除", false, 5);
    /**
     * 弹幕优惠
     */
    public static final MamaVendorPermissionConst BULLET_CHAT_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BULLET_CHAT, "BULLET_CHAT_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst BULLET_CHAT_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BULLET_CHAT, "BULLET_CHAT_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst BULLET_CHAT_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BULLET_CHAT, "BULLET_CHAT_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst BULLET_CHAT_CANCEL =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BULLET_CHAT, "BULLET_CHAT_CANCEL", "取消", false, 4);
    public static final MamaVendorPermissionConst BULLET_CHAT_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BULLET_CHAT, "BULLET_CHAT_DELETE", "删除", false, 5);
    /**
     * 收款码
     */
    public static final MamaVendorPermissionConst RECEIPT_CODE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RECEIPT_CODE, "RECEIPT_CODE_GET", "收款码", false, 1);
    public static final MamaVendorPermissionConst RECEIPT_RECORD_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_RECEIPT_CODE, "RECEIPT_RECORD_GET", "收款记录", false, 2);
    /**
     * 我要核销
     */
    public static final MamaVendorPermissionConst SCAN_AND_WRITE_OFF =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_WRITE_OFF, "SCAN_AND_WRITE_OFF", "扫一扫核销", false, 1);
    public static final MamaVendorPermissionConst MANUAL_WRITE_OFF =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_WRITE_OFF, "MANUAL_WRITE_OFF", "手工核销", false, 2);
    public static final MamaVendorPermissionConst WRITE_OFF_RECORDS =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_WRITE_OFF, "WRITE_OFF_RECORDS", "核销记录", false, 2);
    /**
     * 订单
     */
    public static final MamaVendorPermissionConst TRADE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst TRADE_REMARK =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_REMARK", "加备注", false, 2);
    public static final MamaVendorPermissionConst TRADE_PRICE_CHANGE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_PRICE_CHANGE", "改价", false, 3);
    public static final MamaVendorPermissionConst TRADE_DISPATCH =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_DISPATCH", "发货", false, 4);
    public static final MamaVendorPermissionConst TRADE_CANCEL =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_CANCEL", "取消订单", false, 5);
    public static final MamaVendorPermissionConst TRADE_LOGISTICS_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_LOGISTICS_UPDATE", "修改物流", false, 6);
    public static final MamaVendorPermissionConst TRADE_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE, "TRADE_DELETE", "删除订单", false, 7);
    /**
     * 退款售后
     */
    public static final MamaVendorPermissionConst TRADE_AFTER_SALE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE_AFTER_SALE, "TRADE_AFTER_SALE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst TRADE_AFTER_SALE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE_AFTER_SALE, "TRADE_AFTER_SALE_UPDATE", "处理售后", false, 2);
    /**
     * 评价管理
     */
    public static final MamaVendorPermissionConst TRADE_REVIEW_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE_REVIEW, "TRADE_REVIEW_UPDATE", "立即评价", false, 1);
    public static final MamaVendorPermissionConst TRADE_REVIEW_REPLY =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_TRADE_REVIEW, "TRADE_REVIEW_REPLY", "回复评价", false, 2);
    /**
     * 积分商城
     */
    public static final MamaVendorPermissionConst POINT_PRODUCT_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_POINT_PRODUCT, "POINT_PRODUCT_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst POINT_PRODUCT_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_POINT_PRODUCT, "POINT_PRODUCT_CREATE", "发布", false, 2);
    public static final MamaVendorPermissionConst POINT_PRODUCT_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_POINT_PRODUCT, "POINT_PRODUCT_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst POINT_PRODUCT_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_POINT_PRODUCT, "POINT_PRODUCT_DELETE", "删除", false, 5);
    /**
     * 会员中心
     */
    public static final MamaVendorPermissionConst MEMBER_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MEMBER, "MEMBER_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst MEMBER_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MEMBER, "MEMBER_CREATE", "新增", false, 2);
    public static final MamaVendorPermissionConst MEMBER_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MEMBER, "MEMBER_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst MEMBER_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MEMBER, "MEMBER_DELETE", "删除", false, 4);
    /**
     * 信息管理
     */
    public static final MamaVendorPermissionConst MESSAGE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MESSAGE, "MESSAGE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst MESSAGE_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MESSAGE, "MESSAGE_CREATE", "创建", false, 2);
    public static final MamaVendorPermissionConst MESSAGE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MESSAGE, "MESSAGE_UPDATE", "编辑", false, 3);
    public static final MamaVendorPermissionConst MESSAGE_DELETE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_MESSAGE, "MESSAGE_DELETE", "删除", false, 4);
    /**
     * 数据分析
     */
    public static final MamaVendorPermissionConst DATA_ANALYSIS_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_DATA_ANALYSIS, "DATA_ANALYSIS_GET", "查看", false, 1);
    /**
     * 店铺余额
     */
    public static final MamaVendorPermissionConst FUND_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_FUND, "FUND_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst FUND_WITHDRAW =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_FUND, "FUND_WITHDRAW", "提现", false, 2);
    /**
     * 提现记录
     */
    public static final MamaVendorPermissionConst FUND_WITHDRAW_RECORD_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_FUND_WITHDRAW_RECORD, "FUND_WITHDRAW_RECORD_GET", "查看", false, 1);
    /**
     * 对账单
     */
    public static final MamaVendorPermissionConst STATEMENT_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_STATEMENT, "STATEMENT_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst STATEMENT_SUBMIT =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_STATEMENT, "STATEMENT_SUBMIT", "确认对账单", false, 2);
    public static final MamaVendorPermissionConst STATEMENT_DOWNLOAD =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_STATEMENT, "STATEMENT_DOWNLOAD", "下载对账单", false, 3);
    /**
     * 银行卡
     */
    public static final MamaVendorPermissionConst BANK_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BANK, "BANK_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst BANK_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_BANK, "BANK_UPDATE", "换绑银行卡", false, 2);
    /**
     * 店员管理
     */
    public static final MamaVendorPermissionConst SHOP_VENDOR_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR, "SHOP_VENDOR_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_VENDOR_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR, "SHOP_VENDOR_CREATE", "添加店员", false, 2);
    public static final MamaVendorPermissionConst SHOP_VENDOR_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR, "SHOP_VENDOR_UPDATE", "编辑店员信息", false, 3);
    public static final MamaVendorPermissionConst SHOP_VENDOR_ENABLED =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR, "SHOP_VENDOR_ENABLED", "启用/禁用", false, 4);
    /**
     * 角色管理
     */
    public static final MamaVendorPermissionConst SHOP_VENDOR_ROLE_GET =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR_ROLE, "SHOP_VENDOR_ROLE_GET", "查看", false, 1);
    public static final MamaVendorPermissionConst SHOP_VENDOR_ROLE_CREATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR_ROLE, "SHOP_VENDOR_ROLE_CREATE", "添加员工角色", false, 2);
    public static final MamaVendorPermissionConst SHOP_VENDOR_ROLE_UPDATE =
            new MamaVendorPermissionConst(MamaVendorPermissionGroupConst.VENDOR_PERMISSION_GROUP_SHOP_VENDOR_ROLE, "SHOP_VENDOR_ROLE_UPDATE", "编辑", false, 3);
    private final MamaVendorPermissionGroupConst permissionGroup;
    private final String code;
    private final String name;
    private final boolean anonymousAccess;
    private final Integer orderId;

}
