/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMallMapper.java
 * @createdDate: 2022/08/22 14:40:22
 *
 */

package com.bamboocloud.cdp.user.mama.shop.mapper;

import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategorySelectedDutyDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategorySelectedDutyCreationVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategorySelectedDutyUpdateVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopCategorySelectedDuty;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MamaShopCategorySelectedDutyMapper {
    ShopCategorySelectedDuty toEntityForCreation(MamaShopCategorySelectedDutyCreationVo mamaShopCategorySelectedDutyCreationVo);

    MamaShopCategorySelectedDutyDto toDto(ShopCategorySelectedDuty shopCategorySelectedDuty);

    List<MamaShopCategorySelectedDutyDto> toDtos(List<ShopCategorySelectedDuty> shopCategorySelectedDutyList);

    ShopCategorySelectedDuty toEntityForUpdate(
            MamaShopCategorySelectedDutyUpdateVo mamaShopCategorySelectedDutyUpdateVo,
            @MappingTarget ShopCategorySelectedDuty shopCategorySelectedDuty);
}
