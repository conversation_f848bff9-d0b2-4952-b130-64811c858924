/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMapper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.shop.mapper;

import com.bamboocloud.cdp.pay.sdk.common.vo.wx.shop.ShopOrganizationCreationApplyVo;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaDispShopProductCategoryDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCreationVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopUpdateVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopUpdatedVo;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopSimpleDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.DispShopProductCategory;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Shop;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@DecoratedWith(MamaShopMapperDecorator.class)
public interface MamaShopMapper {
    @Mapping(target = "status.code", source = "statusCode")
    MamaShopDto toDto(Shop shop);

    MamaShopSimpleDto toSimpleDto(Shop shop);

    List<MamaShopDto> toDtos(List<Shop> shops);

    Shop toEntityForUpdated(MamaShopUpdatedVo mamaShopUpdateVo, @MappingTarget Shop shop);
    Shop toEntityForUpdate(MamaShopUpdateVo mamaShopUpdateVo, @MappingTarget Shop shop);

    Shop toEntityForUpdate(MamaShopCreationVo mamaShopCreationVo, @MappingTarget Shop shop);

    @Mapping(target = "shopId", source = "id")
    @Mapping(target = "organizationType", source = "organization.typeCode")
    ShopOrganizationCreationApplyVo toShopOrganizationCreationApplyDto(Shop shop) throws Exception;

    Shop toEntityForCreation(MamaShopCreationVo mamaShopCreationVo);

    List<MamaDispShopProductCategoryDto> toDispShopProductCategoryDto(List<DispShopProductCategory> dispShopProductCategories);
}
