/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: MamaBuyerUsageAgreementController.java
 * @createdDate: 2023/07/10 09:57:10
 *
 */

package com.bamboocloud.cdp.user.mama.vendor.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.common.dto.mama.vendor.MamaVendorUsageAgreementInfoPageDto;
import com.bamboocloud.cdp.user.common.vo.mama.usageagreement.MamaUsageAgreementInfoSearchVo;
import com.bamboocloud.cdp.user.mama.vendor.service.MamaVendorUsageAgreementService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class MamaVendorUsageAgreementController extends BaseMamaController {


    @Autowired
    private MamaVendorUsageAgreementService mamaVendorUsageAgreementService;


    /**
     * 查看东道主申请记录
     *
     * @param mamaUsageAgreementInfoSearchVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_VENDOR_USAGE_AGREEMENT_HOST_SEARCH_V1)
    public FwkApiResponse<MamaVendorUsageAgreementInfoPageDto> search(@RequestBody @Validated MamaUsageAgreementInfoSearchVo mamaUsageAgreementInfoSearchVo) {
        log.debug("MamaVendorUsageAgreementController - search");
        MamaVendorUsageAgreementInfoPageDto search = mamaVendorUsageAgreementService.search(mamaUsageAgreementInfoSearchVo);
        return FwkApiResponse.success(search);
    }
}
