/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaOrganizationBankServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.organization.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.user.mama.organization.repository.MamaOrganizationBankRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.OrganizationBank;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Slf4j
public class MamaOrganizationBankServiceImpl extends BaseMamaService implements MamaOrganizationBankService {

    @Autowired
    private MamaOrganizationBankRepository mamaOrganizationBankRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public OrganizationBank getByOrganizationId(String organizationId) {
        return mamaOrganizationBankRepository.findByOrganizationId(organizationId);
    }

    @Override
    public OrganizationBank get(Integer id) {
        return mamaOrganizationBankRepository.findById(id).orElseThrow();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrganizationBank create(OrganizationBank organizationBank) {
        organizationBank = mamaOrganizationBankRepository.saveAndFlush(organizationBank);
        entityManager.refresh(organizationBank);
        return organizationBank;
    }
}
