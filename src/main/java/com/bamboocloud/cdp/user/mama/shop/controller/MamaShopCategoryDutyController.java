/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopAddressController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.shop.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyTwoDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutyCreationVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutySearchVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutyUpdateVo;
import com.bamboocloud.cdp.user.mama.shop.service.MamaShopCategoryDutyService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@Slf4j
public class MamaShopCategoryDutyController extends BaseMamaController {

    @Autowired
    private MamaShopCategoryDutyService mamaShopCategoryDutyService;


    /**
     * 创建店铺服务
     *
     * @param mamaShopCategoryDutyCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SHOP_CATEGORY_DUTY_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_CREATE_V1)
    public FwkApiResponse<MamaShopCategoryDutyDto> create(@RequestBody MamaShopCategoryDutyCreationVo mamaShopCategoryDutyCreationVo) {
        log.debug("MamaShopDutyController -- create");
        return FwkApiResponse.success(mamaShopCategoryDutyService.create(mamaShopCategoryDutyCreationVo));
    }

    /**
     * 查询指定店铺服务
     *
     * @param id
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_GET_V1)
    public FwkApiResponse<MamaShopCategoryDutyDto> get(@PathVariable Integer id) {
        log.debug("MamaShopDutyController -- get");
        return FwkApiResponse.success(mamaShopCategoryDutyService.get(id));
    }

    /**
     * 修改店铺服务
     *
     * @param id
     * @param mamaShopCategoryDutyUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SHOP_CATEGORY_DUTY_GET.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_UPDATE_V1)
    public FwkApiResponse<MamaShopCategoryDutyDto> update(@PathVariable Integer id, @RequestBody MamaShopCategoryDutyUpdateVo mamaShopCategoryDutyUpdateVo) {
        log.debug("MamaShopDutyController -- update");
        return FwkApiResponse.success(mamaShopCategoryDutyService.update(mamaShopCategoryDutyUpdateVo));
    }

    /**
     * 删除指定店铺服务
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).SHOP_CATEGORY_DUTY_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Integer id) {
        log.debug("MamaShopDutyController -- delete");
        mamaShopCategoryDutyService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 查询所有的店铺服务
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_SEARCH_V1)
    public FwkApiResponse<List<MamaShopCategoryDutyDto>> getList() {
        log.debug("MamaShopDutyController -- getList");
        List<MamaShopCategoryDutyDto> shopDutyDtoList = mamaShopCategoryDutyService.getAll();
        return FwkApiResponse.success(shopDutyDtoList);
    }

    /**
     * 分页查询所有商圈
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_SHOP_CATEGORY_DUTY_PAGE_SEARCH_V1)
    public FwkApiResponse<MamaShopCategoryDutyPageDto> search(@RequestBody MamaShopCategoryDutySearchVo mamaShopCategoryDutySearchVo){
        log.debug("MamaShopMallController -- search");
        MamaShopCategoryDutyPageDto shopCategoryDutyPageDto = mamaShopCategoryDutyService.search(mamaShopCategoryDutySearchVo);
        return FwkApiResponse.success(shopCategoryDutyPageDto);
    }
    /**
     * 根据shopCategoryId来查该类型下的所有服务
     *
     */
    @GetMapping(MamaRouteConstant.MAMA_SHOP_DUTY_SEARCH_V1)
    public FwkApiResponse<List<MamaShopCategoryDutyTwoDto>> getAllByShopCategoryId(@PathVariable Integer shopCategoryId) {
        log.debug("MamaShopDutyController -- getAllByShopCategoryId");
        return FwkApiResponse.success(mamaShopCategoryDutyService.getAllByShopCategoryId(shopCategoryId));
    }
}
