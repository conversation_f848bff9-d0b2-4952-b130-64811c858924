/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMallMapper.java
 * @createdDate: 2022/08/22 14:40:22
 *
 */

package com.bamboocloud.cdp.user.mama.shop.mapper;

import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopSelectedDutyVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopSelectedDuty;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MamaShopSelectedDutyMapper {
    List<ShopSelectedDuty> toEntityForCreation(List<MamaShopSelectedDutyVo> shopSelectedDutyVoList);
}
