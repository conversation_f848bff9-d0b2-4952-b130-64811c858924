/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopDutyServiceImpl.java
 * @createdDate: 2022/08/22 17:16:22
 *
 */

package com.bamboocloud.cdp.user.mama.shop.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaShopCategoryDutyTwoDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutyCreationVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutySearchVo;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaShopCategoryDutyUpdateVo;
import com.bamboocloud.cdp.user.mama.shop.mapper.MamaShopDutyMapper;
import com.bamboocloud.cdp.user.mama.shop.repository.MamaShopCategoryDutyQueryDslRepository;
import com.bamboocloud.cdp.user.mama.shop.repository.MamaShopDutyRepository;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopCategoryDuty;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class MamaShopCategoryDutyServiceImpl extends BaseMamaService implements MamaShopCategoryDutyService {
    @Autowired
    private OperationLogUtil operationLogUtil;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private MamaShopDutyMapper mamaShopDutyMapper;

    @Autowired
    private MamaShopDutyRepository mamaShopDutyRepository;

    @Autowired
    private MamaShopCategorySelectedDutyService mamaShopCategorySelectedDutyService;

    @Autowired
    private MamaShopCategoryDutyQueryDslRepository mamaShopCategoryDutyQueryDslRepository;

    @Transactional(rollbackFor = Exception.class)
    public ShopCategoryDuty create(ShopCategoryDuty shopCategoryDuty){
        operationLogUtil.setCreateCommonInformation(shopCategoryDuty, UserTypeConstant.MAMA);
        shopCategoryDuty = mamaShopDutyRepository.saveAndFlush(shopCategoryDuty);
        entityManager.refresh(shopCategoryDuty);
        return shopCategoryDuty;
    }

    @Transactional(rollbackFor = Exception.class)
    public ShopCategoryDuty update(ShopCategoryDuty shopCategoryDuty){
        operationLogUtil.setUpdateCommonInformation(shopCategoryDuty,UserTypeConstant.MAMA);
        shopCategoryDuty = mamaShopDutyRepository.saveAndFlush(shopCategoryDuty);
        entityManager.refresh(shopCategoryDuty);
        return shopCategoryDuty;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaShopCategoryDutyDto create(MamaShopCategoryDutyCreationVo mamaShopCategoryDutyCreationVo) {
        ShopCategoryDuty shopCategoryDuty = mamaShopDutyMapper.toEntityForCreation(mamaShopCategoryDutyCreationVo);
        shopCategoryDuty = create(shopCategoryDuty);
        return mamaShopDutyMapper.toDto(shopCategoryDuty);
    }

    @Override
    public MamaShopCategoryDutyDto get(Integer id) {
        ShopCategoryDuty shopCategoryDuty = mamaShopDutyRepository.findById(id).orElse(new ShopCategoryDuty());
        return mamaShopDutyMapper.toDto(shopCategoryDuty);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaShopCategoryDutyDto update(MamaShopCategoryDutyUpdateVo mamaShopCategoryDutyUpdateVo) {
        ShopCategoryDuty shopCategoryDuty = mamaShopDutyRepository.findById(mamaShopCategoryDutyUpdateVo.getId()).orElseThrow();
        shopCategoryDuty = mamaShopDutyMapper.toEntityForUpdate(mamaShopCategoryDutyUpdateVo, shopCategoryDuty);
        shopCategoryDuty = update(shopCategoryDuty);
        return mamaShopDutyMapper.toDto(shopCategoryDuty);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) {
      if (id!=null){
          mamaShopCategorySelectedDutyService.deleteByDutyId(id);
          mamaShopDutyRepository.deleteById(id);
      }
    }

    @Override
    public List<MamaShopCategoryDutyDto> getAll() {
        List<ShopCategoryDuty> shopCategoryDutyList = mamaShopDutyRepository.findAll();
        return mamaShopDutyMapper.toDtos(shopCategoryDutyList);
    }

    @Override
    public MamaShopCategoryDutyPageDto search(MamaShopCategoryDutySearchVo mamaShopCategoryDutySearchVo) {
        return mamaShopCategoryDutyQueryDslRepository.search(mamaShopCategoryDutySearchVo);
    }
    @Override
    public List<MamaShopCategoryDutyTwoDto> getAllByShopCategoryId(Integer shopCategoryId) {
        List<MamaShopCategoryDutyTwoDto> list=mamaShopCategoryDutyQueryDslRepository.getAllByShopCategoryId(shopCategoryId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list;
    }
}
