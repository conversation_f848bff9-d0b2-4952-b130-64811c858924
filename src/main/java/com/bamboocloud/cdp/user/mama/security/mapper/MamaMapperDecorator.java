/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaMapperDecorator.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.mama.security.mapper;

import com.bamboocloud.cdp.framework.core.domain.dto.permission.FwkPermissionDto;
import com.bamboocloud.cdp.framework.core.domain.model.basic.FwkIdInteger;
import com.bamboocloud.cdp.user.common.dto.base.permission.GroupDto;
import com.bamboocloud.cdp.user.common.dto.base.permission.ModuleDto;
import com.bamboocloud.cdp.user.common.dto.base.permission.PermissionDto;
import com.bamboocloud.cdp.user.common.dto.base.permission.SubGroupDto;
import com.bamboocloud.cdp.user.common.dto.mama.MamaDto;
import com.bamboocloud.cdp.user.common.vo.mama.MamaCreationVo;
import com.bamboocloud.cdp.user.everyone.user.service.UserService;
import com.bamboocloud.cdp.user.sdk.constant.*;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.mama.Mama;
import com.bamboocloud.cdp.user.sdk.domain.entity.mama.MamaPermission;
import com.bamboocloud.cdp.user.sdk.domain.entity.mama.MamaRole;
import com.bamboocloud.cdp.user.sdk.util.RsaUtils;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Shu
 * @description:
 */
public abstract class MamaMapperDecorator implements MamaMapper {

    @Autowired
    private UserService constantService;

    @Autowired
    @Qualifier("delegate")
    private MamaMapper delegate;

    @Override
    public Mama toEntityForUpdate(MamaCreationVo mamaCreationVo, @MappingTarget Mama mama) {
        if (mamaCreationVo == null) {
            return null;
        }
        mama = delegate.toEntityForUpdate(mamaCreationVo, mama);
        mama.setDeletedUserNickName(null);
        mama.setDeletedUserType(null);
        mama.setDeletedUserId(null);
        mama.setDeletedUserName(null);
        mama.setDeletedDate(null);
        mama.setRoles(fwkIdIntegerListToMamaRoleList(mamaCreationVo.getRoles()));

        return mama;
    }

    protected List<MamaRole> fwkIdIntegerListToMamaRoleList(List<FwkIdInteger> list) {
        if (list == null) {
            return null;
        }

        List<MamaRole> list1 = new ArrayList<MamaRole>(list.size());
        for (FwkIdInteger fwkIdInteger : list) {
            list1.add(toEntityRole(fwkIdInteger));
        }

        return list1;
    }

    @Override
    public MamaDto toDto(Mama mama) {
        if (mama == null) {
            return null;
        }
        MamaDto mamaDto = delegate.toDto(mama);
        mamaDto.setGender(new BaseTypeDto(RsaUtils.encryptByPrivateKey(mama.getGenderCode()),
                RsaUtils.encryptByPrivateKey(constantService.listUserGenders().stream().filter(o -> o.getCode()
                                .equals(mama.getGenderCode())).findAny().orElse(UserTypeConstant.NULL)
                        .getName())));
        Set<ModuleDto> modules = new HashSet<>();
        for (MamaRole role : mama.getRoles()) {
            for (MamaPermission permission : role.getPermissions()) {
                try {
                    List<MamaPermissionConst> permissionConstList = MamaPermissionConst.findAllInternal();
                    for (MamaPermissionConst mamaPermissionConst : permissionConstList) {
                        if (permission.getCode().equals(mamaPermissionConst.getCode())) {
                            MamaPermissionSubGroupConst permissionSubGroup = mamaPermissionConst.getPermissionSubGroup();
                            MamaPermissionGroupConst permissionGroup = permissionSubGroup.getPermissionGroup();
                            MamaPermissionModuleConst permissionModule = permissionGroup.getPermissionModule();

                            ModuleDto moduleDto = modules.stream().filter(module -> module.getCode().equals(permissionModule.getCode())).findAny().orElse(null);
                            if (ObjectUtils.isEmpty(moduleDto)) {
                                moduleDto = new ModuleDto();
                                moduleDto.setCode(permissionModule.getCode());
                                moduleDto.setName(permissionModule.getName());

                                Set<GroupDto> groupDtoList = new HashSet<>();
                                GroupDto groupDto = new GroupDto();
                                groupDto.setName(permissionGroup.getName());
                                groupDto.setCode(permissionGroup.getCode());

                                Set<SubGroupDto> subGroupList = new HashSet<>();
                                SubGroupDto subGroupDto = new SubGroupDto();
                                subGroupDto.setName(permissionSubGroup.getName());
                                subGroupDto.setCode(permissionSubGroup.getCode());

                                Set<PermissionDto> mamaPermissionList = new HashSet<>();
                                PermissionDto permissionDto = new PermissionDto();
                                permissionDto.setName(mamaPermissionConst.getName());
                                permissionDto.setCode(mamaPermissionConst.getCode());
                                mamaPermissionList.add(permissionDto);
                                subGroupDto.setPermissions(mamaPermissionList);

                                subGroupList.add(subGroupDto);
                                groupDto.setSubGroups(subGroupList);
                                groupDtoList.add(groupDto);
                                moduleDto.setGroups(groupDtoList);

                                modules.add(moduleDto);
                            } else {
                                GroupDto groupDto = moduleDto.getGroups().stream().filter(group -> group.getCode()
                                        .equals(permissionGroup.getCode())).findAny().orElse(null);
                                if (ObjectUtils.isEmpty(groupDto)) {
                                    Set<GroupDto> groupDtoList = new HashSet<>();
                                    groupDto = new GroupDto();
                                    groupDto.setName(permissionGroup.getName());
                                    groupDto.setCode(permissionGroup.getCode());

                                    Set<SubGroupDto> subGroupList = new HashSet<>();
                                    SubGroupDto subGroupDto = new SubGroupDto();
                                    subGroupDto.setName(permissionSubGroup.getName());
                                    subGroupDto.setCode(permissionSubGroup.getCode());

                                    Set<PermissionDto> mamaPermissionList = new HashSet<>();
                                    PermissionDto permissionDto = new PermissionDto();
                                    permissionDto.setName(mamaPermissionConst.getName());
                                    permissionDto.setCode(mamaPermissionConst.getCode());
                                    mamaPermissionList.add(permissionDto);
                                    subGroupDto.setPermissions(mamaPermissionList);

                                    subGroupList.add(subGroupDto);
                                    groupDto.setSubGroups(subGroupList);
                                    groupDtoList.add(groupDto);
                                    moduleDto.getGroups().addAll(groupDtoList);
                                } else {
                                    SubGroupDto subGroupDto = groupDto.getSubGroups().stream().filter(subGroup ->
                                            subGroup.getCode().equals(permissionSubGroup.getCode())).findAny().orElse(null);
                                    if (ObjectUtils.isEmpty(subGroupDto)) {
                                        Set<SubGroupDto> subGroupList = new HashSet<>();
                                        subGroupDto = new SubGroupDto();
                                        subGroupDto.setName(permissionSubGroup.getName());
                                        subGroupDto.setCode(permissionSubGroup.getCode());

                                        Set<PermissionDto> mamaPermissionList = new HashSet<>();
                                        PermissionDto permissionDto = new PermissionDto();
                                        permissionDto.setName(mamaPermissionConst.getName());
                                        permissionDto.setCode(mamaPermissionConst.getCode());
                                        mamaPermissionList.add(permissionDto);
                                        subGroupDto.setPermissions(mamaPermissionList);

                                        subGroupList.add(subGroupDto);
                                        groupDto.getSubGroups().addAll(subGroupList);
                                    } else {
                                        PermissionDto permissionDto = new PermissionDto();
                                        permissionDto.setName(mamaPermissionConst.getName());
                                        permissionDto.setCode(mamaPermissionConst.getCode());
                                        subGroupDto.getPermissions().add(permissionDto);
                                    }
                                }
                            }
                        }
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        mamaDto.setModulePermissions(modules);
        mamaDto.setStatusCode(mama.getStatusCode());
        return mamaDto;
    }

    protected FwkPermissionDto mamaPermissionToFwkPermissionDto(MamaPermission mamaPermission) {
        if (mamaPermission == null) {
            return null;
        }

        FwkPermissionDto fwkPermissionDto = new FwkPermissionDto();

        fwkPermissionDto.setId(mamaPermission.getId());
        fwkPermissionDto.setCode(mamaPermission.getCode());

        return fwkPermissionDto;
    }
}
