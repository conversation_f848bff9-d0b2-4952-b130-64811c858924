/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopMallRepository.java
 * @createdDate: 2022/08/22 14:02:22
 *
 */

package com.bamboocloud.cdp.user.label.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagTopClassification;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface MamaTagTopClassificationRepository extends FwkBaseRepository<TagTopClassification,Long> {

    TagTopClassification findByNameAndDelFlag(String name, int i);
}
