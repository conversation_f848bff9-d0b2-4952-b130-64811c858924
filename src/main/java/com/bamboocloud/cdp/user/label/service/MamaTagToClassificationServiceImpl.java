package com.bamboocloud.cdp.user.label.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.user.label.repository.MamaTagToClassificationRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagToClassification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaTagToClassificationServiceImpl extends BaseMamaService implements MamaTagToClassificationService {

    @Autowired
    MamaTagToClassificationRepository mamaTagToClassificationRepository;

    @Override
    public void delete(Long tagToClassificationId) {
        //需要校验是否在使用 todo
        TagToClassification tagToClassification = mamaTagToClassificationRepository.findById(tagToClassificationId).orElseThrow();
        tagToClassification.setDelFlag(1);
        List<TagToClassification> list = mamaTagToClassificationRepository.findByParentId(tagToClassificationId);
        for (TagToClassification tagToClassification1 : list) {
            tagToClassification1.setDelFlag(1);
        }
        list.add(tagToClassification);
        mamaTagToClassificationRepository.saveAll(list);

    }

    @Override
    public void saveOrupdate(TagToClassification tagToClassification) {
  /*      // 需要校验名称是否重复
        TagToClassification tagToClassification1 = mamaTagToClassificationRepository.findByNameAndDelFlag(tagToClassification.getName(), 0);
        tagToClassification.setDelFlag(0);
        tagToClassification.setCreateTime(LocalDateTime.now());
        if (tagToClassification.getId() == null) {
            if (tagToClassification1 != null) {
                throw new RuntimeException("名称重复");
            }

        } else {
            if (tagToClassification1 != null && !Objects.equals(tagToClassification1.getId(), tagToClassification.getId())) {
                throw new RuntimeException("名称重复");
            }
            TagToClassification tagToClassification2 = mamaTagToClassificationRepository.findById(tagToClassification.getId()).orElseThrow();
            tagToClassification.setUpdateTime(LocalDateTime.now());
            tagToClassification.setCreateTime(tagToClassification2.getCreateTime());
            tagToClassification.setTagclassificationId(tagToClassification2.getTagclassificationId());
        }*/
        if(tagToClassification.getId() != null){
            TagToClassification tagToClassification2 = mamaTagToClassificationRepository.findById(tagToClassification.getId()).orElseThrow();
            tagToClassification.setUpdateTime(LocalDateTime.now());
            tagToClassification.setCreateTime(tagToClassification2.getCreateTime());
            tagToClassification.setTagclassificationId(tagToClassification2.getTagclassificationId());
        }else{
            tagToClassification.setDelFlag(0);
            tagToClassification.setCreateTime(LocalDateTime.now());
        }
        mamaTagToClassificationRepository.save(tagToClassification);
    }
}
