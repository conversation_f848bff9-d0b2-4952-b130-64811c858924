package com.bamboocloud.cdp.user.label.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.user.label.repository.MamaTagClassificationRepository;
import com.bamboocloud.cdp.user.label.repository.MamaTagToClassificationRepository;
import com.bamboocloud.cdp.user.label.repository.MamaTagTopClassificationRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagClassification;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagToClassification;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagTopClassification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaTagTopClassificationServiceImpl extends BaseMamaService implements MamaTagTopClassificationService {

    @Autowired
    MamaTagTopClassificationRepository mamaTagTopClassificationRepository;
    @Autowired
    MamaTagClassificationService mamaTagClassificationService;
    @Autowired
    MamaTagClassificationRepository  mamaTagClassificationRepository;
    @Autowired
    MamaTagToClassificationRepository mamaTagToClassificationRepository;

    @Override
    public List<TagTopClassification> search() {
        //查询所有delFlag=0的数据
        return mamaTagTopClassificationRepository.findAll().stream().filter(e -> e.getDelFlag() == 0).collect(Collectors.toList());
    }

    @Override
    public TagTopClassification findById(Long tagClassificationId) {
        return mamaTagTopClassificationRepository.findById(tagClassificationId).orElseThrow();
    }

    @Override
    public void delete(Long tagTopClassificationId) {
        //需要校验是否在使用 todo
        TagTopClassification tagClassification = mamaTagTopClassificationRepository.findById(tagTopClassificationId).orElseThrow();
        tagClassification.setDelFlag(1);
        List<TagClassification> tagClassifications = mamaTagClassificationRepository.findByTagTopClassificationId(tagTopClassificationId);
        tagClassifications.forEach(e -> {
            e.setDelFlag(1);
        });
        //通过tagClassificationIds查询
        List<TagToClassification> tagToClassifications =
            mamaTagToClassificationRepository.findByTagclassificationIdIn(tagClassifications.stream().map(TagClassification::getId).collect(Collectors.toList()));
        tagClassifications.forEach(e -> {
            e.setDelFlag(1);
        });
        mamaTagTopClassificationRepository.save(tagClassification);
        mamaTagClassificationRepository.saveAll(tagClassifications);
        mamaTagToClassificationRepository.saveAll(tagToClassifications);

    }

    @Override
    public void saveOrupdate(TagTopClassification tagTopClassification) {
        // 校验名称是否重复
        TagTopClassification tagTopClassification1 = mamaTagTopClassificationRepository.findByNameAndDelFlag(tagTopClassification.getName(), 0);
        tagTopClassification.setDelFlag(0);
        if (tagTopClassification.getId() == null) {
            if (tagTopClassification1 != null) {
                //throw new RuntimeException("该名称已存在");
                throw new BusinessException("NAME_ERROR", "该名称已存在");
            }
            tagTopClassification.setCreateTime(LocalDateTime.now());
        } else {
            if (tagTopClassification1 != null && !tagTopClassification1.getId().equals(tagTopClassification.getId())) {
                //throw new RuntimeException("该名称已存在");
                throw new BusinessException("NAME_ERROR", "该名称已存在");
            }
            TagTopClassification tagTopClassification2 = mamaTagTopClassificationRepository.findById(tagTopClassification.getId()).orElseThrow();
            tagTopClassification.setUpdateTime(LocalDateTime.now());
            tagTopClassification.setCreateTime(tagTopClassification2.getCreateTime());
        }
        mamaTagTopClassificationRepository.save(tagTopClassification);
    }
}
