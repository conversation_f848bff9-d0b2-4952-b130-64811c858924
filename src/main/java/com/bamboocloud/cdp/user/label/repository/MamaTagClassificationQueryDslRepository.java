/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. AgileAct Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaShopWxReviewHistoryQueryDslRepository.java
 * @createdDate: 2023/08/17 15:07:17
 *
 */

package com.bamboocloud.cdp.user.label.repository;

import cn.hutool.core.util.ObjectUtil;
import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaTagClassificationPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagClassificationDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagToClassificationDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaTagClassificationSearchVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QTagClassification;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QTagToClassification;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Component
@Slf4j
public class MamaTagClassificationQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public MamaTagClassificationPageDto search(MamaTagClassificationSearchVo searchVo) {
      QTagClassification qTagClassification = QTagClassification.tagClassification;
      QTagToClassification qTagToClassification = QTagToClassification.tagToClassification;
      BooleanBuilder builder = new BooleanBuilder();
      builder.and(qTagClassification.delFlag.eq(0));
      log.info("searchVo:{}",searchVo);
      log.info("searchVo.getTagTopclassificationId:{}",searchVo.getTagTopClassificationId());
      if(ObjectUtil.isNotEmpty(searchVo.getTagTopClassificationId())){
          builder.and(qTagClassification.tagTopClassificationId.eq(searchVo.getTagTopClassificationId()));
      }
      if (ObjectUtil.isNotEmpty(searchVo.getClassificationName())) {
          builder.and(qTagClassification.name.like("%" + searchVo.getClassificationName() + "%"));
      }
      if (ObjectUtil.isNotEmpty(searchVo.getToClassificationName())) {
          builder.and(qTagToClassification.name.like("%" + searchVo.getToClassificationName() + "%"));
      }
      //分页查询TagClassification数据封装到TagClassificationPageDto中
        MamaTagClassificationPageDto mamaTagClassificationPageDto = new MamaTagClassificationPageDto();
        mamaTagClassificationPageDto.setTotalCount(queryFactory.select(qTagClassification.id).from(qTagClassification).leftJoin(qTagToClassification).on(qTagClassification.id.eq(qTagToClassification.tagclassificationId)).where(builder).distinct().fetchCount());
        mamaTagClassificationPageDto.setTagClassificationDtoList(queryFactory.select(Projections.constructor(TagClassificationDto.class,
                qTagClassification.id,
                qTagClassification.name,
                qTagClassification.description,
                qTagClassification.usageMethod,
                qTagClassification.needImage,
                qTagClassification.delFlag,
                qTagClassification.tagTopClassificationId
        )).distinct().from(qTagClassification).leftJoin(qTagToClassification).on(qTagClassification.id.eq(qTagToClassification.tagclassificationId)).where(builder).offset(searchVo.getOffset()).limit(searchVo.getLimit()).fetch());
         if(CollectionUtils.isNotEmpty(mamaTagClassificationPageDto.getTagClassificationDtoList())){
             List<Long> tagClassificationDtoIds = mamaTagClassificationPageDto.getTagClassificationDtoList().stream().map(TagClassificationDto::getId).toList();
             BooleanBuilder buildera = new BooleanBuilder();
             buildera.and(qTagToClassification.tagclassificationId.in(tagClassificationDtoIds));
             buildera.and(qTagToClassification.delFlag.eq(0));
             if (searchVo.getToClassificationName() != null) {
                 buildera.and(qTagToClassification.name.like("%" + searchVo.getToClassificationName() + "%"));
                 }
             List<TagToClassificationDto> tagToClassificationListAll = queryFactory.select(Projections.constructor(TagToClassificationDto.class,
                     qTagToClassification.id,
                     qTagToClassification.name,
                     qTagToClassification.picUrl,
                     qTagToClassification.tagclassificationId,
                     qTagToClassification.delFlag,
                     qTagToClassification.hasChildren,
                     qTagToClassification.parentId,
                     qTagToClassification.usageMethod,
                     qTagToClassification.needImage
             )).distinct().from(qTagToClassification).where(buildera).fetch();

             // 先将 tagToClassificationListAll 转换为 Map，以 tagclassificationId 为键
             Map<Long, List<TagToClassificationDto>> tagMap = tagToClassificationListAll.stream()
                 .collect(Collectors.groupingBy(TagToClassificationDto::getTagclassificationId));
           // 遍历分类列表并设置对应的标签树
             for (TagClassificationDto tagClassificationDto : mamaTagClassificationPageDto.getTagClassificationDtoList()) {
                 // 从 Map 中快速获取对应的标签列表
                 List<TagToClassificationDto> tagToClassificationList = tagMap.getOrDefault(tagClassificationDto.getId(), Collections.emptyList());
                 // 设置标签树
                 tagClassificationDto.setTagToClassificationList(buildTree(tagToClassificationList));
             }
         }
        return mamaTagClassificationPageDto;







    }
    public List<TagToClassificationDto> buildTree(List<TagToClassificationDto> flatList) {
        if (flatList == null || flatList.isEmpty()) return Collections.emptyList();
        // Step 1: 将所有节点放入 map，便于后续快速查找
        Map<Long, TagToClassificationDto> nodeMap = flatList.stream()
            .collect(Collectors.toMap(TagToClassificationDto::getId, Function.identity()));
        // Step 2: 构建父子关系
        List<TagToClassificationDto> rootNodes = new ArrayList<>();
        for (TagToClassificationDto node : flatList) {
            Long parentId = node.getParentId();
            if (parentId.equals(-1L) || !nodeMap.containsKey(parentId)) {
                rootNodes.add(node);  // 根节点或无效父节点视为根
            } else {
                TagToClassificationDto parent = nodeMap.get(parentId);
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                }
                parent.getChildren().add(node);
            }
        }
        return rootNodes;
    }

    public  List<TagClassificationDto> findByTopId(Long tagTopClassificationId) {
        QTagClassification qTagClassification = QTagClassification.tagClassification;
        QTagToClassification qTagToClassification = QTagToClassification.tagToClassification;
        List<TagClassificationDto> tagClassificationDtos = queryFactory.select(Projections.constructor(TagClassificationDto.class,
                qTagClassification.id,
                qTagClassification.name,
                qTagClassification.description,
                qTagClassification.usageMethod,
                qTagClassification.needImage,
                qTagClassification.delFlag,
                qTagClassification.tagTopClassificationId
        )).from(qTagClassification).where(qTagClassification.tagTopClassificationId.eq(tagTopClassificationId)).fetch();
        if(CollectionUtils.isNotEmpty((tagClassificationDtos))){
            List<Long> tagClassificationDtoIds = tagClassificationDtos.stream().map(TagClassificationDto::getId).toList();
            List<TagToClassificationDto> tagToClassificationList = queryFactory.select(Projections.constructor(TagToClassificationDto.class,
                qTagToClassification.id,
                qTagToClassification.name,
                qTagToClassification.picUrl,
                qTagToClassification.tagclassificationId,
                qTagToClassification.delFlag,
                qTagToClassification.hasChildren,
                qTagToClassification.parentId,
                qTagToClassification.usageMethod,
                qTagToClassification.needImage
            )).from(qTagToClassification).where(qTagToClassification.tagclassificationId.in(tagClassificationDtoIds)).fetch();
            Map<Long, List<TagToClassificationDto>> classificationIdToTagsMap = tagToClassificationList.stream()
                .collect(Collectors.groupingBy(TagToClassificationDto::getTagclassificationId));
            for (TagClassificationDto tagClassificationDto : tagClassificationDtos) {
                Long classificationId = tagClassificationDto.getId();
                List<TagToClassificationDto> relatedTags = classificationIdToTagsMap.getOrDefault(classificationId, Collections.emptyList());
                tagClassificationDto.setTagToClassificationList(buildTree(relatedTags));
            }
        }
        return tagClassificationDtos;
    }

    public List<TagClassificationDto> getTagByTagTopClassificationId(Long tagTopClassificationId) {
        QTagClassification qTagClassification = QTagClassification.tagClassification;
        QTagToClassification qTagToClassification = QTagToClassification.tagToClassification;
        List<TagClassificationDto> tagClassificationDtos = queryFactory.select(Projections.constructor(TagClassificationDto.class,
            qTagClassification.id,
            qTagClassification.name,
            qTagClassification.description,
            qTagClassification.usageMethod,
            qTagClassification.needImage,
            qTagClassification.delFlag,
            qTagClassification.tagTopClassificationId
        )).from(qTagClassification).where(qTagClassification.tagTopClassificationId.eq(tagTopClassificationId)).fetch();
        if(CollectionUtils.isNotEmpty((tagClassificationDtos))){
            List<Long> tagClassificationDtoIds = tagClassificationDtos.stream().map(TagClassificationDto::getId).toList();
            List<TagToClassificationDto> tagToClassificationList = queryFactory.select(Projections.constructor(TagToClassificationDto.class,
                qTagToClassification.id,
                qTagToClassification.name,
                qTagToClassification.picUrl,
                qTagToClassification.tagclassificationId,
                qTagToClassification.delFlag,
                qTagToClassification.hasChildren,
                qTagToClassification.parentId,
                qTagToClassification.usageMethod,
                qTagToClassification.needImage
            )).from(qTagToClassification).where(qTagToClassification.tagclassificationId.in(tagClassificationDtoIds)).fetch();
            Map<Long, List<TagToClassificationDto>> classificationIdToTagsMap = tagToClassificationList.stream()
                .collect(Collectors.groupingBy(TagToClassificationDto::getTagclassificationId));
            for (TagClassificationDto tagClassificationDto : tagClassificationDtos) {
                Long classificationId = tagClassificationDto.getId();
                List<TagToClassificationDto> relatedTags = classificationIdToTagsMap.getOrDefault(classificationId, Collections.emptyList());
                tagClassificationDto.setTagToClassificationList(buildTree(relatedTags));
            }
        }
        return tagClassificationDtos;
    }
}
