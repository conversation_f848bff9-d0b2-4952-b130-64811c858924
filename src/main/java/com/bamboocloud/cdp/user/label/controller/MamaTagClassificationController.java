/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopController.java
 * @createdDate: 2022/07/25 17:51:25
 */

package com.bamboocloud.cdp.user.label.controller;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.domain.Result;
import com.bamboocloud.cdp.common.sdk.util.CollectionUtil;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaTagClassificationPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagClassificationDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagToClassificationDto;
import com.bamboocloud.cdp.user.common.enums.TagMenuEnum;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaTagClassificationSearchVo;
import com.bamboocloud.cdp.user.label.service.MamaTagClassificationService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagClassification;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaTagClassificationController extends BaseMamaController {

    @Autowired
    private MamaTagClassificationService mamaTagClassificationService;

    /**
     * 列表
     *
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_TAGCLASS_SEARCH_V1)
    public FwkApiResponse<MamaTagClassificationPageDto> search(@Valid @RequestBody MamaTagClassificationSearchVo searchVo) {
        log.debug("MamaTagClassificationController - search");
        log.debug("MamaTagClassificationController - search{}", JSON.toJSONString(searchVo));
        log.debug("MamaTagClassificationController - search111{}", searchVo.getTagTopClassificationId());
        return FwkApiResponse.success(mamaTagClassificationService.search(searchVo));
    }

    /**
     * 根据id获取标签
     *
     * @param tagClassificationId
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGCLASS_GET_BY_ID_V1)
    public FwkApiResponse<TagClassification> findById(@PathVariable("tagClassificationId") Long tagClassificationId) {
        log.debug("MamaTagClassificationController - getNickNameByShopIdAndVendorId");
        return FwkApiResponse.success(mamaTagClassificationService.findById(tagClassificationId));
    }

    /**
     * 删除标签
     *
     * @param tagClassificationId
     * @return
     * @throws ClientException
     */
    @DeleteMapping(MamaRouteConstant.MAMA_TAGCLASS_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("tagClassificationId") Long tagClassificationId) throws ClientException {
        log.debug("MamaTagClassificationController -- delete");
        mamaTagClassificationService.delete(tagClassificationId);
        return FwkApiResponse.success();
    }

    /**
     * 保存或更新标签
     *
     * @param tagClassification
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_TAGCLASS_SAVE_V1)
    public FwkApiResponse<String> saveOrupdate(@RequestBody TagClassification tagClassification) {
        log.debug("MamaTagClassificationController -- saveOrupdate");
        mamaTagClassificationService.saveOrupdate(tagClassification);
        return FwkApiResponse.success();
    }

    /**
     * 获取房型的标签数据
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGCLASS_GET_ROOM_TYPE_TAG_V1)
    public Result<List<TagClassificationDto>> getRoomTypeTagByTagTopClassificationIds() {
        Long tagTopClassificationId = TagMenuEnum.HOTEL_ROOM_FACILITY_SERVICE.getId();
        return Result.success(mamaTagClassificationService.getTagByTagTopClassificationId(tagTopClassificationId));
    }
    /**
     * 获取店铺的标签数据
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGCLASS_GET_SHOP_TAG_V1)
    public Result<List<TagClassificationDto>> getShopTagByTagTopClassificationIds() {
        Long tagTopClassificationId = TagMenuEnum.HOTEL_SHOP_FACILITY_SERVICE.getId();
        return Result.success(mamaTagClassificationService.getTagByTagTopClassificationId(tagTopClassificationId));
    }
    /**
     * 获取餐饮类型选择项
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGCLASS_GET_CATERING_TAG_V1)
    public Result<List<BaseTypeDto>> getCateringTagByTagTopClassificationIds() {
        Long tagTopClassificationId = TagMenuEnum.CATERING_SHOP_FACILITY_SERVICE.getId();
        List<TagClassificationDto> list =
            mamaTagClassificationService.getTagByTagTopClassificationId(tagTopClassificationId).stream().filter(r->r.getId().equals(19L)).toList();
        List<BaseTypeDto> baseTypeDtos = new ArrayList<>();
        if(CollectionUtil.isNotEmpty( list)){
            TagClassificationDto tagClassificationDto = list.get(0);
            List<TagToClassificationDto> tagToClassificationList = tagClassificationDto.getTagToClassificationList();
            if(CollectionUtil.isNotEmpty(tagToClassificationList)){
               baseTypeDtos = tagToClassificationList.stream().map(r->new BaseTypeDto(r.getId().toString(),r.getName())).toList();
            }
        }
        return Result.success(baseTypeDtos);
    }
}
