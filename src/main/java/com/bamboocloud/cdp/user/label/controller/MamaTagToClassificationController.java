/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.label.controller;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.label.service.MamaTagToClassificationService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagToClassification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description:
 */
@RestController
@Slf4j
public class MamaTagToClassificationController extends BaseMamaController {

    @Autowired
    private MamaTagToClassificationService mamaTagToClassificationService;

    /**
     * 删除标签
     *
     * @return
     * @throws ClientException
     */
    @DeleteMapping(MamaRouteConstant.MAMA_TAGTOCLASS_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("tagToClassificationId") Long tagToClassificationId) throws ClientException {
        log.debug("MamaTagToClassificationController -- delete");
        mamaTagToClassificationService.delete(tagToClassificationId);
        return FwkApiResponse.success();
    }

    /**
     * 保存或更新标签
     *
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_TAGTOCLASS_SAVE_V1)
    public FwkApiResponse<String> saveOrupdate(@RequestBody TagToClassification tagToClassification) {
        log.debug("MamaTagToClassificationController -- saveOrupdate");
        mamaTagToClassificationService.saveOrupdate(tagToClassification);
        return FwkApiResponse.success();
    }

}
