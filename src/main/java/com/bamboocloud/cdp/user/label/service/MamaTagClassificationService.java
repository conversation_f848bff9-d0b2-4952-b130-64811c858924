
package com.bamboocloud.cdp.user.label.service;

import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopTagSearchDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaTagClassificationPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagClassificationDto;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaTagClassificationSearchVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagClassification;
import com.bamboocloud.cdp.user.sdk.domain.vo.LabelVO;
import com.bamboocloud.cdp.user.sdk.domain.vo.vendor.shop.LabelRelationVo;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface MamaTagClassificationService {


    MamaTagClassificationPageDto search(MamaTagClassificationSearchVo searchVo);

    TagClassification findById(Long tagClassificationId);

    void delete(Long tagClassificationId);

    void saveOrupdate(TagClassification tagClassification);

    List<TagClassificationDto> findByTopId(Long tagTopClassificationId);

    /**
     * 根据标签分类id获取标签树
     * @param tagTopClassificationId
     * @return
     */
    List<TagClassificationDto> getTagByTagTopClassificationId(Long tagTopClassificationId);

    List<LabelRelationVo> getTagDetail(ShopTagSearchDto shopTagSearchDto);

    /**
     * 获取标签列表（当前只支持查询第一层级及所有层级，若需要查询其他需要进行代码升级）
     *
     * @param param 参数信息
     * @return 标签列表
     */
    List<LabelVO> getLabels(ShopTagSearchDto param);
}
