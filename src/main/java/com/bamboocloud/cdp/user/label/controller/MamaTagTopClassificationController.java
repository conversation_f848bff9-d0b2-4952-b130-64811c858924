/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.label.controller;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.label.service.MamaTagTopClassificationService;
import com.bamboocloud.cdp.user.sdk.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagTopClassification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @description:
 */
@RestController
@Slf4j
public class MamaTagTopClassificationController extends BaseMamaController {

    @Autowired
    private MamaTagTopClassificationService mamaTagTopClassificationService;

    /**
     * 列表
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGTOPCLASS_SEARCH_V1)
    public FwkApiResponse<List<TagTopClassification>> search() {
        log.debug("MamaTagClassificationController - search");
        return FwkApiResponse.success(mamaTagTopClassificationService.search());
    }

    /**
     * 根据id获取标签
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_TAGTOPCLASS_GET_BY_ID_V1)
    public FwkApiResponse<TagTopClassification> findById(@PathVariable("tagTopClassificationId") Long tagTopClassificationId) {
        log.debug("MamaTagClassificationController - getNickNameByShopIdAndVendorId");
        return FwkApiResponse.success(mamaTagTopClassificationService.findById(tagTopClassificationId));
    }

    /**
     * 删除标签
     *
     * @return
     * @throws ClientException
     */
    @PreAuthorize("{authentication.principal.username == " +
        "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME}")
    @DeleteMapping(MamaRouteConstant.MAMA_TAGTOPCLASS_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("tagTopClassificationId") Long tagTopClassificationId) throws ClientException {
        log.debug("MamaTagClassificationController -- delete");
        mamaTagTopClassificationService.delete(tagTopClassificationId);
        return FwkApiResponse.success();
    }

    /**
     * 保存或更新标签
     *
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
        "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME}")
    @PutMapping(MamaRouteConstant.MAMA_TAGTOPCLASS_SAVE_V1)
    public FwkApiResponse<String> saveOrupdate(@RequestBody TagTopClassification tagTopClassification) {
        log.debug("MamaTagClassificationController -- saveOrupdate");
        mamaTagTopClassificationService.saveOrupdate(tagTopClassification);
        return FwkApiResponse.success();
    }

}
