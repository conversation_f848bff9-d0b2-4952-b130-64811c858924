package com.bamboocloud.cdp.user.label.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.common.sdk.util.CollectionUtil;
import com.bamboocloud.cdp.common.sdk.util.Safes;
import com.bamboocloud.cdp.sale.sdk.domain.dto.vendor.ShopTagSearchDto;
import com.bamboocloud.cdp.sale.sdk.feign.IntegrationSaleService;
import com.bamboocloud.cdp.user.common.dto.mama.shop.MamaTagClassificationPageDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagClassificationDto;
import com.bamboocloud.cdp.user.common.dto.mama.shop.TagToClassificationDto;
import com.bamboocloud.cdp.user.common.enums.TagMenuEnum;
import com.bamboocloud.cdp.user.common.vo.mama.shop.MamaTagClassificationSearchVo;
import com.bamboocloud.cdp.user.label.repository.MamaTagClassificationQueryDslRepository;
import com.bamboocloud.cdp.user.label.repository.MamaTagClassificationRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TagClassification;
import com.bamboocloud.cdp.user.sdk.domain.vo.LabelVO;
import com.bamboocloud.cdp.user.sdk.domain.vo.vendor.shop.LabelRelationVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaTagClassificationServiceImpl extends BaseMamaService implements MamaTagClassificationService {

    @Autowired
    MamaTagClassificationRepository mamaTagClassificationRepository;
    @Autowired
    MamaTagClassificationQueryDslRepository mamaTagClassificationQueryDslRepository;
    @Autowired
    IntegrationSaleService integrationSaleService;

    @Override
    public MamaTagClassificationPageDto search(MamaTagClassificationSearchVo searchVo) {
        return mamaTagClassificationQueryDslRepository.search(searchVo);
    }

    @Override
    public TagClassification findById(Long tagClassificationId) {
        return mamaTagClassificationRepository.findById(tagClassificationId).orElseThrow();
    }

    @Override
    public void delete(Long tagClassificationId) {
        //需要校验是否在使用 todo
        TagClassification tagClassification = mamaTagClassificationRepository.findById(tagClassificationId).orElseThrow();
        tagClassification.setDelFlag(1);
        mamaTagClassificationRepository.save(tagClassification);

    }

    @Override
    public void saveOrupdate(TagClassification tagClassification) {
        //需要校验名字是否重复
        TagClassification tagClassification1 = mamaTagClassificationRepository.findByNameAndDelFlag(tagClassification.getName(), 0);
        tagClassification.setDelFlag(0);
        if (tagClassification.getId() == null) {
            if (tagClassification1 != null) {
                throw new BusinessException("NAME_ERROR","名字重复");
            }
            tagClassification.setCreateTime(LocalDateTime.now());
        } else {
            if (tagClassification1 != null && !Objects.equals(tagClassification1.getId(), tagClassification.getId())) {
                throw new BusinessException("NAME_ERROR","名字重复");
            }
            TagClassification tagClassification2 = mamaTagClassificationRepository.findById(tagClassification.getId()).orElseThrow();
            tagClassification.setUpdateTime(LocalDateTime.now());
            tagClassification.setCreateTime(tagClassification2.getCreateTime());
            tagClassification.setTagTopClassificationId(tagClassification2.getTagTopClassificationId());
        }
        mamaTagClassificationRepository.save(tagClassification);
    }

    @Override
    public List<TagClassificationDto> findByTopId(Long tagTopClassificationId) {
        return mamaTagClassificationQueryDslRepository.findByTopId(tagTopClassificationId);
    }

    @Override
    public List<TagClassificationDto> getTagByTagTopClassificationId(Long tagTopClassificationId) {

        return mamaTagClassificationQueryDslRepository.getTagByTagTopClassificationId(tagTopClassificationId);
    }

    @Override
    public List<LabelRelationVo> getTagDetail(ShopTagSearchDto shopTagSearchDto) {
        Long tagTopClassificationId = null;
        if (shopTagSearchDto.getBizType().equals(1)) {
            tagTopClassificationId = TagMenuEnum.HOTEL_ROOM_FACILITY_SERVICE.getId();
        } else if (shopTagSearchDto.getBizType().equals(2)) {
            tagTopClassificationId = TagMenuEnum.HOTEL_SHOP_FACILITY_SERVICE.getId();
        }

        // 获取（店铺，房型）的所有标签数据
        List<TagClassificationDto> list = this.getTagByTagTopClassificationId(tagTopClassificationId);
        // 获取当前店铺或房型所有选择的标签 ID
        List<String> tagIds = integrationSaleService.getLabelRelationFromFeign(shopTagSearchDto).getData();

        List<LabelRelationVo> labelRelationVos = new ArrayList<>();

        for (TagClassificationDto tagClassificationDto : list) {
            LabelRelationVo labelRelationVo = new LabelRelationVo();
            labelRelationVo.setTagclassificationId(tagClassificationDto.getId());
            labelRelationVo.setTagclassificationName(tagClassificationDto.getName());

            List<LabelRelationVo.TagData> tagDataList = new ArrayList<>();
            List<TagToClassificationDto> tagToClassificationList = tagClassificationDto.getTagToClassificationList();
            // 使用递归处理多层级结构
            processTagsRecursive(tagToClassificationList, tagIds, tagDataList);

            labelRelationVo.setList(tagDataList);
            if(CollectionUtil.isNotEmpty(tagDataList)){
                labelRelationVos.add(labelRelationVo);
            }
        }
        return labelRelationVos;
    }

    @Override
    public List<LabelVO> getLabels(ShopTagSearchDto param) {
        List<LabelRelationVo> list = this.getTagDetail(param);
        // 参数新增level方便后续扩展
        boolean isOnlyQueryFirstLevel = Objects.equals(1, param.getLevel());
        return Safes.of(list).stream()
                .map(item -> isOnlyQueryFirstLevel ? buildFirstLevelLabel(item) : buildFullLabelHierarchy(item))
                .toList();
    }

    /**
     * 递归处理标签树，筛选符合条件的标签
     *
     * @param tagNodes   当前层级的标签节点列表
     * @param selectedIds 选中的标签 ID 列表
     * @param result      最终要添加到的结果列表
     */
    private void processTagsRecursive(List<TagToClassificationDto> tagNodes,
                                      List<String> selectedIds,
                                      List<LabelRelationVo.TagData> result) {
        if (CollectionUtil.isEmpty(tagNodes)) {
            return;
        }

        for (TagToClassificationDto node : tagNodes) {
           if (selectedIds.contains(node.getId().toString())) {
                LabelRelationVo.TagData currentData = new LabelRelationVo.TagData();
                currentData.setTagId(node.getId());
                currentData.setTagNme(node.getName());
                currentData.setTagParentId(node.getParentId());
                if(node.getParentId().equals(-1L)){
                    result.add(currentData);
                }else{
                    //获取result中最新的一条数据
                    if(!result.isEmpty()){
                        LabelRelationVo.TagData parentData = result.get(result.size() - 1);
                        parentData.setTagData(currentData);
                    }
                }
            }
            // 递归处理子节点
            processTagsRecursive(node.getChildren(), selectedIds, result);
        }
    }

    /**
     * 构建第一级标签
     */
    private LabelVO buildFirstLevelLabel(LabelRelationVo item) {
        return LabelVO.builder()
                .id(String.valueOf(item.getTagclassificationId()))
                .name(item.getTagclassificationName())
                .icon(item.getIcon())
                .build();
    }

    /**
     * 构建完整的标签层级结构
     */
    private LabelVO buildFullLabelHierarchy(LabelRelationVo item) {
        LabelVO labelVO = buildFirstLevelLabel(item);
        List<LabelVO> childList = Safes.of(item.getList()).stream().map(secondItem -> {
            LabelVO secondVO = LabelVO.builder()
                    .id(String.valueOf(secondItem.getTagId()))
                    .name(secondItem.getTagNme())
                    .icon(secondItem.getIcon())
                    .build();
            if (Objects.isNull(secondItem.getTagData())) {
                return secondVO;
            }
            LabelVO thirdVO = LabelVO.builder()
                    .id(String.valueOf(secondItem.getTagData().getTagId()))
                    .name(secondItem.getTagData().getTagNme())
                    .icon(secondItem.getTagData().getIcon())
                    .build();
            secondVO.setChildren(Lists.newArrayList(thirdVO));
            return secondVO;
        }).toList();
        labelVO.setChildren(childList);
        return labelVO;
    }

}
