/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: GovQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.gov.security.repository;

import com.bamboocloud.cdp.boot.user.common.bo.gov.LoginGovBo;
import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.user.common.dto.gov.GovListDto;
import com.bamboocloud.cdp.user.common.dto.gov.GovNameDto;
import com.bamboocloud.cdp.user.common.dto.gov.GovPageDto;
import com.bamboocloud.cdp.user.common.dto.gov.GovRoleNameListDto;
import com.bamboocloud.cdp.user.common.entity.gov.QGovUserRole;
import com.bamboocloud.cdp.user.common.vo.gov.GovSearchVo;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.gov.Gov;
import com.bamboocloud.cdp.user.sdk.domain.entity.gov.QGov;
import com.bamboocloud.cdp.user.sdk.domain.entity.gov.QGovPermission;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@Component
public class GovQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    public GovPageDto search(GovSearchVo govSearchVo) {
        QGov qGov = QGov.gov;
        QGovUserRole qGovUserRole = QGovUserRole.govUserRole;
        QGovPermission qGovPermission = QGovPermission.govPermission;
        BooleanBuilder builder = getSearchBuilder(govSearchVo, qGov, qGovPermission);
        BlazeJPAQuery<GovListDto> jpaQuery = blazeJpaQueryFactory.from(qGov)
                .leftJoin(qGovUserRole).on(qGov.id.eq(qGovUserRole.govId))
                .leftJoin(qGovPermission).on(qGovUserRole.roleId.eq(qGovPermission.role.id))
                .where(builder)
                .distinct()
                .select(Projections.constructor(GovListDto.class,
                        qGov.id,
                        qGov.mobile,
                        qGov.name,
                        qGov.nickName,
                        qGov.enabled,
                        qGov.updatedUserName,
                        qGov.updatedDate, qGov.lastPasswordChangeDate));
        Map<String, GovListDto> govListDtoMap = jpaQuery
                .orderBy(qGov.updatedDate.desc())
                .offset((long) govSearchVo.getOffset() * govSearchVo.getLimit())
                .limit(govSearchVo.getLimit())
                .transform(GroupBy.groupBy(qGov.id).as(Projections.bean(GovListDto.class,
                        qGov.id,
                        qGov.mobile,
                        qGov.name,
                        qGov.nickName,
                        qGov.enabled,
                        qGov.updatedUserName,
                        qGov.updatedDate,
                        qGov.lastPasswordChangeDate,
                        GroupBy.list(Projections.constructor(GovRoleNameListDto.class,
                                qGovPermission.role.id, qGovPermission.role.name)).as("roles")
                )));
        GovPageDto govPageDto = new GovPageDto();
        govPageDto.setTotalCount(String.valueOf(blazeJpaQueryFactory.select(qGov.id.countDistinct()).from(qGov)
                .leftJoin(qGovUserRole).on(qGov.id.eq(qGovUserRole.govId))
                .leftJoin(qGovPermission).on(qGovUserRole.roleId.eq(qGovPermission.role.id))
                .where(builder).fetchFirst()));
        List<GovListDto> govList = new ArrayList<>(govListDtoMap.values());
        govPageDto.setGovs(govList);
        return govPageDto;
    }

    private BooleanBuilder getSearchBuilder(GovSearchVo govSearchVo, QGov qGov, QGovPermission qGovPermission) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qGov.deleted.isFalse());
        builder.and(qGov.mobile.ne(UserTypeConstant.DEV_SSR));
        if (FwkStringUtil.isNotBlank(govSearchVo.getMobile())) {
            builder.and(qGov.mobile.contains(govSearchVo.getMobile()));
        }
        if (FwkStringUtil.isNotBlank(govSearchVo.getName())) {
            builder.and(qGov.name.contains(govSearchVo.getName()));
        }
        if (govSearchVo.getRoleId() != null) {
            builder.and(qGov.roles.any().id.eq(govSearchVo.getRoleId()));
        }
        if (govSearchVo.getEnabled() != null && govSearchVo.getEnabled().equals(0)) {
            builder.and(qGov.enabled.isFalse());
        }
        if (govSearchVo.getEnabled() != null && govSearchVo.getEnabled().equals(1)) {
            builder.and(qGov.enabled.isTrue());
        }
        if (FwkStringUtil.isNotBlank(govSearchVo.getPermissionCode())) {
            builder.and(qGovPermission.code.eq(govSearchVo.getPermissionCode()));
        }
        return builder;
    }

    public Gov getById(String id) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.eq(id));
        return queryFactory.selectFrom(qGov).where(builder).fetchFirst();
    }

    public Gov getByMobile(String mobile) {
        if (mobile == null) {
            return null;
        }
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.deleted.eq(false));
        builder.and(qGov.mobile.eq(mobile));
        return queryFactory.select(Projections.constructor(
                Gov.class,
                qGov.id,
                qGov.mobileCountryCode,
                qGov.mobile,
                qGov.enabled)).from(qGov).where(builder).fetchFirst();
    }

    public Gov getByMobileAndIdNot(String mobile, String id) {
        if (mobile == null) {
            return null;
        }
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.ne(id));
        builder.and(qGov.mobile.eq(mobile));
        builder.and(qGov.deleted.eq(false));
        return queryFactory.select(Projections.constructor(
                Gov.class,
                qGov.id)).from(qGov).where(builder).fetchFirst();
    }

    public Gov getByEmailAndIdNot(String email, String id) {
        if (email == null) {
            return null;
        }
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.ne(id));
        builder.and(qGov.email.eq(email));
        builder.and(qGov.deleted.eq(false));
        return queryFactory.select(Projections.constructor(
                Gov.class,
                qGov.id)).from(qGov).where(builder).fetchFirst();
    }

    public Gov getByEmail(String email) {
        if (email == null) {
            return null;
        }
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.email.eq(email));
        builder.and(qGov.deleted.eq(false));
        return queryFactory.select(Projections.constructor(
                Gov.class,
                qGov.id)).from(qGov).where(builder).fetchFirst();
    }

    public GovNameDto getNameById(String id) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.eq(id));
        builder.and(qGov.deleted.eq(false));
        builder.and(qGov.enabled.eq(true));
        return queryFactory.select(Projections.constructor(
                        GovNameDto.class,
                        qGov.name,
                        qGov.nickName))
                .from(qGov)
                .where(builder)
                .fetchFirst();
    }

    public GovNameDto getFirstNameByPermissionCode(String permissionCode) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        QGovUserRole qGovUserRole = QGovUserRole.govUserRole;
        QGovPermission qGovPermission = QGovPermission.govPermission;
        builder.and(qGov.deleted.eq(false));
        builder.and(qGov.enabled.eq(true));
        if (FwkStringUtil.isNotBlank(permissionCode)) {
            qGovPermission.code.eq(permissionCode);
        }
        return queryFactory.select(Projections.constructor(
                        GovNameDto.class,
                        qGov.name,
                        qGov.nickName))
                .from(qGov)
                .leftJoin(qGovUserRole).on(qGov.id.eq(qGovUserRole.govId))
                .leftJoin(qGovPermission).on(qGovPermission.role.id.eq(qGovUserRole.roleId))
                .where(builder)
                .orderBy(qGov.createdDate.desc())
                .fetchFirst();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNickNameIsNull(List<String> ids, LoginGovBo loginGovBo) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.in(ids));
        queryFactory.update(qGov)
                .set(qGov.nickName, "")
                .set(qGov.updatedUserType, UserTypeConstant.GOV)
                .set(qGov.updatedUserNickName, FwkStringUtil.isNotBlank(loginGovBo.getNickName()) ? loginGovBo.getNickName() : loginGovBo.getName())
                .set(qGov.updatedUserId, loginGovBo.getId())
                .set(qGov.updatedUserName, loginGovBo.getName())
                .set(qGov.updatedDate, LocalDateTime.now())
                .where(builder)
                .execute();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateNickName(String id, String nickName, LoginGovBo loginGovBo) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        builder.and(qGov.id.eq(id));
        queryFactory.update(qGov)
                .set(qGov.nickName, nickName)
                .set(qGov.updatedUserType, UserTypeConstant.GOV)
                .set(qGov.updatedUserNickName, FwkStringUtil.isNotBlank(loginGovBo.getNickName()) ? loginGovBo.getNickName() : loginGovBo.getName())
                .set(qGov.updatedUserId, loginGovBo.getId())
                .set(qGov.updatedUserName, loginGovBo.getName())
                .set(qGov.updatedDate, LocalDateTime.now())
                .where(builder)
                .execute();
    }

    public Gov getByIdAndPermissionCode(String id, String permissionCode) {
        BooleanBuilder builder = new BooleanBuilder();
        QGov qGov = QGov.gov;
        QGovUserRole qGovUserRole = QGovUserRole.govUserRole;
        QGovPermission qGovPermission = QGovPermission.govPermission;
        builder.and(qGov.id.eq(id));
        builder.and(qGovPermission.code.eq(permissionCode));
        return queryFactory.select(qGov).from(qGov)
                .leftJoin(qGovUserRole).on(qGovUserRole.govId.eq(qGov.id))
                .leftJoin(qGovPermission).on(qGovUserRole.roleId.eq(qGovPermission.role.id))
                .where(builder).fetchFirst();
    }
}
