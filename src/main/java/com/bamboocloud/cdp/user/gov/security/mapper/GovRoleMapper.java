/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: GovRoleMapper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.gov.security.mapper;

import com.bamboocloud.cdp.user.common.dto.gov.role.GovRoleDto;
import com.bamboocloud.cdp.user.common.dto.gov.role.GovRoleListDto;
import com.bamboocloud.cdp.user.common.vo.gov.role.GovRoleCreationVo;
import com.bamboocloud.cdp.user.common.vo.gov.role.GovRoleUpdateVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.gov.GovRole;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface GovRoleMapper {
    GovRoleDto toDto(GovRole govRole);

    List<GovRoleDto> toDtos(List<GovRole> govRoles);

    GovRoleListDto toListDto(GovRole govRole);

    List<GovRoleListDto> toListDtos(List<GovRole> govRoles);

    GovRole toEntity(GovRoleDto govRoleDto);

    GovRole toEntityForCreation(GovRoleCreationVo govRoleCreationVo);

    GovRole toEntityForUpdate(GovRoleUpdateVo govRoleUpdateVo, @MappingTarget GovRole govRole);
}
