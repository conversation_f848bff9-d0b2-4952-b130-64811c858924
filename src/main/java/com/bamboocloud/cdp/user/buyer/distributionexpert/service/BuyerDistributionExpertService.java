/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAuthService.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.service;


import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPayBindPhoneDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPaySendVerificationCodeDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPaySignAcctProtocolDto;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.base.UnionPayNotificationBaseVo;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.domain.vo.DistributionConfigVO;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpert;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistStatisticsVO;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistributionInfoVO;

import java.util.List;
import java.util.Set;

public interface BuyerDistributionExpertService {
    /**
     * 创建达人
     */
    BuyerDistributionExpertDto createExpert(BuyerDistributionExpertCreationVo param);

    BuyerDistributionExpert update(BuyerDistributionExpert expert);

    /**
     * 查询达人信息
     */
    BuyerDistributionExpertDto getBuyerDistributionExpertInfo();

    /**
     * 绑定手机发送验证码
     *
     * @param param
     */
    UnionPaySendVerificationCodeDto bindPhoneSendCode(ExpertBindPhoneUnionSendCodeVo param);

    /**
     * 确认绑定手机
     *
     * @param param
     */
    UnionPayBindPhoneDto bindPhone(ExpertBindPhoneUnionApplyPhoneVo param);

    /**
     * 通联支付绑卡
     *
     * @param param
     */
    void bindBankToPrivate(ExpertBindBankCreationVo param);

    /**
     * 达人绑卡-支持的卡列表
     */
    List<SupportBankDto> supportBankList();

    /**
     * 达人提现签约
     */
    UnionPaySignAcctProtocolDto signAcctProtocol(ExpertSignAcctProtocolVo param);

    /**
     * 签约回调通知
     *
     * @param expertSignResultVo
     * @return
     */
    String receiveUnionPaySignResult(ExpertSignResultVo expertSignResultVo);

    /**
     * 达人提现
     *
     * @param
     * @return
     */
    BuyerDistributionExpertWithDrawDto withDraw(ExpertWithDrawVo param);

    void withDrawNotification(UnionPayNotificationBaseVo unionPayNotificationBaseVo);

    /**
     * 达人提现记录列表
     *
     * @param
     * @return
     */
    ExpertWithDrawPageDto withDrawList(ExpertWithDrawListVo param);

    /**
     * 通过ID批量查询分销达人
     *
     * @param expertIds
     * @return
     */
    List<BuyerDistributionExpertDto> listBuyerDistributionExpertInfoByIds(List<String> expertIds);

    /**
     * 查询达人收益及可提现余额信息
     *
     * @return
     */
    ExpertFundBalanceDto getFundBalance();

    /**
     * 获取达人邀请海报
     *
     * @param expertShareInfoVo
     * @return
     */
    String getExpertInvitePoster(ExpertShareInfoVo expertShareInfoVo);

    /**
     * 获取达人合伙人-（好友）
     *
     * @return
     */
    ExpertPartnerPageListDto getExpertPartner(ExpertWithPartnerRequest expertWithPartnerRequest);

    ExpertPartnerSummarizeDto getExpertPartnerSummarize();

    /**
     * 更新分销达人的分销总消费金额（单位：分）
     *
     * @param userId  用户ID
     * @param orderNo 订单号
     * @param amount  消费金额（只累加，不做减法）
     */
    void updateAmounts(String userId, String orderNo, Long amount);

    /**
     * 获取分销配置信息
     */
    DistributionConfigVO getDistributionConfig();

    /**
     * 分销统计（邀新）分销
     *
     * @param userIds 用户ID（分销员）列表
     * @return 用户Id邀新的数据
     */
    List<DistStatisticsVO> statistics(Set<String> userIds);

    /**
     * 分销员分销成果信息
     *
     * @param userId       分销员用户ID
     * @param distribution 分销达人信息
     * @return 分销成果
     */
    DistributionInfoVO achievements(String userId, BuyerDistributionExpert distribution);
}
