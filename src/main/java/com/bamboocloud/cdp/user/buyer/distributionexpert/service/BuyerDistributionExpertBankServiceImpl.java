/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAuthServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertBankRepository;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertBank;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Service
@Slf4j
public class BuyerDistributionExpertBankServiceImpl extends BaseBuyerService implements BuyerDistributionExpertBankService {
    @Autowired
    private BuyerDistributionExpertBankRepository buyerDistributionExpertBankRepository;
    @Autowired
    private OperationLogUtil operationLogUtil;
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerDistributionExpertBank create(BuyerDistributionExpertBank bank) {
        operationLogUtil.setCreateCommonInformation(bank, UserTypeConstant.BUYER);
        //分销达人银行卡表
        buyerDistributionExpertBankRepository.saveAndFlush(bank);
        return bank;
    }
}