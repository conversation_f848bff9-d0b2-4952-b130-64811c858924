/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerService.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.security.service;


import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.user.common.constant.TextContentConstant;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerCertInfoDTO;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerCertValidatedResultDto;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerCurrentLoginResultDto;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerStatisticsDTO;
import com.bamboocloud.cdp.user.common.params.PageParam;
import com.bamboocloud.cdp.user.domain.vo.DistributionUserVO;
import com.bamboocloud.cdp.user.domain.vo.PageVO;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerCreatePayPasswordVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerDeletePayPasswordVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerInfoDownloadVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerUpdateLoginPasswordVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerUpdatePayPasswordVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerUpdateVo;
import com.bamboocloud.cdp.user.sdk.constant.TagConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.BuyerDistributorDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.BuyerSimpleDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.BuyerSimpleInfoDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.base.UserLoginInfoWxMini;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxMiniLoginResultDto;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Set;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Mo
 * @description:
 */
public interface BuyerService {

    /**
     * 对使用微信小程序授权登录的buyer进行创建或修改的校验
     *
     * @param wxMiniLoginResultDto
     * @return
     */
    boolean createBuyerIfNotExistWhenLogin(WxMiniLoginResultDto wxMiniLoginResultDto);

    /**
     * 对使用微信授权登录的buyer进行创建或修改的校验
     *
     * @return
     */
    Buyer getBuyerByWxMiniOpenId(String openId);

    /**
     * 通过wxAppOpenId来获取用户信息
     *
     * @return
     */
    Buyer getBuyerByWxAppOpenId(String wxAppOpenId);

    /**
     * 通过wxUnionId来获取用户信息
     *
     * @return
     */
    Buyer getBuyerByWxUnionId(String wxUnionId);

    /**
     * 对使用小程序授权登录的buyer进行创建或修改的校验并返回是否新用户
     *
     * @param mobile
     * @return
     */
    boolean createBuyerIfNotExistWhenLogin(String mobile);

    /**
     * 如果手机号不存在 创建用户
     *
     * @param mobile
     * @param shareBuyerId 分享用户id
     * @return
     */
    boolean createBuyerIfNotExistWhenLogin(String mobile, String shareBuyerId);

    /**
     * 根据手机号查询
     *
     * @param mobile
     * @return
     */
    Buyer getIdByMobile(String mobile);

    /**
     * 根据手机号查询
     *
     * @param mobile
     * @return
     */
    Buyer getBuyerByMobile(String mobile);

    /**
     * 根据手机号查询buyer
     *
     * @param mobile
     * @return
     */
    Buyer getWholeByMobile(String mobile);

    /**
     * 根据openId获取
     *
     * @param wxMiniOpenId
     * @return
     */
    Buyer getByWxMiniOpenId(String wxMiniOpenId);

    /**
     * 根据邮箱查询
     *
     * @param email
     * @return
     */
    Buyer getByEmail(String email);


    /**
     * 创建
     *
     * @param buyer
     * @return
     */
    Buyer create(Buyer buyer);

    /**
     * 修改
     *
     * @param buyer
     * @return
     */
    Buyer update(Buyer buyer);

    /**
     * 修改
     *
     * @param buyerUpdateVo
     * @return
     */
    Buyer update(BuyerUpdateVo buyerUpdateVo) throws IOException, ClientException;

    /**
     * 获取修改后的tag，根据性别来修改
     *
     * @param genderCode
     * @param oldTag
     * @return
     */
    String getUpdateTag(String genderCode, String oldTag);

    /**
     * 根据ID获取
     *
     * @param id
     * @return
     */
    Buyer get(String id);

    /**
     * 根据ID获取
     *
     * @param id
     * @return
     */
    Buyer getBuyer(String id);

    /**
     * 根据手机号获取
     *
     * @param mobile
     * @param id
     * @return
     */
    Buyer getByMobileAndIdNot(String mobile, String id);

    /**
     * 根据邮箱获取
     *
     * @param email
     * @param id
     * @return
     */
    Buyer getByEmailAndIdNot(String email, String id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(String id);

    /**
     * 保存小程序系统信息
     *
     * @param userLoginInfoWxMini
     */
    void wxMiniSaveSystemInfo(@RequestBody UserLoginInfoWxMini userLoginInfoWxMini);

    /**
     * 查询
     *
     * @param id
     * @return
     */
    BuyerSimpleDto getSimpleById(String id);

    /**
     * 通过accessToken登录获取除accessToken外的其他数据
     *
     * @return
     */
    BuyerCurrentLoginResultDto getCurrentBuyerInfo();

    /**
     * 上传图片到oss
     *
     * @param vendorId
     * @param tempPath
     * @param objectPath
     * @param isPublic
     * @return
     */
    String uploadToOss(String vendorId, String tempPath, String objectPath, boolean isPublic) throws ClientException;

    /**
     * 上传图片到oss
     *
     * @param vendorId
     * @param tempPath
     * @param objectPath
     * @param isPublic
     * @return
     */
    String uploadToOssTwo(String vendorId, String tempPath, String objectPath, boolean isPublic) throws ClientException;

    /**
     * 上传图片到oss
     *
     * @param fwkFileAliOssStsFileAuth
     * @param inputStream
     * @param objectPath
     * @param userId
     * @return
     * @throws IOException
     */
    String saveWxLogoImageToOss(String fwkFileAliOssStsFileAuth, InputStream inputStream, String objectPath,
                                String userId) throws IOException, ClientException;

    /**
     * 注销账号
     */
    void logout();

    /**
     * 根据生日来修改标签
     *
     * @param buyer
     * @return
     */
    Buyer updateTagByBirthday(Buyer buyer);

    /**
     * 获取年龄范围的集合
     *
     * @return
     */
    List<TagConstant> getYearTagList();

    /**
     * 回去当前用户是否实名认证了
     *
     * @return
     */
    Boolean getCertValidated(String id);

    /**
     * 根据身份证号码查询用户
     *
     * @param certNumber
     * @return
     */
    Buyer getByCertNumber(String certNumber);

    /**
     * 根据用户id来获取是否为测试人员
     *
     * @return
     */
    Boolean getTestOnly(String id);


    /**
     * 通过AppleId查询buyer
     *
     * @param appleId
     * @return
     */
    Buyer getByAppleId(String appleId);

    /**
     * 查询所有buyer
     *
     * @return
     */
    List<Buyer> list();

    /**
     * 批量修改
     *
     * @param buyers
     */
    void bulkUpdate(List<Buyer> buyers);

    /**
     * 新增密码
     *
     * @param buyerCreatePayPasswordVo
     */
    void createPayPassword(BuyerCreatePayPasswordVo buyerCreatePayPasswordVo);

    /**
     * 修改养老支付密码
     *
     * @param buyerUpdatePayPasswordVo
     */
    void updatePayPassword(BuyerUpdatePayPasswordVo buyerUpdatePayPasswordVo);

    /**
     * 关闭养老支付密码
     *
     * @param buyerDeletePayPasswordVo
     */
    void deletePayPassword(BuyerDeletePayPasswordVo buyerDeletePayPasswordVo);

    /**
     * 绑定手机号
     *
     * @param mobile
     */
    void updateBindMobile(String mobile);

    /**
     * 修改登录密码（养老人员专属）
     *
     * @param buyerUpdateLoginPasswordVo
     */
    void updateLoginPassword(BuyerUpdateLoginPasswordVo buyerUpdateLoginPasswordVo);

    /**
     * 根据Id查看身份证号码
     *
     * @param id
     * @return
     */
    String getCertNumberById(String id);

    /**
     * 根据手机号查找用户,如果不存在则创建
     */
    String getBuyerIdByMobile(String mobile);

    /**
     * 修改登录密码（后端内部使用）
     *
     * @param password
     */
    void setLoginPassword(String password);

    /**
     * 根据id查找手机号
     */
    String getMobileById(String id);

    /**
     * 添加登出的信息
     *
     * @param
     */
    void addLoginOutInfo();

    /**
     * 添加切换app后登录的信息
     *
     * @param
     */
    void addLoginInfo();

    /**
     * 查询用户的头像和昵称
     *
     * @param id
     * @return
     */
    BuyerSimpleInfoDto getSimpleInfoById(String id);


    /**
     * 获得当前用户实名信息
     *
     * @return
     */
    BuyerCertInfoDTO getAuthentication();

    /**
     * 违规原因列表
     *
     * @return
     */
    List<TextContentConstant> labelTypeList();

    /**
     * 根据手机号查询用户
     *
     * @param mobile
     * @return
     */
    String getThirdBuyerIdByMobile(String mobile);

    /**
     * @description 根据id修改成云商通用户
     * <AUTHOR>
     */
    void updateBuyerYunstMember(String id, String columName);

    void changePersonalizedRecommendation(boolean enable);

    /**
     * 用户个人信息下载
     *
     * @param buyerId
     * @param buyerInfoDownloadVo
     */
    void downloadBuyerInfo(String buyerId, BuyerInfoDownloadVo buyerInfoDownloadVo);

    List<BuyerDistributorDto> getBuyerInfoByDistributorIds(List<String> distributorIds);

    BuyerDistributorDto getDistributorInfoByBuyerId(String buyerId);

    /**
     * 分析统计用户信息
     *
     * @param inviteUserIds 分销达人用户ID列表
     * @return 分销达人对应的要求用户数
     */
    List<BuyerStatisticsDTO> statisticsUser(Set<String> inviteUserIds);

    /**
     * 分析统计用户信息
     *
     * @param userId 分销达人用户ID
     * @return 分销达人对应的要求用户数
     */
    BuyerStatisticsDTO statisticsUser(String userId);

    /**
     * 分页查询分销达人邀请的用户列表
     *
     * @param pageParam 分页参数
     * @param shareId   分销人用户ID
     * @return 邀请的用户列表
     */
    PageVO<DistributionUserVO> page(PageParam pageParam, String shareId);

    BuyerCertValidatedResultDto getCurrentBuyerCertValidatedInfo();

    Buyer getBuyerInfoById(String id);
}
