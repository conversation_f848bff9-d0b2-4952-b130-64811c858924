/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopBuyerViewServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.service;

import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.user.buyer.shop.mapper.BuyerShopBuyerViewMapper;
import com.bamboocloud.cdp.user.buyer.shop.repository.BuyerShopBuyerViewRepository;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopBuyerViewDto;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.buyerview.BuyerShopBuyerViewCreationVo;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.buyerview.BuyerShopBuyerViewSearchVo;
import com.bamboocloud.cdp.user.sdk.constant.BuyerViewTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopBusinessHourDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopCategoryDutyDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopPartInfoDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopBuyerView;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.TrackShopBuyerView;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerShopBuyerViewServiceImpl extends BaseBuyerService implements BuyerShopBuyerViewService {

    @Autowired
    private BuyerShopBuyerViewRepository buyerShopBuyerViewRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BuyerShopService buyerShopService;

    @Autowired
    private BuyerShopBuyerViewMapper buyerShopBuyerViewMapper;

    @Autowired
    private BuyerShopBusinessHourService buyerShopBusinessHourService;

    @Autowired
    private BuyerShopCategoryDutyService buyerShopCategoryDutyService;

    @Autowired
    private BuyerTrackShopBuyerViewService buyerTrackShopBuyerViewService;

    @Transactional(rollbackFor = Exception.class)
    void create(ShopBuyerView shopBuyerView) {
        buyerShopBuyerViewRepository.save(shopBuyerView);
        createIndex();
    }

    private void createIndex() {
        mongoTemplate.indexOps(ShopBuyerView.class)
                .ensureIndex(new Index().on(ShopBuyerView.SHOP_ID.getKey(), Sort.Direction.ASC));
        mongoTemplate.indexOps(ShopBuyerView.class)
                .ensureIndex(new Index().on(ShopBuyerView.BUYER_ID.getKey(), Sort.Direction.ASC));
        mongoTemplate.indexOps(ShopBuyerView.class)
                .ensureIndex(new Index().on(ShopBuyerView.VIEW_DATE.getKey(), Sort.Direction.ASC));
        mongoTemplate.indexOps(ShopBuyerView.class)
                .ensureIndex(new Index().on(ShopBuyerView.SHOP_ID.getKey(), Sort.Direction.ASC)
                        .on(ShopBuyerView.VIEW_DATE.getKey(), Sort.Direction.DESC));
        mongoTemplate.indexOps(ShopBuyerView.class)
                .ensureIndex(new Index().on(ShopBuyerView.SHOP_ID.getKey(), Sort.Direction.ASC)
                        .on(ShopBuyerView.VIEW_DATE.getKey(), Sort.Direction.ASC));
    }


    @Override
    @Async(value = "userTrackThreadPool")
    public void create(BuyerShopBuyerViewCreationVo buyerShopBuyerViewCreationVo) {
        //当天的最大时间和最小时间
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime minTime = localDateTime.with(LocalTime.MIN);
        LocalDateTime maxTime = localDateTime.with(LocalTime.MAX);
        LoginBuyerBo loginBuyer = getLoginBuyer();
        ShopBuyerView shopBuyerView = buyerShopBuyerViewRepository.findFirstByBuyerIdAndShopIdAndViewDateBetween(loginBuyer.getId(), buyerShopBuyerViewCreationVo.getShopId(), minTime, maxTime);
        if (ObjectUtils.isEmpty(shopBuyerView)) {
            shopBuyerView = new ShopBuyerView();
            shopBuyerView.setShopId(buyerShopBuyerViewCreationVo.getShopId());
            BuyerShopDto buyerShopDto = buyerShopService.getSimpleById(buyerShopBuyerViewCreationVo.getShopId());
            shopBuyerView.setShopName(buyerShopDto.getName());
            shopBuyerView.setBuyerId(loginBuyer.getId());
            shopBuyerView.setBuyerNickName(loginBuyer.getNickName());
        } else {
            shopBuyerView.setViewDate(LocalDateTime.now());
        }
        create(shopBuyerView);

        //添加店铺浏览记录到埋点记录表
        TrackShopBuyerView trackShopBuyerView = new TrackShopBuyerView();
        trackShopBuyerView.setBuyerId(loginBuyer.getId());
        trackShopBuyerView.setShopId(shopBuyerView.getShopId());
        trackShopBuyerView.setShopName(shopBuyerView.getShopName());
        trackShopBuyerView.setViewDate(LocalDateTime.now());
        trackShopBuyerView.setTypeCode(BuyerViewTypeConstant.BUYER_VIEW_TYPE_START_VIEW.getCode());
        trackShopBuyerView.setBuyerNickName(shopBuyerView.getBuyerNickName());
        buyerTrackShopBuyerViewService.createTrack(trackShopBuyerView);
    }

    @Override
    @Async(value = "userTrackThreadPool")
    public void stopView(BuyerShopBuyerViewCreationVo buyerShopBuyerViewCreationVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        BuyerShopDto buyerShopDto = buyerShopService.getSimpleById(buyerShopBuyerViewCreationVo.getShopId());
        //添加店铺浏览记录到埋点记录表
        TrackShopBuyerView trackShopBuyerView = new TrackShopBuyerView();
        trackShopBuyerView.setBuyerId(loginBuyer.getId());
        trackShopBuyerView.setShopId(buyerShopBuyerViewCreationVo.getShopId());
        if (!ObjectUtils.isEmpty(buyerShopDto)){
            trackShopBuyerView.setShopName(buyerShopDto.getName());
        }
        trackShopBuyerView.setViewDate(LocalDateTime.now());
        trackShopBuyerView.setTypeCode(BuyerViewTypeConstant.BUYER_VIEW_TYPE_END_VIEW.getCode());
        trackShopBuyerView.setBuyerNickName(loginBuyer.getNickName());
        buyerTrackShopBuyerViewService.createTrack(trackShopBuyerView);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bulkDelete(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            List<ShopBuyerView> shopBuyerViews = buyerShopBuyerViewRepository.findAllByIdIn(ids);
            if (!FwkCollectionUtil.isEmpty(shopBuyerViews)) {
                for (ShopBuyerView shopBuyerView : shopBuyerViews) {
                    shopBuyerView.setDeleted(true);
                }
                buyerShopBuyerViewRepository.saveAll(shopBuyerViews);
            }
        }
    }

    @Override
    public List<BuyerShopBuyerViewDto> search(BuyerShopBuyerViewSearchVo buyerShopBuyerViewSearchVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        LocalDateTime localDateTime = LocalDateTime.now();
        LocalDateTime minTime = localDateTime.with(LocalTime.MIN);
        LocalDateTime maxTime = localDateTime.with(LocalTime.MAX);
        LocalDateTime minusDays = minTime.minusDays(30);
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.and(ShopBuyerView.BUYER_ID.getKey()).is(loginBuyer.getId());
        criteria.and(ShopBuyerView.DELETED.getKey()).is(false);
        criteria.andOperator(Criteria.where(ShopBuyerView.VIEW_DATE.getKey()).gte(minusDays), Criteria.where(ShopBuyerView.VIEW_DATE.getKey()).lte(maxTime));
        query.addCriteria(criteria);
        query.with(PageRequest.of(buyerShopBuyerViewSearchVo.getPageNumber(), buyerShopBuyerViewSearchVo.getPageSize(), Sort.by(Sort.Direction.DESC, ShopBuyerView.VIEW_DATE.getKey())));
        List<ShopBuyerView> shopBuyerViews = mongoTemplate.find(query, ShopBuyerView.class);
        List<BuyerShopBuyerViewDto> buyerShopBuyerViews = buyerShopBuyerViewMapper.toDtos(shopBuyerViews);
        if (!CollectionUtils.isEmpty(buyerShopBuyerViews)) {
            for (BuyerShopBuyerViewDto buyerShopBuyerView : buyerShopBuyerViews) {
                String shopId = buyerShopBuyerView.getId();
                BuyerShopPartInfoDto shopPartInfoDto = buyerShopService.getPartInfoById(shopId);
                buyerShopBuyerView.setName(shopPartInfoDto.getName());
                buyerShopBuyerView.setLogoUrl(shopPartInfoDto.getLogoUrl());
                //获取营业时间
                List<BuyerShopCategoryDutyDto> shopCategoryDutyList = buyerShopCategoryDutyService.listByShopId(shopId);
                buyerShopBuyerView.setBuyerShopDuties(shopCategoryDutyList);
                //获取相关服务
                List<BuyerShopBusinessHourDto> shopBusinessHourList = buyerShopBusinessHourService.listByShopId(shopId);
                buyerShopBuyerView.setBuyerShopBusinessHours(shopBusinessHourList);
            }
        }
        return buyerShopBuyerViews;
    }
}
