/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaVendorConfigRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpert;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistStatisticsVO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Repository
public interface BuyerDistributionExpertRepository extends FwkBaseRepository<BuyerDistributionExpert, String> {
    BuyerDistributionExpert findFirstByBuyerIdAndDeletedIsFalse(String buyerId);

    BuyerDistributionExpert findByIdAndDeletedIsFalse(String id);

    List<BuyerDistributionExpert> findByDeletedIsFalseAndIdIn(List<String> ids);

    List<BuyerDistributionExpert> findAllByPidAndDeletedIsFalse(String pid);


    @Modifying
    @Query("UPDATE BuyerDistributionExpert d SET d.amounts = d.amounts + :amounts WHERE d.buyerId = :userId")
    int updateAmounts(@Param("userId") String userId, @Param("amounts") Long amounts);


    @Query("SELECT NEW com.bamboocloud.cdp.user.sdk.domain.vo.DistStatisticsVO(b.shareId, COUNT(b.id)) " +
            "FROM Buyer b " +
            "WHERE b.shareId IN :shareIds " +
            "GROUP BY b.shareId")
    List<DistStatisticsVO> countBuyersByShareIds(@Param("shareIds") Set<String> shareIds);
}
