package com.bamboocloud.cdp.user.buyer.usageagreement.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.sale.sdk.feign.IntegrationSaleService;
import com.bamboocloud.cdp.user.buyer.address.repository.BuyerAddressRepository;
import com.bamboocloud.cdp.user.buyer.security.service.BuyerDeviceService;
import com.bamboocloud.cdp.user.buyer.security.service.BuyerService;
import com.bamboocloud.cdp.user.common.dto.buyer.usageagreement.UserInfoCollectionListDto;
import com.bamboocloud.cdp.user.common.service.sms.SmsService;
import com.bamboocloud.cdp.user.common.vo.base.BaseUserSendSmsVerificationCodeVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerCheckVerificationCodeVo;
import com.bamboocloud.cdp.user.config.property.AppProperty;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerAddress;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDevice;
import com.bamboocloud.cdp.user.sdk.enums.ExceptionEnum;
import com.bamboocloud.cdp.user.sdk.util.DesUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserInfoCollectionListServiceImpl extends BaseBuyerService implements UserInfoCollectionListService {

    @Autowired
    private BuyerService buyerService;
    @Autowired
    private BuyerAddressRepository buyerAddressRepository;
    @Autowired
    private IntegrationSaleService integrationSaleService;
    @Autowired
    private BuyerDeviceService buyerDeviceService;
    @Autowired
    private FwkCacheService fwkCacheService;
    @Autowired
    private CacheConstant cacheConstant;
    @Autowired
    private SmsService smsService;
    @Autowired
    private AppProperty appProperty;
    @Autowired
    private DesUtils desUtils;

    @Override
    public UserInfoCollectionListDto getUserInfoCollectionList() {
        UserInfoCollectionListDto userInfoCollectionListDto = null;
        LoginBuyerBo loginBuyer = getLoginBuyer();
        if (ObjectUtil.isNotEmpty(loginBuyer)) {
            String key = cacheConstant.getKeyAuthLoginUser("UserInfoCollectionList", loginBuyer.getId());
            userInfoCollectionListDto = FwkJsonUtil.toObject(fwkCacheService.get(key), UserInfoCollectionListDto.class);
            if (ObjectUtil.isEmpty(userInfoCollectionListDto)) {
                userInfoCollectionListDto = buildUserInfoCollectionListDto(userInfoCollectionListDto, loginBuyer);
                if (ObjectUtil.isNotEmpty(userInfoCollectionListDto)) {
                    fwkCacheService.setEx(key, FwkJsonUtil.toJsonString(userInfoCollectionListDto), 10, TimeUnit.MINUTES);
                }
            }
        }
        return userInfoCollectionListDto;
    }

    @Override
    public void sendUserInfoCollectionListMobileVerificationCode(BaseUserSendSmsVerificationCodeVo baseUserSendVerificationCodeDto) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        String keyBuyerInfoList = cacheConstant.getKeyAuthLoginUser("UserInfoCollectionListMobileVerificationCode", loginBuyer.getId());
        if (ObjectUtil.isNotEmpty(fwkCacheService.get(keyBuyerInfoList))) {
            throw new BusinessException("YZM_YFS_QSHZS", "验证码已发送，请稍后再试");

        }
        String verificationCode = smsService.sendSmsVerificationCode(loginBuyer.getMobileCountryCode(),
            baseUserSendVerificationCodeDto.getMobile(),
            appProperty.getAliyun().getSms().getTemplate().getVerificationCode(),
            appProperty.getAliyun().getSms().getTemplate().getVerificationCode());
        fwkCacheService.setEx(keyBuyerInfoList, verificationCode, CacheConstant.USER_VERIFICATION_CODE_EXPIRATION_MINUTES,
            TimeUnit.MINUTES);
    }

    @Override
    public void checkUserInfoListVerificationCode(BuyerCheckVerificationCodeVo buyerCheckVerificationCodeVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        log.debug("UserInfoCollectionListServiceImpl - checkUserInfoListVerificationCode");
        String verificationCode = null;
        //输入错误验证码，超过6次，需锁定账号10分钟
        String sendNumberKey = getSmsCodeErrorSendNumberKey(buyerCheckVerificationCodeVo.getMobile());
        String number = fwkCacheService.get(sendNumberKey);
        int errorNumber = 0;
        if (FwkStringUtil.isNotBlank(number)) {
            errorNumber = Integer.parseInt(number);
        }
        if (errorNumber >= SystemConstant.NUMBER_SIX) {
            throw new BusinessException(ExceptionEnum.VERIFICATION_CODE_ERROR_NUMBER_MORE_SIX);
        }
        String keyBuyerInfoList = cacheConstant.getKeyAuthLoginUser("UserInfoCollectionListMobileVerificationCode", loginBuyer.getId());
        if (FwkStringUtil.isNotBlank(buyerCheckVerificationCodeVo.getMobile())) {
            verificationCode = fwkCacheService.get(keyBuyerInfoList);
        }
        if (FwkStringUtil.isBlank(verificationCode) || !buyerCheckVerificationCodeVo.getVerificationCode()
            .equals(verificationCode)) {
            //输入错误密码，超过6次，需锁定账号10分钟
            setErrorNumber(sendNumberKey, number, errorNumber);
            throw new BusinessException(ExceptionEnum.VERIFICATION_CODE_ERROR_OR_EXPIRED);
        } else {
            //验证成功后删除缓存
            fwkCacheService.delete(keyBuyerInfoList);
        }
    }

    @Override
    public Boolean getUserInfoCollectionListSkip() {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        if (ObjectUtil.isEmpty(loginBuyer)) {
            throw new BusinessException(ExceptionEnum.LOGIN_FAILURE);
        }
        Buyer buyer = buyerService.getBuyer(loginBuyer.getId());
        //养老用户并且没得手机号跳过手机号验证
        if (ObjectUtil.isEmpty(buyer.getMobile())) {
            return true;
        } else {
            return false;
        }
    }

    private String getSmsCodeErrorSendNumberKey(String mobile) {
        return "User_Info_List_Code_Error_Send:mobile:" + mobile;
    }

    private void setErrorNumber(String sendNumberKey, String number, int errorNumber) {
        if (FwkStringUtil.isBlank(number)) {
            //十分钟内第一次报错，存入redis中
            fwkCacheService.setEx(sendNumberKey, String.valueOf(1), 600, TimeUnit.SECONDS);
        } else {
            //剩余的过期时间
            Long expire = fwkCacheService.getExpire(sendNumberKey);
            errorNumber = errorNumber + 1;
            fwkCacheService.setEx(sendNumberKey, String.valueOf(errorNumber), expire, TimeUnit.SECONDS);
        }
    }

    private UserInfoCollectionListDto buildUserInfoCollectionListDto(UserInfoCollectionListDto userInfoCollectionListDto, LoginBuyerBo loginBuyer) {
        userInfoCollectionListDto = new UserInfoCollectionListDto();
        Buyer buyer = buyerService.getBuyer(loginBuyer.getId());
        List<BuyerAddress> buyerAddresses = buyerAddressRepository.findAllByBuyerId(loginBuyer.getId());
        log.info("个人信息收集清单-getUserInfoCollectionList-5用户id{}", loginBuyer.getId());
        Integer orderNo = integrationSaleService.totalTradeCount(loginBuyer.getId()).getData();
        List<BuyerDevice> buyerDevices = buyerDeviceService.getBuyerDevicesByBuyerId(loginBuyer.getId());
        //昵称
        if (ObjectUtil.isNotEmpty(buyer.getNickName())) {
            userInfoCollectionListDto.setNickName(buyer.getNickName());
            userInfoCollectionListDto.setNickNameNo(1);
        }
        //头像
        if (ObjectUtil.isNotEmpty(buyer.getAvatarUrl())) {
            userInfoCollectionListDto.setAvatarNo(1);
        }

        //生日
        if (ObjectUtil.isNotEmpty(buyer.getBirthday())) {
            userInfoCollectionListDto.setBirthday(buyer.getBirthday());
            userInfoCollectionListDto.setBirthdayNo(1);
        }
        //地址
        if (CollectionUtils.isNotEmpty(buyerAddresses)) {
            userInfoCollectionListDto.setAddressNo(buyerAddresses.size());
        }
        //姓名
        if (ObjectUtil.isNotEmpty(buyer.getName())) {
            userInfoCollectionListDto.setName(buyer.getName());
            userInfoCollectionListDto.setNameNo(1);
        }
        //手机号
        if (ObjectUtil.isNotEmpty(buyer.getMobile())) {
            userInfoCollectionListDto.setMobile(buyer.getMobile());
            userInfoCollectionListDto.setMobileNo(1);
        }
        //身份证号
        if (ObjectUtil.isNotEmpty(buyer.getCertNumber())) {
            //buyer.getCertNumber()是加密数据，需要前端解密
            userInfoCollectionListDto.setIdCard(desUtils.decrypt(buyer.getCertNumber()));
            userInfoCollectionListDto.setIdCardNo(1);
        }
        //用户账号信息
        if (ObjectUtil.isNotEmpty(buyer.getAppleId()) || ObjectUtil.isNotEmpty(buyer.getWxMiniOpenId())) {
            if (ObjectUtil.isNotEmpty(buyer.getAppleId()) && ObjectUtil.isNotEmpty(buyer.getWxMiniOpenId())) {
                userInfoCollectionListDto.setAccountNo(2);
            } else {
                userInfoCollectionListDto.setAccountNo(1);
            }
        }
        //订单记录
        if (ObjectUtil.isNotEmpty(orderNo)) {
            userInfoCollectionListDto.setOrderNo(orderNo);
        }
        //设备信息
        if (CollectionUtils.isNotEmpty(buyerDevices)) {
            //按照设备类型DeviceTypeCode去重

            userInfoCollectionListDto.setDeviceNo(buyerDevices.size());
            //用List.stream取出设备信息deviceTypeCode拼接成字符串用“，”隔开并且去掉重复的
            userInfoCollectionListDto.setDevice(
                buyerDevices.stream().map(BuyerDevice::getDeviceTypeCode).distinct().collect(Collectors.joining(",")));
            /*String deviceTypeCode = buyerDevices.stream().map(BuyerDevice::getDeviceTypeCode).collect(Collectors.joining(","));
            userInfoCollectionListDto.setDevice(deviceTypeCode);*/
        }
        return userInfoCollectionListDto;
    }


}
