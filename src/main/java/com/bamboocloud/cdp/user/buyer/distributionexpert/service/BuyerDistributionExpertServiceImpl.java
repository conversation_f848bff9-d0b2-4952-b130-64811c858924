/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAuthServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.service;

import com.alibaba.fastjson.JSON;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.common.sdk.util.BooleanUtil;
import com.bamboocloud.cdp.common.sdk.util.NumberUtil;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.enums.TypeCode4BuyerPointEnum;
import com.bamboocloud.cdp.market.sdk.domain.param.StatisticsPointParam;
import com.bamboocloud.cdp.market.sdk.domain.vo.BuyerStatisticsVO;
import com.bamboocloud.cdp.market.sdk.integration.BuyerPointFeign;
import com.bamboocloud.cdp.pay.sdk.common.constant.UnionPayShopConstant;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.*;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.conf.UnionPayBankDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPayQueryBalanceDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.trade.UnionPayWithdrawApplyDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.wx.bank.BankDto;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.base.UnionPayNotificationBaseVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.shop.conf.*;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.trade.UnionPayQueryBalanceVo;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.trade.UnionPayWithdrawApplyVo;
import com.bamboocloud.cdp.pay.sdk.integration.IntegrationPayService;
import com.bamboocloud.cdp.sale.sdk.domain.dto.GeneralConfigInnerDto;
import com.bamboocloud.cdp.sale.sdk.domain.dto.trade.DistributorIncomeDto;
import com.bamboocloud.cdp.sale.sdk.domain.param.TradeStatisticsParam;
import com.bamboocloud.cdp.sale.sdk.domain.vo.GeneralConfigVo;
import com.bamboocloud.cdp.sale.sdk.domain.vo.UserTradeStatisticsVO;
import com.bamboocloud.cdp.sale.sdk.feign.EveryoneGeneralConfigFeign;
import com.bamboocloud.cdp.sale.sdk.feign.IntegrationSaleService;
import com.bamboocloud.cdp.sale.sdk.feign.OrderPointFeign;
import com.bamboocloud.cdp.user.buyer.constant.BuyerConstant;
import com.bamboocloud.cdp.user.buyer.constant.RiskCheckConfigProperty;
import com.bamboocloud.cdp.user.buyer.distributionexpert.mapper.BuyerDistributionExpertBankMapper;
import com.bamboocloud.cdp.user.buyer.distributionexpert.mapper.BuyerDistributionExpertMapper;
import com.bamboocloud.cdp.user.buyer.distributionexpert.mapper.BuyerDistributionExpertWithDrawMapper;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertBankRepository;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertQueryDslRepository;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertRepository;
import com.bamboocloud.cdp.user.buyer.security.service.BuyerService;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerStatisticsDTO;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.common.entity.vendor.union.UnionPayReceivePayNotificationVo;
import com.bamboocloud.cdp.user.domain.vo.DistributionConfigVO;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.config.property.AppProperty;
import com.bamboocloud.cdp.user.sdk.constant.*;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.DistributionMessageDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.ExpertPointStatisticsDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpert;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertBank;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertWithDraw;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.VendorConfig;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistStatisticsVO;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistributionInfoVO;
import com.bamboocloud.cdp.user.sdk.enums.Exception4BuyerEnum;
import com.bamboocloud.cdp.user.sdk.service.NotiHistoryService;
import com.bamboocloud.cdp.user.sdk.util.DesUtils;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import com.bamboocloud.cdp.user.vendor.security.service.VendorConfigService;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.common.file.PolicyGenerateUtil;
import com.bamboocloud.cdp.util.sdk.common.util.MultipartFileConverter;
import com.bamboocloud.cdp.util.sdk.common.vo.qrcode.QrCodeRequestVo;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import com.freewayso.image.combiner.ImageCombiner;
import com.freewayso.image.combiner.enums.OutputFormat;
import com.freewayso.image.combiner.enums.ZoomMode;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Service
@Slf4j
public class BuyerDistributionExpertServiceImpl extends BaseBuyerService implements BuyerDistributionExpertService {

    @Autowired
    private AppProperty appProperty;

    @Autowired
    private DesUtils desUtils;
    @Autowired
    private BuyerService buyerService;
    @Autowired
    private BuyerDistributionExpertRepository buyerDistributionExpertRepository;
    @Autowired
    private IntegrationPayService integrationPayService;
    @Autowired
    private OperationLogUtil operationLogUtil;
    @Autowired
    private BuyerDistributionExpertMapper buyerDistributionExpertMapper;
    @Autowired
    private BuyerDistributionExpertBankRepository buyerDistributionExpertBankRepository;
    @Autowired
    private BuyerDistributionExpertBankMapper buyerDistributionExpertBankMapper;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private BuyerDistributionExpertBankService buyerDistributionExpertBankService;
    @Autowired
    private BuyerDistributionExpertWithDrawService buyerDistributionExpertWithDrawService;
    @Autowired
    private FwkCacheService fwkCacheService;
    @Autowired
    private BuyerDistributionExpertWithDrawMapper buyerDistributionExpertWithDrawMapper;
    @Autowired
    private MessagingUtil messagingUtil;
    @Autowired
    private IntegrationSaleService integrationSaleService;
    @Autowired
    private IntegrationUtilService integrationUtilService;
    @Autowired
    private FileConstant fileConstant;
    @Autowired
    private BuyerDistributionExpertQueryDslRepository buyerDistributionExpertQueryDslRepository;
    @Autowired
    private NotiHistoryService notiHistoryService;
    @Autowired
    private VendorConfigService vendorConfigService;
    @Autowired
    private RiskCheckConfigProperty riskCheckConfigProperty;
    @Autowired
    private CacheConstant cacheConstant;
    @Autowired
    private BuyerPointFeign buyerPointFeign;
    @Autowired
    private OrderPointFeign orderPointFeign;
    @Autowired
    private EveryoneGeneralConfigFeign everyoneGeneralConfigFeign;
    @Autowired
    @Lazy
    private BuyerDistributionExpertService buyerDistributionExpertService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerDistributionExpertDto createExpert(BuyerDistributionExpertCreationVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "createExpert" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 5);
        try {
            Buyer buyer = buyerService.getBuyer(loginBuyer.getId());
            if (!buyer.isCertValidated()) {
                throw new BusinessException(Exception4BuyerEnum.BUYER_CERT_NUMBER_NOT_EXIST);
            }
            if (buyer.isDistributionExpert()) {
                throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT);
            }
            param.setPid(FwkStringUtil.isBlank(param.getPid()) ? DistributionExpertConstant.MAMA_ID : param.getPid());
            //验证分享人达人数据是否存在
            BuyerDistributionExpert pExpert = null;
            if (!DistributionExpertConstant.MAMA_ID.equals(param.getPid())) {
                pExpert = buyerDistributionExpertRepository.findByIdAndDeletedIsFalse(param.getPid());
                if (ObjectUtils.isEmpty(pExpert)) {
                    throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT_SHART_NOT);
                }
            }
            String expertId = IdUtil.generateUserId();
            String certNumber = desUtils.decrypt(buyer.getCertNumber());
            String name = buyer.getName();
            // 开通分销达人的支付账户（如通联注册会员并实名）
            // UnionPayMemberRegistAndSetRealNameDto dto = this.activePayAccount(expertId, name, certNumber);
            BuyerDistributionExpert expert = new BuyerDistributionExpert();
            expert.setId(expertId);
            expert.setBuyerId(buyer.getId());
            expert.setPid(param.getPid());
            // expert.setExtPayUserId(Objects.nonNull(dto) ? dto.getUserId() : null);
            expert.setCertValidated(true);
            expert.setName(name);
            expert.setCertNumber(certNumber);
            expert.setCertType(MamaConstant.VENDOR_CERT_TYPE_ID_CARD.getCode());
            operationLogUtil.setCreateCommonInformation(expert, UserTypeConstant.BUYER);
            //保存达人表
            buyerDistributionExpertRepository.saveAndFlush(expert);
            //更新用户表
            buyer.setDistributionExpert(true);
            buyerService.update(buyer);
            //达人数据埋点和消息通知
            if (!DistributionExpertConstant.MAMA_ID.equals(param.getPid())) {
                ExpertPointStatisticsDto pointStatisticsDto = new ExpertPointStatisticsDto();
                pointStatisticsDto.setUuid(UUID.randomUUID().toString());
                pointStatisticsDto.setType(DistributionExpertConstant.EXPERT_POINT_1);
                pointStatisticsDto.setExpertId(param.getPid());
                pointStatisticsDto.setTag(MessagingConstant.USER_EXPERT_POINT_MESSAGING_TAG);
                messagingUtil.sendMessage(pointStatisticsDto, MessagingConstant.USER_TOPIC + SystemConstant.COLON
                        + MessagingConstant.USER_EXPERT_POINT_MESSAGING_TAG);

            }
            //达人创建后消息通知
            notiHistoryService.sendMessageToDistributor(new DistributionMessageDto(expert.getBuyerId(), LocalDateTime.now(), "加入达人成功", "恭喜您成为分销达人，赶紧来一起分享赚钱吧～", "DISTRIBUTION_MESSAGE_ADDEXPERT"));
            if (!ObjectUtils.isEmpty(pExpert)) {
                notiHistoryService.sendMessageToDistributor(new DistributionMessageDto(pExpert.getBuyerId(), LocalDateTime.now(), "邀请合伙人成功", "您邀请的用户" + name + "成为达人成功！", "DISTRIBUTION_MESSAGE_INVITEEXPERT"));
            }
            return buyerDistributionExpertMapper.toDto(expert);
        } finally {
            fwkCacheService.delete(lockKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerDistributionExpert update(BuyerDistributionExpert expert) {
        operationLogUtil.setUpdateCommonInformation(expert, UserTypeConstant.BUYER);
        //保存达人表
        buyerDistributionExpertRepository.saveAndFlush(expert);
        entityManager.refresh(expert);
        return expert;
    }

    @Override
    public BuyerDistributionExpertDto getBuyerDistributionExpertInfo() {
        LoginBuyerBo loginBuyer = getPrincipalLoginUser();
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (Objects.isNull(expert)) {
            return null;
        }
        BuyerDistributionExpertDto dto = buyerDistributionExpertMapper.toDto(expert);
        List<BuyerDistributionExpertBank> banks = buyerDistributionExpertBankRepository.findAllByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (!FwkCollectionUtil.isEmpty(banks)) {
            dto.setBindBanks(buyerDistributionExpertBankMapper.toDtos(banks));
        }
        return dto;
    }

    @Override
    public UnionPaySendVerificationCodeDto bindPhoneSendCode(ExpertBindPhoneUnionSendCodeVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击发送验证码
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "bindPhoneSendCode" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 60);
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (expert.isBindPhoneSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_HAS);
        }
        UnionPaySendCodeVo sendCodeVo = new UnionPaySendCodeVo();
        sendCodeVo.setBizUserId(expert.getId());
        sendCodeVo.setPhone(param.getPhone());
        sendCodeVo.setVerificationCodeType(UnionPayShopConstant.BINDING_PHONE);
        UnionPaySendVerificationCodeDto dto = integrationPayService.sendCode(sendCodeVo).getData();
        if (!dto.getSubCode().equals(UnionPayShopConstant.OK_STATUS_SUB)) {
            log.error("达人绑定手机号发送验证码失败: expertId:{} ,错误msg:{}", expert.getId(), dto.getSubMsg());
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_SEND_CODE);
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnionPayBindPhoneDto bindPhone(ExpertBindPhoneUnionApplyPhoneVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "bindPhone" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 60);

        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (expert.isBindPhoneSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_HAS);
        }
        UnionPayPhoneApplyVo phoneApplyVo = new UnionPayPhoneApplyVo();
        phoneApplyVo.setBizUserId(expert.getId());
        phoneApplyVo.setPhone(param.getPhone());
        phoneApplyVo.setVerificationCode(param.getVerificationCode());
        UnionPayBindPhoneDto dto = integrationPayService.phoneApply(phoneApplyVo).getData();
        if (!dto.getSubCode().equals(UnionPayShopConstant.OK_STATUS_SUB)) {
            log.error("达人绑定手机号失败: expertId:{} ,错误msg:{}", expert.getId(), dto.getSubMsg());
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_ERR);
        }
        expert.setBindPhoneSuccess(true);
        expert.setPhone(param.getPhone());
        expert.setBindPhoneDate(LocalDateTime.now());
        update(expert);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindBankToPrivate(ExpertBindBankCreationVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "bindBankToPrivate" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 5);
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        //实名校验
        if (!expert.isCertValidated()) {
            throw new BusinessException(Exception4BuyerEnum.BUYER_CERT_NUMBER_NOT_EXIST);
        }
        //手机号校验
        if (!expert.isBindPhoneSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_NOT);
        }
        //校验开户行是否允许
        if (!supportBankList().stream().map(SupportBankDto::getAccountBankCode).collect(Collectors.toList()).contains(param.getAccountBankCode())) {
            log.error("达人绑卡时，传入的开户行编码:{} 不在支持的开户行列表中", param.getAccountBankCode());
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_NOT_SUOPPORT);
        }
        BankDto vaildBankDto;
        try {
            vaildBankDto = integrationPayService.listBanksByAccountNumber(param.getCardNo()).getData();
        } catch (Exception e) {
            log.error("达人绑卡时校验卡信息失败：", e);
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_VAILD);
        }
        //校验开户行是否匹配
        if (CollectionUtils.isEmpty(vaildBankDto.getData()) || !param.getAccountBankCode().equals(vaildBankDto.getData().get(0).getAccountBankCode())) {
            log.error("达人绑卡时银行卡与开户行不匹配，传入开户行：{},实际开户行:{}", param.getAccountBankCode(), CollectionUtils.isEmpty(vaildBankDto.getData()) ? "" : vaildBankDto.getData().get(0).getAccountBankCode());
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_VAILD);
        }
        String bizUserId = expert.getId();
        UnionPayBankSendCodeVo unionPayBankSendCodeVo = new UnionPayBankSendCodeVo();
        unionPayBankSendCodeVo.setBizUserId(bizUserId);
        unionPayBankSendCodeVo.setName(expert.getName());
        unionPayBankSendCodeVo.setIdentityNo(expert.getCertNumber());
        unionPayBankSendCodeVo.setCardNo(param.getCardNo());
        unionPayBankSendCodeVo.setPhone(param.getPhone());
        UnionPayApplyBindBankCardDto bindBankCardResult = integrationPayService.bankSendCode(unionPayBankSendCodeVo).getData();
        if (!UnionPayShopConstant.OK_STATUS_SUB.equals(bindBankCardResult.getSubCode())) {
            log.error("bindBankToPrivate绑定银行卡失败:{}", bindBankCardResult.getSubMsg());
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_FAIL);
        }
        UnionPayGetBankVo unionPayGetBankVo = new UnionPayGetBankVo();
        unionPayGetBankVo.setCardNo(param.getCardNo());
        unionPayGetBankVo.setBizUserId(bizUserId);
        UnionPayBankDto getBankDto = integrationPayService.getBank(unionPayGetBankVo).getData();
        if (!UnionPayShopConstant.OK_STATUS_SUB.equals(getBankDto.getSubCode())) {
            log.error("bindBankToPrivate获取银行卡信息失败:{}", getBankDto.getSubMsg());
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_GET_BANK_FAIL);
        }
        UnionPayGetBankDto bankDto = getBankDto.getBindCardList().get(0);
        if (UnionPayConstant.BANK_APPLY_BIND.equals(bankDto.getBindState())) {
            BuyerDistributionExpertBank bank = new BuyerDistributionExpertBank();
            bank.setBuyerId(loginBuyer.getId());
            bank.setExpertId(bizUserId);
            bank.setAccountName(expert.getName());
            bank.setAccountNumber(param.getCardNo());
            bank.setCertNumber(expert.getCertNumber());
            bank.setCertType(expert.getCertType());
            bank.setBankName(bankDto.getBankName());
            bank.setBankCode(bindBankCardResult.getBankCode());
            bank.setBankAlias(bindBankCardResult.getBankName());
            bank.setPhone(param.getPhone());
            bank.setBankBranchName(bankDto.getBranchBankName());
            bank.setTranceNum(bindBankCardResult.getTranceNum());
            buyerDistributionExpertBankService.create(bank);
            expert.setBindBankSuccess(true);
            update(expert);
        } else {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_GET_BANK_FAIL);
        }
    }

    @Override
    public List<SupportBankDto> supportBankList() {
        List<SupportBankDto> list = new ArrayList<>();
        list.add(new SupportBankDto("工商银行", 1002));
        list.add(new SupportBankDto("建设银行", 1003));
        list.add(new SupportBankDto("农业银行", 1005));
        list.add(new SupportBankDto("中国银行", 1026));
        list.add(new SupportBankDto("招商银行", 1001));
        list.add(new SupportBankDto("邮政储蓄银行", 1066));
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UnionPaySignAcctProtocolDto signAcctProtocol(ExpertSignAcctProtocolVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "signAcctProtocol" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 2);
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        //实名校验
        if (!expert.isCertValidated()) {
            throw new BusinessException(Exception4BuyerEnum.BUYER_CERT_NUMBER_NOT_EXIST);
        }
        //手机号校验
        if (!expert.isBindPhoneSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_NOT);
        }
        //绑卡校验
        if (!expert.isBindBankSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_NOT);
        }
        //已签约校验
        if (expert.isSignAcctProtocol()) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_SIGN_ACCT_PROTOCOL_DONE);
        }
        //签约中校验
//        if(ShopConstant.ACCOUNT_SIGN_STATUS_SIGNING.getCode().equals(expert.getSignStatusCode())){
//            throw new BusinessException(Exception4BuyerEnum.EXPERT_SIGN_ACCT_PROTOCOL_ING);
//        }
        UnionPaySignApplyVo signApplyVo = new UnionPaySignApplyVo();
        signApplyVo.setBizUserId(expert.getId());
        signApplyVo.setSignAcctName(expert.getName());
        signApplyVo.setSource(1L);
        signApplyVo.setBackUrl(appProperty.getUserApi().getNotificationHost() + BuyerRouteConstant.BUYER_DISTRIBUTION_RECEIVE_SIGN_RESULT_V1);
        if (DistributionExpertConstant.EXPERT_SIGN_CHANNEL_2.equals(param.getChannel())) {
            signApplyVo.setJumpPageType(DistributionExpertConstant.EXPERT_SIGN_CHANNEL_2);
        }
        UnionPaySignAcctProtocolDto dto = integrationPayService.signApply(signApplyVo).getData();
        log.info("signAcctProtocol发起提现签约返回:{}", FwkJsonUtil.toJSONString(dto));
        if (!UnionPayShopConstant.OK_STATUS.equals(dto.getCode())) {
            log.error("signAcctProtocol发起提现签约失败:{}", dto.getSubMsg());
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.EXPERT_SIGN_ACCT_PROTOCOL_ERR);
        }
        expert.setSignStatusCode(ShopConstant.ACCOUNT_SIGN_STATUS_SIGNING.getCode());
        update(expert);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String receiveUnionPaySignResult(ExpertSignResultVo expertSignResultVo) {
        String result = "success";
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findByIdAndDeletedIsFalse(expertSignResultVo.getBizUserId());
        if (!ObjectUtils.isEmpty(expert)) {
            if (UnionPayConstant.SIGN_SUCCESS.equals(expertSignResultVo.getResult())) {
                expert.setSignAcctProtocol(true);
                expert.setAcctProtocolNo(expertSignResultVo.getAcctProtocolNo());
                expert.setSignAcctProtocolDate(LocalDateTime.now());
                expert.setSignStatusCode(ShopConstant.ACCOUNT_SIGN_STATUS_SUCCEED.getCode());
            } else if (UnionPayConstant.SIGN_FAIL.equals(expertSignResultVo.getResult())) {
                //签约失败
                log.error("达人通联提现签约回调-签约 失败，bizUserId：{},", expertSignResultVo.getBizUserId());
                expert.setSignStatusCode(ShopConstant.ACCOUNT_SIGN_STATUS_FAILED.getCode());
            }
            update(expert);
        } else {
            log.error("达人通联提现签约回调找不到达人，bizUserId：{}", expertSignResultVo.getBizUserId());
        }
        return result;
    }

    @Override
    public BuyerDistributionExpertWithDrawDto withDraw(ExpertWithDrawVo param) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        //加锁防止一直重复点击
        String lockKey = DistributionExpertConstant.EXPERT_LOCK_PRE + "withDraw" + loginBuyer.getId();
        lockVaildAndLock(lockKey, loginBuyer.getId(), 5);
        if (riskCheckConfigProperty.getWithDrawDisabled()) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_WITH_DRAW_DISABLED);
        }
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        //实名校验
        if (!expert.isCertValidated()) {
            throw new BusinessException(Exception4BuyerEnum.BUYER_CERT_NUMBER_NOT_EXIST);
        }
        //手机号校验
        if (!expert.isBindPhoneSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.MEMBER_BIND_PHONE_NOT);
        }
        //绑卡校验
        if (!expert.isBindBankSuccess()) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_BANK_BIND_BANK_NOT);
        }
        //提现签约校验
        if (!expert.isSignAcctProtocol()) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_SIGN_ACCT_PROTOCOL_NOT);
        }
        BuyerDistributionExpertBank bank = buyerDistributionExpertBankRepository.findByBuyerIdAndAccountNumberAndDeletedIsFalse(loginBuyer.getId(), param.getAccountNumber());
        //校验提现银行卡是否属于当前用户
        if (ObjectUtils.isEmpty(bank)) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_NOT_BANK);
        }
        //校验提现门槛
        VendorConfig vendorConfig = vendorConfigService.getFirst();
        if (!ObjectUtils.isEmpty(vendorConfig.getCashOutThreshold()) && param.getAmount().compareTo(vendorConfig.getCashOutThreshold()) < 0) {
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.EXPERT_LESS_THAN_STR);
        }
        //余额查询，比较余额是否足够
        UnionPayQueryBalanceDto queryBalanceDto = new UnionPayQueryBalanceDto();
        queryBalanceDto.setBizUserId(expert.getId());
        UnionPayQueryBalanceVo unionPayQueryBalanceVo = integrationPayService.queryBalance(queryBalanceDto).getData();
        if (FwkStringUtil.isBlank(unionPayQueryBalanceVo.getData())) {
            log.error("达人提现时通联查询余额失败，expertId：{},msg:{}", expert.getId(), unionPayQueryBalanceVo.getSubMsg());
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.EXPERT_INSUFFICIENT_BALANCE_ERR);
        }
        long availableAmount = unionPayQueryBalanceVo.getAllAmount() - unionPayQueryBalanceVo.getFreezenAmount();
        BigDecimal amount = param.getAmount().multiply(new BigDecimal(100));
        log.info("达人提现金额修改单位为分后等于：{}", amount.longValue());
        if (amount.longValue() > availableAmount) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_INSUFFICIENT_BALANCE);
        }
        LocalDateTime now = LocalDateTime.now();
        //构建提现请求参数
        String bizOrderNo = IdUtil.generateTradeId();
        UnionPayWithdrawApplyDto withdrawApplyDto = new UnionPayWithdrawApplyDto();
        withdrawApplyDto.setBizOrderNo(bizOrderNo);
        withdrawApplyDto.setBizUserId(expert.getId());
        withdrawApplyDto.setAmount(amount.longValue());
        //通联提现手续费添加，读取nacos配置
        BigDecimal channelFee = new BigDecimal(FwkStringUtil.isBlank(appProperty.getPayApi().getChannelFee()) ? "0.3" : appProperty.getPayApi().getChannelFee());
        if (channelFee.compareTo(param.getAmount()) > 0) {
            throw new BusinessException(Exception4BuyerEnum.EXPERT_LESS_THAN_CHANNEL_FEE);
        }
        withdrawApplyDto.setFee(channelFee.multiply(new BigDecimal(100)).longValue());
        withdrawApplyDto.setBackUrl(appProperty.getUserApi().getNotificationHost() + BuyerRouteConstant.BUYER_DISTRIBUTION_WITHDRAW_RESULT_V1);
        withdrawApplyDto.setBankCardPro(UnionPayConstant.BANK_CARD_PRO_PR);
        withdrawApplyDto.setBankCardNo(param.getAccountNumber());
        withdrawApplyDto.setValidateType(UnionPayShopConstant.VALIDATE_TYPE);
        //调用pay-api提现接口
        UnionPayWithdrawApplyVo withdrawApplyVo = integrationPayService.withdrawApply(withdrawApplyDto).getData();
        if (!Objects.equals(withdrawApplyVo.getSubCode(), UnionPayShopConstant.OK_STATUS_SUB)) {
            log.error("通联提现申请失败，bizOrderNo：{},msg：{}", bizOrderNo, withdrawApplyVo.getSubMsg());
            fwkCacheService.delete(lockKey);
            throw new BusinessException(Exception4BuyerEnum.EXPERT_WITH_DRAW_ERR);
        }
        //提现记录入库
        BuyerDistributionExpertWithDraw withDraw = new BuyerDistributionExpertWithDraw();
        withDraw.setBuyerId(expert.getBuyerId());
        withDraw.setExpertId(expert.getId());
        withDraw.setChannelFee(channelFee);
        withDraw.setExtPayAmount(param.getAmount());
        withDraw.setExtBizOrderNo(bizOrderNo);
        withDraw.setExtPayStatusCode(ShopExtPayFundWithDrawConstant.SHOP_EXT_PAY_FUND_WITH_DRAW_STATUS_PROCESSING.getCode());
        withDraw.setAccountName(bank.getAccountName());
        withDraw.setAccountNumber(param.getAccountNumber());
        withDraw.setBankName(bank.getBankName());
        withDraw.setBankAlias(bank.getBankAlias());
        withDraw.setExtOrderNo(withdrawApplyVo.getOrderNo());
        withDraw.setCreatedDate(now);
        //加个锁，防止通联回调时数据还没创建
        fwkCacheService.setEx(bizOrderNo, param.getAmount().toString(), 10, TimeUnit.SECONDS);
        buyerDistributionExpertWithDrawService.create(withDraw);
        fwkCacheService.delete(bizOrderNo);
        return buyerDistributionExpertWithDrawMapper.toDto(withDraw);
    }

    @Override
    public void withDrawNotification(UnionPayNotificationBaseVo unionPayNotificationBaseVo) {
        FwkApiResponse<String> stringFwkApiResponse = integrationPayService.receiveNotificationParam(JSON.toJSONString(unionPayNotificationBaseVo));
        log.info("达人提现回调响应内容：{}", JSON.toJSONString(stringFwkApiResponse));
        UnionPayReceivePayNotificationVo unionPayReceivePayNotificationVo = JSON.parseObject(stringFwkApiResponse.getData(), UnionPayReceivePayNotificationVo.class);
        while (fwkCacheService.hasKey(unionPayReceivePayNotificationVo.getBizOrderNo())) {
            log.debug("等待达人提现数据创建：提现单号：{}", unionPayReceivePayNotificationVo.getBizOrderNo());
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        BuyerDistributionExpertWithDraw withDraw = buyerDistributionExpertWithDrawService.getByExtBizOrderNo(unionPayReceivePayNotificationVo.getBizOrderNo());
        if (!ObjectUtils.isEmpty(withDraw)) {
            withDraw.setFinishDate(LocalDateTime.parse(unionPayReceivePayNotificationVo.getPayDatetime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            if (UnionPayShopConstant.ORDER_NOTICE_OK.equals(unionPayReceivePayNotificationVo.getStatus())) {
                withDraw.setExtPayStatusCode(ShopExtPayFundWithDrawConstant.SHOP_EXT_PAY_FUND_WITH_DRAW_STATUS_SUCCESS.getCode());
                //通联提现成功
                notiHistoryService.sendMessageToDistributor(new DistributionMessageDto(withDraw.getBuyerId(), LocalDateTime.now(), "提现到账", "您的提现" + withDraw.getExtPayAmount() + "元已到账，快去看看吧", "DISTRIBUTION_MESSAGE_WITHDRAW_SUCCESS"));
            }
            if (UnionPayShopConstant.ORDER_NOTICE_ERROR.equals(unionPayReceivePayNotificationVo.getStatus())) {
                withDraw.setExtPayStatusCode(ShopExtPayFundWithDrawConstant.SHOP_EXT_PAY_FUND_WITH_DRAW_STATUS_FAIL.getCode());
                //通联提现失败
                notiHistoryService.sendMessageToDistributor(new DistributionMessageDto(withDraw.getBuyerId(), LocalDateTime.now(), "提现失败", "您的提现" + withDraw.getExtPayAmount() + "元未能成功，前往重新发起提现", "DISTRIBUTION_MESSAGE_WITHDRAW_FAIL"));
            }
            buyerDistributionExpertWithDrawService.update(withDraw);
        }
    }

    @Override
    public ExpertWithDrawPageDto withDrawList(ExpertWithDrawListVo param) {
        return buyerDistributionExpertWithDrawService.withDrawList(getLoginBuyerBo().getId(), param);
    }

    @Override
    public List<BuyerDistributionExpertDto> listBuyerDistributionExpertInfoByIds(List<String> expertIds) {
        if (CollectionUtils.isEmpty(expertIds)) {
            return Lists.newArrayList();
        }
        List<BuyerDistributionExpert> experts = buyerDistributionExpertRepository.findByDeletedIsFalseAndIdIn(expertIds);
        return experts.stream().map(buyerDistributionExpertMapper::toDto).collect(Collectors.toList());
    }

    @Override
    public ExpertFundBalanceDto getFundBalance() {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        BuyerDistributionExpert distribution = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (ObjectUtils.isEmpty(distribution)) {
            throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT_NOT);
        }

        DistributorIncomeDto distributorIncomeDto = integrationSaleService.countDistributorPendingAndFinishedIncome(distribution.getId()).getData();
        ExpertFundBalanceDto dto = new ExpertFundBalanceDto();
        dto.setWaitRecordAmount(distributorIncomeDto.getPendentIncome().setScale(2, RoundingMode.HALF_UP));
        dto.setHasRecordAmount(distributorIncomeDto.getFinishedIncome().setScale(2, RoundingMode.HALF_UP));
        dto.setTotalExtPayAmount(buyerDistributionExpertWithDrawService.totalExtPayAmount(loginBuyer.getId()));

        // 查询体现余额信息
        UnionPayQueryBalanceVo queryBalanceVo = this.getBalanceInfo(distribution.getId());
        if (Objects.nonNull(queryBalanceVo)) {
            dto.setAvailableAmount(new BigDecimal(queryBalanceVo.getAllAmount() - queryBalanceVo.getFreezenAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            dto.setPendingAmount(queryBalanceVo.getFreezenAmount() == 0L ? BigDecimal.ZERO : new BigDecimal(queryBalanceVo.getFreezenAmount()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }

        dto.setExpertPartnerNum(buyerDistributionExpertRepository.findAllByPidAndDeletedIsFalse(distribution.getId()).size());
        dto.setWithdrawFee(new BigDecimal(FwkStringUtil.isBlank(appProperty.getPayApi().getChannelFee()) ? "0.3" : appProperty.getPayApi().getChannelFee()));
        dto.setTotalIncome(dto.getWaitRecordAmount().add(dto.getHasRecordAmount()).setScale(2, RoundingMode.HALF_UP));
        VendorConfig vendorConfig = vendorConfigService.getFirst();
        dto.setCashOutThreshold(vendorConfig.getCashOutThreshold());

        // 分销成果
        dto.setDistributionInfo(this.achievements(loginBuyer.getId(), distribution));
        return dto;
    }

    //防重复提交
    void lockVaildAndLock(String lockKey, String lockValue, long seconds) {
        if (fwkCacheService.hasKey(lockKey)) {
            throw new BusinessException(Exception4BuyerEnum.ACCESS_LIMIT_ERROR);
        }
        fwkCacheService.setEx(lockKey, lockValue, seconds, TimeUnit.SECONDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getExpertInvitePoster(ExpertShareInfoVo expertShareInfoVo) {
        LoginBuyerBo loginBuyer = this.getLoginBuyerBo();
        Buyer user = buyerService.getBuyer(loginBuyer.getId());
        String page = BuyerConstant.DEFAULT_DISTRIBUTION_EXPERT_INVITE_JUMP_PAGE;
        String scene = loginBuyer.getId();
        if (expertShareInfoVo != null) {
            page = expertShareInfoVo.getPage() != null ? expertShareInfoVo.getPage() : page;
            scene = expertShareInfoVo.getScene() != null ? expertShareInfoVo.getScene() : scene;
        }

        QrCodeRequestVo vo = new QrCodeRequestVo();
        vo.setPage(page);
        vo.setScene(scene);

        String hashKey = loginBuyer.getId() + ":" + loginBuyer.getNickName() + ":" + JSON.toJSONString(vo);
        String md5Name = DigestUtils.md5Hex(hashKey);
        if (StringUtils.isNotEmpty(user.getInvitePosterUrl())) {
            int i = user.getInvitePosterUrl().lastIndexOf("/");
            String invitePosterName = user.getInvitePosterUrl().substring(i + 1).replace(".jpg", "");
            if (StringUtils.equals(md5Name, invitePosterName)) {
                return user.getInvitePosterUrl();
            }
        }

        // 获取邀请用户获得积分值
        DistributionConfigVO distConfig = buyerDistributionExpertService.getDistributionConfig();
        if (Objects.isNull(distConfig) || NumberUtil.isEmpty(distConfig.getPoints4Invite())) {
            log.error("未配置达人分销时邀请新用户所得的积分值");
            throw new BusinessException("邀请海报生成失败");
        }
        try {
            ResponseEntity<byte[]> responseEntity = integrationUtilService.getQrCodeUnLimit(vo);
            byte[] miniFile = responseEntity.getBody();

            String backGroundUrl = BuyerConstant.DISTRIBUTION_EXPERT_INVITE_POSTER_BACKGROUND_PATH;
            InputStream inputStream = this.combinePoster(loginBuyer, miniFile, backGroundUrl, String.valueOf(distConfig.getPoints4Invite()));
            String fileName = fileConstant.generateUserObjectNameV2(loginBuyer.getId(), md5Name, true) + ".jpg";

            MultipartFile file = MultipartFileConverter.convert(inputStream, fileName);
            FileParam fileParam = new FileParam(PolicyGenerateUtil.getPolicyByBuyerId(loginBuyer.getId()), fileName);
            String invitePosterUrl = integrationUtilService.fileSave(file, FwkJsonUtil.toJsonString(fileParam)).getData();
            // String qrCodeUrl = FwkFileManageUtil.getFileService().save(loginBuyer.getObsFileAuth(), inputStream, fileName );
            log.info("达人：{}，合成邀请图片，{}", loginBuyer.getNickName(), invitePosterUrl);

            if (StringUtils.isNotEmpty(user.getInvitePosterUrl())) {
                integrationUtilService.fileDelete(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), user.getInvitePosterUrl()));
                // FwkFileManageUtil.getFileService().delete(loginBuyer.getObsFileAuth(),user.getInvitePosterUrl());
            }
            user.setInvitePosterUrl(invitePosterUrl);
            //更新邀请记录
            buyerService.update(user);
        } catch (Exception e) {
            log.error("融合图片失败,参数：{},用户：{}", loginBuyer.getNickName(), expertShareInfoVo, e);
            throw new BusinessException(Exception4BuyerEnum.THE_SYSTEM_ERROR);
        }
        return user.getInvitePosterUrl();
    }

    /**
     * 合并海报
     *
     * @param loginBuyer    登录人信息
     * @param miniFile      二维码文件信息
     * @param backGroundUrl 背景图片URL
     * @param pointTips     积分动态文本
     * @return 合成的文件输入流
     * @throws Exception
     */
    private InputStream combinePoster(LoginBuyerBo loginBuyer, byte[] miniFile, String backGroundUrl, String pointTips) throws Exception {
        // 查看系统支持字体
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        Font[] availableFonts = ge.getAllFonts();
        for (Font availableFont : availableFonts) {
            log.info("系统支持的字体:" + availableFont.getFontName() + "-------------" + availableFont.getName());
        }

        String avatarUrl = loginBuyer.getAvatarUrl();

        //合成器（指定背景图和输出格式，整个图片的宽高和相关计算依赖于背景图，所以背景图的大小是个基准）
        byte[] bytes = integrationUtilService.fileDownloada(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), backGroundUrl));
        // ResponseEntity<InputStreamResource> backgroundUrlDownload = FwkFileManageUtil.getFileService().download(obsFileAuth,backGroundUrl);
        InputStream backgroundUrlUrlInputStream = new ByteArrayInputStream(bytes);
        BufferedImage backgroundUrlUrlBufferedImage = ImageIO.read(backgroundUrlUrlInputStream);
        ImageCombiner combiner = new ImageCombiner(backgroundUrlUrlBufferedImage, OutputFormat.JPG);

        //头像（圆角设置一定的大小，可以把头像变成圆的）
        byte[] headBytes = integrationUtilService.fileDownloada(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), avatarUrl));
        // ResponseEntity<InputStreamResource> avatarUrlDownload = FwkFileManageUtil.getFileService().download(obsFileAuth,avatarUrl);
        InputStream avatarUrlInputStream = new ByteArrayInputStream(headBytes);
        BufferedImage avatarUrlBufferedImage = ImageIO.read(avatarUrlInputStream);
        combiner.addImageElement(avatarUrlBufferedImage, 40, 30, 90, 90, ZoomMode.WidthHeight).setRoundCorner(90);

        String nickName = loginBuyer.getNickName();
        String workText = nickName;
        if (nickName.length() > 3) {
            workText = nickName.substring(0, 3) + "...";
        }
        //加文本元素（昵称信息）
        combiner.addTextElement(workText + "邀请你成为分销达人", "阿里巴巴普惠体-R", Font.BOLD, 32, 150, 58).setColor(Color.WHITE);
        //加文本元素（积分信息）
        combiner.addTextElement(pointTips, "阿里巴巴普惠体-R", Font.BOLD, 38, 375, 770).setColor(new Color(254, 101, 58));

        //二维码（强制按指定宽度、高度缩放）
        InputStream in = new ByteArrayInputStream(miniFile);
        combiner.addImageElement(ImageIO.read(in), 540, 1509, 164, 164, ZoomMode.WidthHeight);

        //执行图片合并
        combiner.combine();

        //可以获取流（并上传oss等）
        return combiner.getCombinedImageStream();
    }

    public LoginBuyerBo getLoginBuyerBo() {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        log.info("达人loginBuyer:{}", loginBuyer.getId());
        return loginBuyer;
    }


    @Override
    public ExpertPartnerPageListDto getExpertPartner(ExpertWithPartnerRequest expertWithPartnerRequest) {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (ObjectUtils.isEmpty(expert)) {
            throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT_NOT);
        }
        return buyerDistributionExpertQueryDslRepository.listByPid(expert.getId(), expertWithPartnerRequest);
    }

    @Override
    public ExpertPartnerSummarizeDto getExpertPartnerSummarize() {
        LoginBuyerBo loginBuyer = getLoginBuyerBo();
        BuyerDistributionExpert expert = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(loginBuyer.getId());
        if (ObjectUtils.isEmpty(expert)) {
            throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT_NOT);
        }
        ExpertPartnerSummarizeDto expertPartnerSummarize = buyerDistributionExpertQueryDslRepository.getExpertPartnerSummarize(expert.getId());
        expertPartnerSummarize.setBuyerId(expert.getBuyerId());
        expertPartnerSummarize.setDistributionId(expert.getId());
        return expertPartnerSummarize;
    }

    @Override
    public void updateAmounts(String userId, String orderNo, Long amount) {
        log.info("更新分销达人的分销总消费金额参数信息：userId={}, orderNo={}, amount={}", userId, orderNo, amount);
        int rows = buyerDistributionExpertRepository.updateAmounts(userId, amount);
        log.info("更新分销达人的分销总消费金额结果：rows={}, userId={}, orderNo={}, amount={}", rows, userId, orderNo, amount);
    }

    @Override
    public DistributionConfigVO getDistributionConfig() {
        // 查询邀新获得积分配置
        GeneralConfigVo param = new GeneralConfigVo();
        param.setGroupKey("DRPOINTCONFIG");
        param.setGroupChildKey("DRPOINTCONFIG_KHJF");
        param.setConfigKey("DRPOINTCONFIG_KHJF");
        FwkApiResponse<List<GeneralConfigInnerDto>> response = everyoneGeneralConfigFeign.findInnerListByKey(param);
        log.info("调用sale-api获取邀新获得积分配置：response={}, param={}", JSON.toJSONString(response), param);
        if (ObjectUtils.isEmpty(response) || ObjectUtils.isEmpty(response.getData())) {
            return null;
        }
        GeneralConfigInnerDto dto = response.getData().stream().findFirst().orElse(null);
        if (Objects.isNull(dto)) {
            return null;
        }
        return DistributionConfigVO.builder().points4Invite(Integer.parseInt(dto.getConfigValue())).build();
    }

    @Override
    public List<DistStatisticsVO> statistics(Set<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        return buyerDistributionExpertRepository.countBuyersByShareIds(userIds);
    }

    @Override
    public DistributionInfoVO achievements(String userId, BuyerDistributionExpert distribution) {
        if (Objects.isNull(distribution)){
            distribution = buyerDistributionExpertRepository.findFirstByBuyerIdAndDeletedIsFalse(userId);
        }
        if (ObjectUtils.isEmpty(distribution)) {
            log.info("用户不是分销达人：userId={}", userId);
            throw new BusinessException(Exception4BuyerEnum.HAS_EXPERT_NOT);
        }

        // 赋值分销成果信息
        BuyerStatisticsDTO buyerStatisticsDTO = buyerService.statisticsUser(userId);
        Map<String, Integer> userPointMap = this.getUserPoint(Sets.newHashSet(userId), Sets.newHashSet(TypeCode4BuyerPointEnum.INVITENEWUSERS.getCode()));

        DistributionInfoVO distVO = DistributionInfoVO.builder()
                .amounts(distribution.getAmounts())
                .inviteUsers(Objects.nonNull(buyerStatisticsDTO) ? buyerStatisticsDTO.getInviteUsers() : 0)
                .invitePoints(userPointMap.get(userId))
                .build();

        UserTradeStatisticsVO orderPoint = this.getOrderPoint(userId);
        if (Objects.nonNull(orderPoint)) {
            distVO.setOrderPoints(orderPoint.getPoints());
            distVO.setOrderIntransitPoints(orderPoint.getIntransitPoints());
            distVO.setOrderAvailablePoints(orderPoint.getAvailablePoints());
        }
        distVO.setPoints(NumberUtil.getNull4Default(distVO.getOrderPoints(), 0) + NumberUtil.getNull4Default(distVO.getInvitePoints(), 0));
        return distVO;
    }

    /**
     * 获取用户积分
     *
     * @param userIds   用户ID列表
     * @param typeCodes 积分来源列表 {@link TypeCode4BuyerPointEnum}
     * @return 用户ID对应积分信息
     */
    private Map<String, Integer> getUserPoint(Set<String> userIds, Set<String> typeCodes) {
        StatisticsPointParam param = StatisticsPointParam.builder().userIds(userIds).typeCodes(typeCodes).build();
        FwkApiResponse<List<BuyerStatisticsVO>> response = buyerPointFeign.statisticsPoint(param);
        if (Objects.nonNull(response) && Objects.equals(response.getHttpStatus(), 200) && Objects.nonNull(response.getData())) {
            return response.getData().stream()
                    .collect(Collectors.toMap(BuyerStatisticsVO::getUserId, BuyerStatisticsVO::getPoints, (v1, v2) -> v1));
        }
        return Maps.newHashMap();
    }

    /**
     * 获取用户订单积分
     *
     * @param userId
     * @return
     */
    private UserTradeStatisticsVO getOrderPoint(String userId) {
        FwkApiResponse<UserTradeStatisticsVO> response = orderPointFeign.tradeStatistics(TradeStatisticsParam.builder().userId(userId).build());
        if (Objects.nonNull(response) && Objects.equals(response.getHttpStatus(), 200) && Objects.nonNull(response.getData())) {
            return response.getData();
        }
        return null;
    }

    /**
     * 开通分销达人的支付账号（如通联注册会员并实名）
     *
     * @param userId   用户ID
     * @param realName 用户姓名
     * @param idCard   身份证号码
     * @return 返还信息
     */
    private UnionPayMemberRegistAndSetRealNameDto activePayAccount(String userId, String realName, String idCard) {
        UnionPayMemberRegistAndSetRealApplyNameVo reqVo = new UnionPayMemberRegistAndSetRealApplyNameVo();
        reqVo.setBizUserId(userId);
        reqVo.setName(realName);
        reqVo.setIdentityNo(idCard);
        try {
            UnionPayMemberRegistAndSetRealNameDto dto = integrationPayService.memberRegistAndSetRealName(reqVo).getData();
            if (Objects.isNull(dto) || BooleanUtil.isFalse(Objects.equals(dto.getSubCode(), UnionPayShopConstant.OK_STATUS_SUB))) {
                log.error("达人创建会员且实名认证失败：{}", JSON.toJSONString(dto));
                throw new BusinessException(Exception4BuyerEnum.MEMBER_REGIST_REALNAME);
            }
            return dto;
        } catch (Exception e) {
            log.error("达人创建会员且实名认证异常: ", e);
        }
        return null;
    }

    /**
     * 查询余额信息
     *
     * @param userId 用户ID
     * @return 余额信息
     */
    private UnionPayQueryBalanceVo getBalanceInfo(String userId) {
        UnionPayQueryBalanceDto queryBalanceDto = new UnionPayQueryBalanceDto();
        queryBalanceDto.setBizUserId(userId);
        // 查询体现余额信息
        try {
            FwkApiResponse<UnionPayQueryBalanceVo> response = integrationPayService.queryBalance(queryBalanceDto);
            return Objects.nonNull(response) ? response.getData() : null;
        } catch (Exception e) {
            log.error("查询余额信息异常：userId={}", userId, e);
        }
        return null;
    }
}