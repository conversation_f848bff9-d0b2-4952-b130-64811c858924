/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.controller;

import com.bamboocloud.cdp.boot.security.annotation.Inner;
import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.buyer.shop.service.BuyerShopService;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopCategorySimpleDto;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopRecommendedPageDto;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopSmartRecommendedDto;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopSmartRecommendedListDto;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.BuyerShopByLongitudeAndLatitudeSearchVo;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.BuyerShopDistributionExpertInvitePosterVo;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.BuyerShopListByLongitudeAndLatitudeSearchVo;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.BuyerShopRecommendedSearchVo;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.*;
import com.bamboocloud.cdp.user.sdk.domain.vo.buyer.shop.BuyerShopIdsRequestVo;
import com.bamboocloud.cdp.user.sdk.domain.vo.buyer.shop.BuyerShopRequestDto;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Slf4j
@RestController
public class BuyerShopController extends BaseBuyerController {

    @Autowired
    private BuyerShopService buyerShopService;

    /**
     * 根据id获取店铺简单信息
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_SIMPLE_BY_ID_V1)
    public FwkApiResponse<BuyerShopDto> getSimpleById(@PathVariable String id) {
        log.debug("BuyerShopController - getSimpleById");
        return FwkApiResponse.success(buyerShopService.getSimpleById(id));
    }

    /**
     * 根据id获取店铺部分信息（目前用于market-api的查询我的线路api）
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_PART_INFO_BY_ID_V1)
    public FwkApiResponse<BuyerShopPartInfoDto> getPartInfoById(@PathVariable String id) {
        log.debug("BuyerShopController - getPartInfoById");
        return FwkApiResponse.success(buyerShopService.getPartInfoById(id));
    }

    /**
     * 根据id集合 获取店铺部分信息（目前用于market-api的查询我的线路api）
     *
     * @param dto
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_GET_PART_INFO_V1)
    public FwkApiResponse<List<BuyerShopPartInfoDto>> getPartInfoByIds(@RequestBody @Valid BuyerShopRequestDto dto) {
        log.debug("BuyerShopController - getPartInfoByIds");
        return FwkApiResponse.success(buyerShopService.getPartInfo(dto));
    }

    /**
     * 根据id来获取精品优选的店铺信息
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_PREMIUM_GET_BY_ID_V1)
    public FwkApiResponse<BuyerShopPremiumDto> getPremiumShopById(@PathVariable String id) {
        log.debug("BuyerShopController - getPremiumShopById");
        return FwkApiResponse.success(buyerShopService.getPremiumShopById(id));
    }

    /**
     * 查询shopId
     *
     * @param buyerShopIdsRequestVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_LIST_SHOP_IDS_V1)
    public FwkApiResponse<BuyerShopIdsDto> listIds(@RequestBody BuyerShopIdsRequestVo buyerShopIdsRequestVo) {
        log.debug("BuyerShopController - listIds");
        return FwkApiResponse.success(buyerShopService.listIds(buyerShopIdsRequestVo));
    }

    /**
     * 分页查询大数据推荐店铺
     *
     * @param buyerShopRecommendedSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_RECOMMEND_LIST_V1)
    public FwkApiResponse<BuyerShopRecommendedPageDto> searchRecommendShopList(@RequestBody BuyerShopRecommendedSearchVo buyerShopRecommendedSearchVo) {
        log.debug("BuyerShopController - searchRecommendShopList");
        return FwkApiResponse.success(buyerShopService.searchRecommendShop(buyerShopRecommendedSearchVo));
    }

    /**
     * 根据用户的当前定位来查询周边的店铺
     *
     * @param buyerShopByLongitudeAndLatitudeSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_BY_LONGITUDE_AND_LATITUDE_LIST_V1)
    public FwkApiResponse<List<BuyerShopSmartRecommendedDto>> searchByLongitudeAndLatitudeShopList(@RequestBody BuyerShopByLongitudeAndLatitudeSearchVo buyerShopByLongitudeAndLatitudeSearchVo) {
        log.debug("BuyerShopController - searchByLongitudeAndLatitudeShopList");
        return FwkApiResponse.success(buyerShopService.searchShopByLongitudeAndLatitude(buyerShopByLongitudeAndLatitudeSearchVo));
    }

    /**
     * 根据用户的当前定位来查询店铺列表，根据距离排序(互动页查询店铺，用于切换成列表）
     *
     * @param buyerShopListByLongitudeAndLatitudeSearchVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_BY_LONGITUDE_AND_LATITUDE_SEARCH_V1)
    public FwkApiResponse< List<BuyerShopSmartRecommendedListDto>> searchShopList(@RequestBody BuyerShopListByLongitudeAndLatitudeSearchVo buyerShopListByLongitudeAndLatitudeSearchVo) {
        log.debug("BuyerShopController - searchShopList");
        return FwkApiResponse.success(buyerShopService.searchShopList(buyerShopListByLongitudeAndLatitudeSearchVo));
    }

    /**
     * 根据id获取店铺信息
     *
     * @param shopId
     * @return
     */
    @Inner
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_ID_V1)
    public FwkApiResponse<BuyerShopDto> getShopByShopId(@PathVariable("shopId") String shopId) {
        log.debug("BuyerShopController - getShopByShopId");
        return FwkApiResponse.success(buyerShopService.getShopByShopId(shopId));
    }

    /**
     * 根据id获取organizationId
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_ORGANIZATION_ID_BY_ID_V1)
    public FwkApiResponse<String> getOrganizationIdById(@PathVariable String id) {
        log.debug("BuyerShopController - getOrganizationIdById");
        return FwkApiResponse.success(buyerShopService.getOrganizationIdById(id));
    }


    /**
     * 根据状态集合获取id集合
     *
     * @param statusCodes
     * @param testOnly
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_LIST_IDS_BY_STATUS_CODES_AND_TEST_ONLY_V1)
    public FwkApiResponse<List<String>> listIdsByStatusCodesAndTestOnly(@RequestParam List<String> statusCodes,
                                                                        @RequestParam boolean testOnly
                                                                       ) {
        log.debug("BuyerShopController - getPartInfoById");
        return FwkApiResponse.success(buyerShopService.listIdsByStatusCodesAndTestOnly(statusCodes, testOnly));
    }


    /**
     * 获取测试店铺的id列表
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_TEST_SHOP_ID_GET_V1)
    public FwkApiResponse<List<String>> getTestShopIds() {
        log.debug("BuyerShopController - getTestShopIds");
        return FwkApiResponse.success(buyerShopService.getTestShopIds());
    }


    /**
     * 查询聚合支付商户Id
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_EXT_PAY_USER_ID_BY_ID_V1)
    public FwkApiResponse<String> getExtPayUserIdById(@PathVariable String id) {
        log.debug("BuyerShopController - getExtPayUserIdById");
        String extPayUserId = buyerShopService.getExtPayUserIdById(id);
        return FwkApiResponse.success(extPayUserId);
    }

    /**
     * 获取所有的店铺类型
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_TEST_SHOP_CATEGORY_GET_LIST_V1)
    public FwkApiResponse<List<BuyerShopCategorySimpleDto>> getShopCategoryList() {
        log.debug("BuyerShopController - getShopCategoryList");
        return FwkApiResponse.success(buyerShopService.getList());
    }

    /**
     * 获取指定装修店铺的店铺类型
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_TEST_SHOP_CATEGORY_OWNED_BY_SHOP_GET_LIST_V1)
    public FwkApiResponse<List<BuyerShopCategorySimpleDto>> getShopCategoryOwnedShopList(@RequestParam(required = false) Long pageDecorationSubId) {
        log.debug("BuyerShopController - getShopCategoryOwnedShopList");
        return FwkApiResponse.success(buyerShopService.getOwnedByShopList(pageDecorationSubId));
    }
    /**
     * 根据id来获取主体路线选择的关联的店铺信息
     *
     * @param id
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_GET_SIMPLE_INFO_DES_BY_ID_V1)
    public FwkApiResponse<BuyerShopSimpleDesDto> getThemeRouteAreaShopById(@PathVariable String id) {
        log.debug("BuyerShopController - getThemeRouteAreaShopById");
        return FwkApiResponse.success(buyerShopService.getShopSimpleInfo(id));
    }


    /**
     * 查看店铺id集合根据店铺类型id
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_SHOP_IDS_BY_CATEGORY_ID_V1)
    public FwkApiResponse<List<String>> getShopIdsByCategoryId(@PathVariable Integer shopCategoryId) {
        log.debug("MamaShopController - getShopIdsByCategoryId");
        return FwkApiResponse.success(buyerShopService.getShopIdsByCategoryId(shopCategoryId));
    }




    /**
     * 达人分销-分享店铺
     *
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_SHOP_SHARE_POSTER)
    public FwkApiResponse<String> getShopSharePoster(@RequestBody @Validated BuyerShopDistributionExpertInvitePosterVo requestVo) {
        log.debug("BuyerShopBuyerController - getShopSharePoster");
        String image = buyerShopService.getShopSharePoster(requestVo);
        return FwkApiResponse.success(image);
    }

    @PostMapping(BuyerRouteConstant.FIND_SHOPLOGO_BY_IDS_V1)
    public FwkApiResponse<List<ShopLogoDto>> findShopLogoDtoByIds(@RequestBody List<String> shopIds) {
        log.debug("BuyerShopBuyerController - findShopLogoDtoByIds");
        List<ShopLogoDto> list = buyerShopService.findShopLogoDtoByIds(shopIds);
        return FwkApiResponse.success(list);
    }
}
