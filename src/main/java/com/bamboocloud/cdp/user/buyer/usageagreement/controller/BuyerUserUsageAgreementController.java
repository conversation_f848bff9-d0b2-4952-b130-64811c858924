/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerUserUsageAgreementController.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.buyer.usageagreement.controller;

import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.buyer.security.service.BuyerService;
import com.bamboocloud.cdp.user.buyer.usageagreement.service.BuyerUserUsageAgreementService;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserAgreementConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerUsageAgreement;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Slf4j
@RestController
public class BuyerUserUsageAgreementController extends BaseBuyerController {

    @Autowired
    private BuyerUserUsageAgreementService buyerUserUsageAgreementService;
    @Autowired
    private BuyerService buyerService;

    /**
     * 同意协议
     *
     * @param id
     * @return
     */
    @PutMapping(BuyerRouteConstant.BUYER_BUYER_USAGE_AGREEMENT_UPDATE_V1)
    public FwkApiResponse<String> update(@PathVariable Integer id) {
        log.debug("BuyerUserUsageAgreementController - update");
        BuyerUsageAgreement buyerUsageAgreement = buyerUserUsageAgreementService.get(id);
        if(!buyerUsageAgreement.isAccepted()){
            buyerUsageAgreement.setAccepted(true);
            buyerUserUsageAgreementService.update(buyerUsageAgreement);
            // 如果同意东道主协议，buyer需要打上东道主标签
            if(Objects.equals(buyerUsageAgreement.getUsageAgreementCode(), UserAgreementConstant.USAGE_AGREEMENT_CODE_LETTER_OF_COMMITMENT_FROM_HOST_MERCHANT_BUYER.getCode())){
                LoginBuyerBo loginBuyer = getLoginBuyer();
                Buyer buyer = buyerService.getBuyer(loginBuyer.getId());
                if (!ObjectUtils.isEmpty(buyer)){
                    buyer.setDaYunHost(true);
                    buyerService.update(buyer);
                }
            }
        }
        return FwkApiResponse.success();
    }
}
