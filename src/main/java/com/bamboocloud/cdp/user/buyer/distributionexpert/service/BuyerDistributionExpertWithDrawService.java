/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAuthService.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.service;

import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.ExpertWithDrawListVo;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertWithDraw;

import java.math.BigDecimal;

public interface BuyerDistributionExpertWithDrawService {
    BuyerDistributionExpertWithDraw create(BuyerDistributionExpertWithDraw draw);
    BuyerDistributionExpertWithDraw update(BuyerDistributionExpertWithDraw draw);
    BuyerDistributionExpertWithDraw getByExtBizOrderNo(String extBizOrderNo);
    ExpertWithDrawPageDto withDrawList(String buyerId,ExpertWithDrawListVo param);
    //累计提现金额
    BigDecimal totalExtPayAmount(String buyerId);
}
