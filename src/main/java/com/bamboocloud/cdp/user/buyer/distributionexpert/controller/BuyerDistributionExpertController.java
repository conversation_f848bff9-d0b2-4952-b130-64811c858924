/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.controller;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.common.sdk.domain.Result;
import com.bamboocloud.cdp.common.sdk.util.StringUtil;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPayBindPhoneDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPaySendVerificationCodeDto;
import com.bamboocloud.cdp.pay.sdk.common.dto.unionpay.shop.UnionPaySignAcctProtocolDto;
import com.bamboocloud.cdp.pay.sdk.common.vo.unionpay.base.UnionPayNotificationBaseVo;
import com.bamboocloud.cdp.pay.sdk.integration.IntegrationPayService;
import com.bamboocloud.cdp.user.buyer.distributionexpert.service.BuyerDistributionExpertService;
import com.bamboocloud.cdp.user.buyer.security.service.BuyerService;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.common.params.PageParam;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.*;
import com.bamboocloud.cdp.user.domain.vo.DistributionConfigVO;
import com.bamboocloud.cdp.user.domain.vo.DistributionUserVO;
import com.bamboocloud.cdp.user.domain.vo.PageVO;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.user.sdk.constant.UriConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.param.DistStatisticsParam;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistStatisticsVO;
import com.bamboocloud.cdp.user.sdk.domain.vo.DistributionInfoVO;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> @description:分销达人
 */
@Slf4j
@RestController
public class BuyerDistributionExpertController extends BaseBuyerController {

    @Autowired
    private BuyerDistributionExpertService buyerDistributionExpertService;
    @Autowired
    private IntegrationPayService integrationPayService;
    @Autowired
    private BuyerService buyerService;

    /**
     * 创建达人
     *
     * @param buyerDistributionExpertCreationVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_CREATE_V1)
    public FwkApiResponse<BuyerDistributionExpertDto> create(@Validated @RequestBody BuyerDistributionExpertCreationVo buyerDistributionExpertCreationVo) {
        log.info("BuyerDistributionExpertController-create入参:{}", FwkJsonUtil.toJSONString(buyerDistributionExpertCreationVo));
        BuyerDistributionExpertDto dto = buyerDistributionExpertService.createExpert(buyerDistributionExpertCreationVo);
        return FwkApiResponse.success(dto);
    }

    /**
     * 获取达人信息
     */
    @GetMapping("/v1/distributionExpertInfo")
    public FwkApiResponse<BuyerDistributionExpertDto> getBuyerDistributionExpertInfo() {
        log.info("BuyerDistributionExpertController - getBuyerDistributionExpertInfo");
        BuyerDistributionExpertDto dto = buyerDistributionExpertService.getBuyerDistributionExpertInfo();
        return FwkApiResponse.success(dto);
    }

    /**
     * 达人在通联绑定手机发送验证码
     *
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_BINDPHONE_SENDCODE)
    public FwkApiResponse<UnionPaySendVerificationCodeDto> bindPhoneSendCode(@Validated @RequestBody ExpertBindPhoneUnionSendCodeVo param) {
        log.info("BuyerDistributionExpertController-bindPhoneSendCode入参:{}", FwkJsonUtil.toJSONString(param));
        return FwkApiResponse.success(buyerDistributionExpertService.bindPhoneSendCode(param));
    }

    /**
     * 达人在通联绑定手机
     *
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_BINDPHONE)
    public FwkApiResponse<UnionPayBindPhoneDto> bindPhone(@Validated @RequestBody ExpertBindPhoneUnionApplyPhoneVo param) {
        log.info("BuyerDistributionExpertController -- bindPhone入参:{}", FwkJsonUtil.toJSONString(param));
        return FwkApiResponse.success(buyerDistributionExpertService.bindPhone(param));
    }

    /**
     * 达人通联支付绑卡-对私
     *
     * @throws Exception
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_BINDBANK)
    public FwkApiResponse<String> bindBankToPrivate(@Validated @RequestBody ExpertBindBankCreationVo param) {
        log.debug("BuyerDistributionExpertController - bindBankToPrivate入参:{}", FwkJsonUtil.toJSONString(param));
        buyerDistributionExpertService.bindBankToPrivate(param);
        return FwkApiResponse.success(null);
    }

    /**
     * 达人绑卡-支持的卡列表
     *
     * @throws Exception
     */
    @GetMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_SUPPORT_BANKLIST)
    public FwkApiResponse<List<SupportBankDto>> supportBankList() {
        log.debug("BuyerDistributionExpertController - supportBankList");
        return FwkApiResponse.success(buyerDistributionExpertService.supportBankList());
    }

    /**
     * 达人提现签约
     *
     * @throws Exception
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_SIGNACCTPROTOCOL)
    public FwkApiResponse<UnionPaySignAcctProtocolDto> signAcctProtocol(@Validated @RequestBody ExpertSignAcctProtocolVo param) {
        log.debug("BuyerDistributionExpertController - signAcctProtocol入参:{}", FwkJsonUtil.toJSONString(param));
        return FwkApiResponse.success(buyerDistributionExpertService.signAcctProtocol(param));
    }

    /**
     * 通联支付：达人提现签约回调接口
     * 账户提现协议签约结果异步通知
     *
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_RECEIVE_SIGN_RESULT_V1)
    public String receiveUnionPayExpertSignResult(UnionPayNotificationBaseVo unionPayNotificationBaseVo) {
        log.debug("BuyerDistributionExpertController - receiveUnionPayExpertSignResult");
        log.info("达人提现协议签约结果异步通知:{}", unionPayNotificationBaseVo);
        // 云商通-通联支付报文解析验证
        FwkApiResponse<String> response = integrationPayService.receiveNotificationParam(JSONObject.toJSONString(unionPayNotificationBaseVo));
        if (FwkStringUtil.isBlank(response.getData())) {
            return "fail";
        }
        ExpertSignResultVo expertSignResultVo = FwkJsonUtil.toObject(response.getData(), ExpertSignResultVo.class);
        return buyerDistributionExpertService.receiveUnionPaySignResult(expertSignResultVo);
    }

    /**
     * 获取达人信息
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_LIST_BY_ID_V1)
    public FwkApiResponse<List<BuyerDistributionExpertDto>> listBuyerDistributionExpertInfoByIds(@RequestBody List<String> expertIds) {
        log.info("BuyerDistributionExpertController - listBuyerDistributionExpertInfoById:{}", expertIds);
        List<BuyerDistributionExpertDto> list = buyerDistributionExpertService.listBuyerDistributionExpertInfoByIds(expertIds);
        return FwkApiResponse.success(list);
    }

    /**
     * 达人提现
     *
     * @throws Exception
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_WITHDRAW)
    public FwkApiResponse<BuyerDistributionExpertWithDrawDto> withDraw(@Validated @RequestBody ExpertWithDrawVo param) {
        log.debug("BuyerDistributionExpertController - withDraw入参:{}", FwkJsonUtil.toJSONString(param));
        return FwkApiResponse.success(buyerDistributionExpertService.withDraw(param));
    }

    /**
     * 达人提现-通联支付提现异步通知
     *
     * @param unionPayNotificationBaseVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_WITHDRAW_RESULT_V1)
    public String withDrawNotification(UnionPayNotificationBaseVo unionPayNotificationBaseVo) {
        log.info("BuyerDistributionExpertController - withDrawNotification达人提现异步通知入参:{}", FwkJsonUtil.toJSONString(unionPayNotificationBaseVo));
        buyerDistributionExpertService.withDrawNotification(unionPayNotificationBaseVo);
        return "success";
    }

    /**
     * 达人提现记录
     *
     * @throws Exception
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_WITHDRAW_LIST_V1)
    public FwkApiResponse<ExpertWithDrawPageDto> withDrawList(@Validated @RequestBody ExpertWithDrawListVo param) {
        log.debug("BuyerDistributionExpertController - withDrawList入参:{}", FwkJsonUtil.toJSONString(param));
        return FwkApiResponse.success(buyerDistributionExpertService.withDrawList(param));
    }

    /**
     * 查询达人收益及可提现余额信息
     */
    @GetMapping("/v1/distributionExpert/fundBalance")
    public FwkApiResponse<ExpertFundBalanceDto> getFundBalance() {
        ExpertFundBalanceDto fundBalance = buyerDistributionExpertService.getFundBalance();
        return FwkApiResponse.success(fundBalance);
    }

    /**
     * 获取达人邀请海报
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_INVITE_POSTER)
    public FwkApiResponse<String> getExpertInvitePoster(@Validated @RequestBody(required = false) ExpertShareInfoVo expertShareInfoVo) {
        String expertInvitePoster = buyerDistributionExpertService.getExpertInvitePoster(expertShareInfoVo);
        return FwkApiResponse.success(expertInvitePoster);
    }

    /**
     * 获取达人好友信息
     */
    @PostMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_PARTNER)
    public FwkApiResponse<ExpertPartnerPageListDto> getExpertPartner(@RequestBody @Validated ExpertWithPartnerRequest expertWithPartnerRequest) {
        log.debug("BuyerDistributionExpertController - getExpertPartner");
        ExpertPartnerPageListDto expertPartner = buyerDistributionExpertService.getExpertPartner(expertWithPartnerRequest);
        return FwkApiResponse.success(expertPartner);
    }


    /**
     * 获取达人好友汇总信息
     */
    @GetMapping(BuyerRouteConstant.BUYER_DISTRIBUTION_EXPERT_PARTNER_SUMMARIZE)
    public FwkApiResponse<ExpertPartnerSummarizeDto> getExpertPartnerSummarize() {
        log.debug("BuyerDistributionExpertController - getExpertPartnerSummarize");
        ExpertPartnerSummarizeDto expertPartner = buyerDistributionExpertService.getExpertPartnerSummarize();
        return FwkApiResponse.success(expertPartner);
    }

    /**
     * 获取分销用户列表（C端-个人分销模块）
     */
    @PostMapping("/distribution/user/list")
    public FwkApiResponse<PageVO<DistributionUserVO>> distributionUsers(@Validated @RequestBody PageParam param) {
        String userId = super.getUserId();
        PageVO<DistributionUserVO> page = buyerService.page(param, userId);
        return FwkApiResponse.success(page);
    }

    /**
     * 获取分销配置信息
     */
    @GetMapping("/distribution/config")
    public FwkApiResponse<DistributionConfigVO> distributionConfig() {
        return FwkApiResponse.success(buyerDistributionExpertService.getDistributionConfig());
    }

    /**
     * 分销数据（邀新）统计
     */
    @PostMapping(UriConstant.DISTRIBUTION_STATISTICS)
    public Result<List<DistStatisticsVO>> statistics(@Valid @RequestBody DistStatisticsParam param) {
        return Result.success(buyerDistributionExpertService.statistics(param.getUserIds()));
    }

    /**
     * 分销员分销成果信息
     */
    @GetMapping(UriConstant.DISTRIBUTION_ACHIEVEMENTS)
    public Result<DistributionInfoVO> achievements(@PathVariable String userId) {
        if (StringUtil.isBlank(userId)) {
            return Result.success();
        }
        return Result.success(buyerDistributionExpertService.achievements(userId, null));
    }
}
