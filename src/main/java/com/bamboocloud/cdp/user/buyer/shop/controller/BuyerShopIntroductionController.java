/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaShopController.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.config.annotation.Loggable;
import com.bamboocloud.cdp.user.mama.shop.service.MamaShopIntroductionService;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.shop.BuyerShopReviewScoreDto;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class BuyerShopIntroductionController extends BaseMamaController {

    @Autowired
    private MamaShopIntroductionService mamaShopIntroductionService;


    /**
     * 设置店铺评分
     */
    @Loggable
    @PostMapping(BuyerRouteConstant.BUYER_SHOP_INTRODUCTION_SET_REVIEW_SCORE_V1)
    public FwkApiResponse<String> setShopIntroductionReviewScore(@RequestBody @Valid BuyerShopReviewScoreDto buyerShopReviewScoreDto) {
        mamaShopIntroductionService.setShopIntroductionReviewScore(buyerShopReviewScoreDto);
        return FwkApiResponse.success();
    }


}
