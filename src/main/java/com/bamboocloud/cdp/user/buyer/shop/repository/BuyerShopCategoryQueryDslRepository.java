/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorShopDutyQueryDslRepository.java
 * @createdDate: 2022/08/23 15:07:23
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopCategoryDto;
import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopCategorySimpleDto;
import com.bamboocloud.cdp.user.sdk.constant.OrganizationConstant;
import com.bamboocloud.cdp.user.sdk.constant.ShopConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShop;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.QShopCategory;
import com.bamboocloud.cdp.user.sdk.enums.ShopStatusCodeEnum;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class BuyerShopCategoryQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public List<BuyerShopCategorySimpleDto> getList() {
        BooleanBuilder builder = new BooleanBuilder();
        QShopCategory shopCategory = QShopCategory.shopCategory;
        builder.and(shopCategory.deleted.isFalse());
        return queryFactory.select(Projections.constructor(BuyerShopCategorySimpleDto.class,
                shopCategory.id,
                shopCategory.name)).from(shopCategory).where(builder).fetch();
    }

    public List<BuyerShopCategorySimpleDto> getOwnedByShopList(BuyerShopCategoryDto dto) {

        BooleanBuilder builder = new BooleanBuilder();
        QShopCategory shopCategory = QShopCategory.shopCategory;
        QShop shop = QShop.shop;
        builder.and(shopCategory.deleted.isFalse());
        builder.and(shop.deleted.isFalse());
        builder.and(shop.statusCode.eq(ShopStatusCodeEnum.OPEN.getCode()));
        builder.and(shop.extPayUserStatusCode.eq(ShopConstant.SHOP_EXT_PAY_USER_STATUS_FINISH.getCode())
            .or(shop.wxPayApplymentStatusCode.eq(ShopConstant.SHOP_WX_PAY_APPLYMENT_STATUS_FINISH.getCode())));
        builder.and(shop.organization.reviewStatusCode.eq(OrganizationConstant.ORGANIZATION_REVIEW_STATUS_APPROVED.getCode()));

        if (CollectionUtils.isNotEmpty(dto.getShopIds())){
            builder.and(shop.id.in(dto.getShopIds()));
        }else{
            builder.and(shop.mamaOwnedFixedPricePointOnly.isFalse());
        }
        if (CollectionUtils.isNotEmpty(dto.getNeShopIds())){
            builder.and(shop.id.notIn(dto.getNeShopIds()));
        }
        return queryFactory.select(Projections.constructor(BuyerShopCategorySimpleDto.class,
                shopCategory.id,
                shopCategory.name))
            .from(shop)
            .leftJoin(shopCategory).on(shop.shopCategoryId.eq(shopCategory.id))
            .where(builder)
            .orderBy(shopCategory.id.desc())
            .distinct()
            .fetch();

    }
}
