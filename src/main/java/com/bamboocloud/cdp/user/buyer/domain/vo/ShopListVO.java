package com.bamboocloud.cdp.user.buyer.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/25  11:08
 */
@Data
public class ShopListVO {

    /**
     * 店铺ID
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 距离（单位:米）
     */
    private Long distance;

    /**
     * 门头图
     */
    private String headImg;

    /**
     * 酒店星级
     */
    private Integer star;

    /**
     * 最低价（单位：元）
     */
    private BigDecimal minPrice;

    /**
     * 店铺下商品总库存
     */
    private Long stock;
}
