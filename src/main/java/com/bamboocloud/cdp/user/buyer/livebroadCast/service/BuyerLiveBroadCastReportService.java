package com.bamboocloud.cdp.user.buyer.livebroadCast.service;

import com.bamboocloud.cdp.user.common.dto.buyer.livebroadcast.BuyerLiveBroadCastReportDetailDto;
import com.bamboocloud.cdp.user.common.dto.buyer.livebroadcast.BuyerLiveBroadCastReportPageListDto;
import com.bamboocloud.cdp.user.common.dto.buyer.livebroadcast.BuyerLiveBroadCastReportPageSimpleListDto;
import com.bamboocloud.cdp.user.common.vo.buyer.livebroadcast.BuyerLiveBroadCastReportQueryVo;
import com.bamboocloud.cdp.user.common.vo.buyer.livebroadcast.BuyerLiveBroadCastReportVo;

/**
 * <AUTHOR>
 * @Date 2024/12/19 19:17
 * @Version 1.0
 * @Describe
 */
public interface BuyerLiveBroadCastReportService {


    void create(BuyerLiveBroadCastReportVo buyerLiveBroadCastReportVo);

    BuyerLiveBroadCastReportPageListDto search(BuyerLiveBroadCastReportQueryVo buyerLiveBroadCastReportVo);

    BuyerLiveBroadCastReportPageSimpleListDto searchSimple(BuyerLiveBroadCastReportQueryVo buyerLiveBroadCastReportVo);

    BuyerLiveBroadCastReportDetailDto getDetail(Long id);
}
