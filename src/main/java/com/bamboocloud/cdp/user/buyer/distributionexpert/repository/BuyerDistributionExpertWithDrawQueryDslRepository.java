/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopQueryDslRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.ExpertWithDrawListVo;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyerDistributionExpertWithDraw;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Component
public class BuyerDistributionExpertWithDrawQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    public ExpertWithDrawPageDto withDrawList(String buyerId,ExpertWithDrawListVo expertWithDrawListVo) {
        QBuyerDistributionExpertWithDraw qWithDraw = QBuyerDistributionExpertWithDraw.buyerDistributionExpertWithDraw;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qWithDraw.buyerId.eq(buyerId));
        if (!ObjectUtils.isEmpty(expertWithDrawListVo.getExtPayStatusCode())) {
            builder.and(qWithDraw.extPayStatusCode.eq(expertWithDrawListVo.getExtPayStatusCode()));
        }
        BlazeJPAQuery<BuyerDistributionExpertWithDrawDto> jpaQuery = blazeJpaQueryFactory.select(Projections.constructor(BuyerDistributionExpertWithDrawDto.class,
                qWithDraw.id,
                qWithDraw.extPayAmount,
                qWithDraw.extPayStatusCode,
                qWithDraw.accountNumber,
                qWithDraw.bankName,
                qWithDraw.finishDate,
                qWithDraw.createdDate
            )).from(qWithDraw)
            .where(builder);
        ExpertWithDrawPageDto dto = new ExpertWithDrawPageDto();
        dto.setTotalCount(jpaQuery.fetchCount());
        if (!ObjectUtils.isEmpty(expertWithDrawListVo.getOffset()) &&
            !ObjectUtils.isEmpty(expertWithDrawListVo.getLimit())) {
            jpaQuery.limit(expertWithDrawListVo.getLimit())
                .offset(expertWithDrawListVo.getLimit() * expertWithDrawListVo.getOffset());
        }
        dto.setWithDrawDtos(jpaQuery.orderBy(qWithDraw.createdDate.desc()).fetch());
        return dto;
    }
}
