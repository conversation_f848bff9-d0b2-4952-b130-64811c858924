/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerUsageAgreementController.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.buyer.usageagreement.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.buyer.usageagreement.mapper.BuyerUsageAgreementMapper;
import com.bamboocloud.cdp.user.buyer.usageagreement.service.BuyerUsageAgreementService;
import com.bamboocloud.cdp.user.common.dto.buyer.usageagreement.BuyerUsageAgreementDto;
import com.bamboocloud.cdp.user.common.entity.mama.UsageAgreement;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class BuyerUsageAgreementController extends BaseMamaController {

    @Autowired
    private BuyerUsageAgreementService buyerUsageAgreementService;

    @Autowired
    private BuyerUsageAgreementMapper buyerUsageAgreementMapper;

    /**
     * 查看协议
     *
     * @param code code
     * @return
     */
    @GetMapping(BuyerRouteConstant.BUYER_USAGE_AGREEMENT_GET_BY_CODE_V1)
    public FwkApiResponse<BuyerUsageAgreementDto> getByCode(@RequestParam String code) {
        log.debug("BuyerUsageAgreementController - getByCode");
        UsageAgreement usageAgreement = buyerUsageAgreementService.getByCode(code);
        return FwkApiResponse.success(buyerUsageAgreementMapper.toDto(usageAgreement));
    }
}
