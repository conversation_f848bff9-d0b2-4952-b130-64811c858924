/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerUserUsageAgreementServiceImpl.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.buyer.usageagreement.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.user.buyer.usageagreement.mapper.BuyerUserUsageAgreementMapper;
import com.bamboocloud.cdp.user.buyer.usageagreement.repository.BuyerUsageAgreementRepository;
import com.bamboocloud.cdp.user.buyer.usageagreement.repository.BuyerUserUsageAgreementRepository;
import com.bamboocloud.cdp.user.common.dto.base.BaseUserUsageAgreementDto;
import com.bamboocloud.cdp.user.common.entity.mama.UsageAgreement;
import com.bamboocloud.cdp.user.sdk.constant.UserAgreementConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerUsageAgreement;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerUserUsageAgreementServiceImpl extends BaseBuyerService implements BuyerUserUsageAgreementService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private BuyerUserUsageAgreementRepository buyerUserUsageAgreementRepository;

    @Autowired
    private BuyerUsageAgreementRepository buyerUsageAgreementRepository;

    @Autowired
    private BuyerUserUsageAgreementMapper buyerUserUsageAgreementMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerUsageAgreement create(BuyerUsageAgreement buyerUsageAgreement) {
        buyerUsageAgreement = buyerUserUsageAgreementRepository.saveAndFlush(buyerUsageAgreement);
        entityManager.refresh(buyerUsageAgreement);
        return buyerUsageAgreement;
    }

    @Override
    public BuyerUsageAgreement get(Integer id) {
        return buyerUserUsageAgreementRepository.findById(id).orElseThrow();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerUsageAgreement update(BuyerUsageAgreement buyerUsageAgreement) {
        buyerUsageAgreement = buyerUserUsageAgreementRepository.saveAndFlush(buyerUsageAgreement);
        entityManager.refresh(buyerUsageAgreement);
        return buyerUsageAgreement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<BaseUserUsageAgreementDto> listByBuyerId(String buyerId) {
        List<BaseUserUsageAgreementDto> baseUserUsageAgreements = new ArrayList<>();
        String buyerInfoCode = UserAgreementConstant.USAGE_AGREEMENT_CODE_INFO_BUYER.getCode();
        String levelRuleCode = UserAgreementConstant.USAGE_AGREEMENT_CODE_LEVEL_RULE.getCode();
        String cancellationNoticeCode = UserAgreementConstant.USAGE_AGREEMENT_CODE_CANCELLATION_NOTICE.getCode();
        String codePointRuleCode = UserAgreementConstant.USAGE_AGREEMENT_CODE_POINT_RULE.getCode();
        String daYunHuiHostCode = UserAgreementConstant.USAGE_AGREEMENT_CODE_LETTER_OF_COMMITMENT_FROM_HOST_MERCHANT_BUYER.getCode();
        String platformService = UserAgreementConstant.USAGE_AGREEMENT_CODE_PLATFORM_SERVICE.getCode();
        baseUserUsageAgreements.add(getByBuyerId(buyerId, buyerInfoCode));
        baseUserUsageAgreements.add(getByBuyerId(buyerId, levelRuleCode));
        baseUserUsageAgreements.add(getByBuyerId(buyerId, cancellationNoticeCode));
        baseUserUsageAgreements.add(getByBuyerId(buyerId, codePointRuleCode));
        baseUserUsageAgreements.add(getByBuyerId(buyerId, daYunHuiHostCode));
        baseUserUsageAgreements.add(getByBuyerId(buyerId, platformService));
        return baseUserUsageAgreements;
    }

    @Override
    public List<UserAgreementConstant> listUsageAgreementCodeTypes() {
        List<UserAgreementConstant> buyerConstants = new ArrayList<>();
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_VENDOR_ORG);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_INFO);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_INFO_BUYER);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_POINT_RULE);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_LEVEL_RULE);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_CANCELLATION_NOTICE);
        buyerConstants.add(UserAgreementConstant.USAGE_AGREEMENT_CODE_LETTER_OF_COMMITMENT_FROM_HOST_MERCHANT_BUYER);
        return buyerConstants;
    }

    private BaseUserUsageAgreementDto getByBuyerId(String buyerId, String usageAgreementCode) {
        UsageAgreement usageAgreement =
                buyerUsageAgreementRepository.findFirstByCodeAndDraftIsFalseOrderByVersionDesc(usageAgreementCode);
        BuyerUsageAgreement buyerUsageAgreement =
                buyerUserUsageAgreementRepository.findFirstByBuyerIdAndUsageAgreementCodeOrderByUsageAgreementVersionDesc(buyerId
                        , usageAgreementCode);
        if (buyerUsageAgreement != null) {
            if (!usageAgreement.getVersion().equals(buyerUsageAgreement.getUsageAgreementVersion())) {
                buyerUsageAgreement = createBuyerUsageAgreement(buyerId, usageAgreementCode, usageAgreement);
            }
        } else {
            buyerUsageAgreement = createBuyerUsageAgreement(buyerId, usageAgreementCode, usageAgreement);
        }
        BaseUserUsageAgreementDto baseUserUsageAgreementDto =
                buyerUserUsageAgreementMapper.toDto(buyerUsageAgreement);
        baseUserUsageAgreementDto.setName(usageAgreement.getName());
        return baseUserUsageAgreementDto;
    }

    private BuyerUsageAgreement createBuyerUsageAgreement(String buyerId, String buyerInfoCode, UsageAgreement usageAgreement) {
        BuyerUsageAgreement buyerInfoUsageAgreement = new BuyerUsageAgreement();
        buyerInfoUsageAgreement.setBuyerId(buyerId);
        buyerInfoUsageAgreement.setUsageAgreementCode(buyerInfoCode);
        buyerInfoUsageAgreement.setUsageAgreementVersion(usageAgreement.getVersion());
        buyerInfoUsageAgreement = create(buyerInfoUsageAgreement);
        return buyerInfoUsageAgreement;
    }
}
