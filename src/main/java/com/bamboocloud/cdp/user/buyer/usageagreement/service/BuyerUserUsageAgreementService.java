/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerUserUsageAgreementService.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.buyer.usageagreement.service;

import com.bamboocloud.cdp.user.common.dto.base.BaseUserUsageAgreementDto;
import com.bamboocloud.cdp.user.sdk.constant.UserAgreementConstant;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerUsageAgreement;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerUserUsageAgreementService {
    /**
     * 新增
     *
     * @param buyerUsageAgreement
     * @return
     */
    BuyerUsageAgreement create(BuyerUsageAgreement buyerUsageAgreement);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    BuyerUsageAgreement get(Integer id);

    /**
     * 修改
     *
     * @param buyerUsageAgreement
     * @return
     */
    BuyerUsageAgreement update(BuyerUsageAgreement buyerUsageAgreement);

    /**
     * 查询
     *
     * @param buyerId 消费者用户Id
     * @return
     */
    List<BaseUserUsageAgreementDto> listByBuyerId(String buyerId);

    /**
     * 协议类型
     *
     * @return
     */
    List<UserAgreementConstant> listUsageAgreementCodeTypes();
}
