/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerMapper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.mapper;

import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertWithDraw;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Component
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuyerDistributionExpertWithDrawMapper {

    BuyerDistributionExpertWithDrawDto toDto(BuyerDistributionExpertWithDraw buyerDistributionExpertWithDraw);

}
