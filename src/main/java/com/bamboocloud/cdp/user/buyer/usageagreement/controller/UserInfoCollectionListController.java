package com.bamboocloud.cdp.user.buyer.usageagreement.controller;

import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.user.buyer.usageagreement.service.UserInfoCollectionListService;
import com.bamboocloud.cdp.user.common.dto.buyer.usageagreement.UserInfoCollectionListDto;
import com.bamboocloud.cdp.user.common.vo.base.BaseUserSendSmsVerificationCodeVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerCheckVerificationCodeVo;
import com.bamboocloud.cdp.user.sdk.constant.BuyerRouteConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
/**
 *个人信息收集清单
 */
public class UserInfoCollectionListController {

    @Autowired
    private UserInfoCollectionListService userInfoCollectionListService;

    /**
     * 查看个人信息收集清单是是否跳过手机号验证
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.GET_USER_INFO_COLLECTION_LIST_SKIP_V1)
    public FwkApiResponse<Boolean> getUserInfoCollectionListSkip() {
        log.info("个人信息收集清单-getUserInfoCollectionListSkip-1");
        Boolean isSkip = userInfoCollectionListService.getUserInfoCollectionListSkip();
        return FwkApiResponse.success(isSkip);
    }

    /**
     * 个人信息收集清单
     *
     * @return
     */
    @GetMapping(BuyerRouteConstant.GET_USER_INFO_COLLECTION_LIST_V1)
    public FwkApiResponse<UserInfoCollectionListDto> getUserInfoCollectionList() {
        log.info("个人信息收集清单-getUserInfoCollectionList-1");
        return FwkApiResponse.success(
            userInfoCollectionListService.getUserInfoCollectionList());
    }

    /**
     * 发送获取清单的验证码
     *
     * @param baseUserSendVerificationCodeDto
     * @return
     */
    @PostMapping(BuyerRouteConstant.GET_USER_INFO_COLLECTION_SEND_CODE_V1)
    public FwkApiResponse<String> sendUserInfoCollectionListMobileVerificationCode(
        @Validated @RequestBody BaseUserSendSmsVerificationCodeVo baseUserSendVerificationCodeDto) {
        userInfoCollectionListService.sendUserInfoCollectionListMobileVerificationCode(baseUserSendVerificationCodeDto);
        return FwkApiResponse.success();
    }

    /**
     * 校验收集清单验证码
     */
    @PostMapping(BuyerRouteConstant.GET_USER_INFO_COLLECTION_CHECK_CODE_V1)
    public FwkApiResponse<String> checkUserInfoListVerificationCode(
        @Validated @RequestBody BuyerCheckVerificationCodeVo buyerCheckVerificationCodeVo){
        userInfoCollectionListService.checkUserInfoListVerificationCode(buyerCheckVerificationCodeVo);
        return FwkApiResponse.success();
    }
}
