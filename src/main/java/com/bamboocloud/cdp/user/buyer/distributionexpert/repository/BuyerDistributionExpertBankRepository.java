/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaVendorConfigRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertBank;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface BuyerDistributionExpertBankRepository extends FwkBaseRepository<BuyerDistributionExpertBank, Long> {
    List<BuyerDistributionExpertBank> findAllByBuyerIdAndDeletedIsFalse(String buyerId);
    BuyerDistributionExpertBank findByBuyerIdAndAccountNumberAndDeletedIsFalse(String buyerId, String bankCardNo);
}
