/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerUsageAgreementRepository.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.buyer.usageagreement.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.common.entity.mama.UsageAgreement;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface BuyerUsageAgreementRepository extends FwkBaseRepository<UsageAgreement, Integer> {
    UsageAgreement findFirstByCodeAndDraftIsFalseOrderByVersionDesc(String code);
}
