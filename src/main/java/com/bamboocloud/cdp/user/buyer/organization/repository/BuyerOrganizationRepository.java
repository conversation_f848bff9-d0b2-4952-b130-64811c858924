/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAddressRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.organization.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.Organization;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Shu
 */
@Repository
public interface BuyerOrganizationRepository extends FwkBaseRepository<Organization, String> {
    Organization findOrganizationById(String id);

    List<Organization> findOrganizationByIdIn(List<String> ids);
}
