/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: BuyerDeviceRepository.java
 * @createdDate: 2022/10/19 11:55:19
 *
 */

package com.bamboocloud.cdp.user.buyer.security.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDevice;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface BuyerDeviceRepository extends FwkBaseRepository<BuyerDevice, Long> {
    BuyerDevice findFirstByBuyerIdAndDeviceId(String buyerId, String deviceId);
}
