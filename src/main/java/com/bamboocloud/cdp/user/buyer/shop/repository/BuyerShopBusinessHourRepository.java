/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorShopBusinessHourRepository.java
 * @createdDate: 2022/08/18 15:26:18
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopBusinessHour;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface BuyerShopBusinessHourRepository extends FwkBaseRepository<ShopBusinessHour, Integer> {

    List<ShopBusinessHour> findAllByShopId(String shopId);
}
