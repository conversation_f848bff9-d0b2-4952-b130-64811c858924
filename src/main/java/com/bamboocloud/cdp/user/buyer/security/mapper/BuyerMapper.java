/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerMapper.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.security.mapper;

import com.bamboocloud.cdp.boot.user.common.bo.base.BaseLoginUserBo;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerDto;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerUpdateBaseInfoDto;
import com.bamboocloud.cdp.user.common.dto.buyer.BuyerUpdateMobileDto;
import com.bamboocloud.cdp.user.common.dto.everyone.cps.CreditSendBuyerInfoDto;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerCreationVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerUpdateVo;
import com.bamboocloud.cdp.user.common.vo.buyer.BuyerWxMiniGetMobileVo;
import com.bamboocloud.cdp.user.sdk.domain.entity.base.UserLoginInfoWxMini;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxAppLoginResultDto;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxMiniLoginResultDto;
import com.bamboocloud.cdp.util.sdk.common.dto.security.WxMiniUserMobileInfoDto;
import com.bamboocloud.cdp.util.sdk.common.vo.security.WxMiniExtractUserMobileInfoVo;
import org.mapstruct.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Component
@DecoratedWith(BuyerMapperDecorator.class)
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuyerMapper {
    @Mapping(target = "mobile", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerCreationVo.getMobile()))")
    @Mapping(target = "name", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerCreationVo.getName()))")
    @Mapping(target = "email", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerCreationVo.getEmail()))")
    @Mapping(target = "birthday", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptBirthDayByPrivateKey(buyerCreationVo.getBirthday()))")
    @Mapping(target = "genderCode", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerCreationVo.getGender()))")
    @Mapping(target = "password", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerCreationVo.getPassword()))")
    Buyer toEntityForCreation(BuyerCreationVo buyerCreationVo);

    @Mapping(target = "birthday", expression = "java((buyer.getBirthday() != null) ? com.bamboocloud.cdp.user.sdk.util.RsaUtils.encryptByPrivateKey(String.valueOf(buyer.getBirthday())) : null)")
    @Mapping(target = "mobile", expression = "java((buyer.getMobile() != null) ? com.bamboocloud.cdp.user.sdk.util.RsaUtils.encryptByPrivateKey(buyer.getMobile()) : null)")
    @Mapping(target = "name", expression = "java((buyer.getName() != null) ? com.bamboocloud.cdp.user.sdk.util.RsaUtils.encryptByPrivateKey(buyer.getName()) : null)")
    @Mapping(target = "email", expression = "java((buyer.getEmail() != null) ? com.bamboocloud.cdp.user.sdk.util.RsaUtils.encryptByPrivateKey(buyer.getEmail()) : null)")
    BuyerDto toDto(Buyer buyer);

    @Mappings({
            @Mapping(target = "wxMiniOpenId", source = "openId"),
            @Mapping(target = "wxUnionId", source = "unionId"),
    })
    Buyer toEntityForCreation(WxMiniLoginResultDto wxMiniLoginResultDto);

    @Mappings({
            @Mapping(target = "wxMiniOpenId", source = "openId"),
            @Mapping(target = "wxUnionId", source = "unionId"),
    })
    Buyer toEntityForCreation(WxAppLoginResultDto wxAppLoginResultDto);

    @Mappings({
            @Mapping(target = "wxMiniOpenId", source = "openId"),
            @Mapping(target = "wxUnionId", source = "unionId"),
    })
    Buyer toEntityForUpdate(WxMiniLoginResultDto wxMiniLoginResultDto, @MappingTarget Buyer buyer);

    @Mappings({
            @Mapping(target = "mobileCountryCode", source = "countryCode"),
            @Mapping(target = "mobile", expression = "java(com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(wxMiniUserMobileInfoDto.getPurePhoneNumber()))")
    })
    Buyer toEntityForUpdate(WxMiniUserMobileInfoDto wxMiniUserMobileInfoDto, @MappingTarget Buyer buyer);

    @Mapping(target = "mobile", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateVo.getMobile())?buyer.getMobile():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateVo.getMobile()))")
    @Mapping(target = "name", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateVo.getName())?buyer.getName():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateVo.getName()))")
    @Mapping(target = "email", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateVo.getEmail())?buyer.getEmail():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateVo.getEmail()))")
    @Mapping(target = "birthday", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateVo.getBirthday())?buyer.getBirthday():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptBirthDayByPrivateKey(buyerUpdateVo.getBirthday()))")
    @Mapping(target = "genderCode", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateVo.getGenderCode())?buyer.getGenderCode():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateVo.getGenderCode()))")
    Buyer toEntityForUpdate(BuyerUpdateVo buyerUpdateVo, @MappingTarget Buyer buyer);

    @Mapping(target = "mobile", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateDto.getMobile())?buyer.getMobile():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateDto.getMobile()))")
    Buyer toEntityForUpdate(BuyerUpdateMobileDto buyerUpdateDto, @MappingTarget Buyer buyer);

    @Mapping(target = "name", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateDto.getName())?buyer.getName():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateDto.getName()))")
    @Mapping(target = "email", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateDto.getEmail())?buyer.getEmail():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateDto.getEmail()))")
    @Mapping(target = "birthday", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateDto.getBirthday())?buyer.getBirthday():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptBirthDayByPrivateKey(buyerUpdateDto.getBirthday()))")
    @Mapping(target = "genderCode", expression = "java(com.bamboocloud.cdp.framework.core.util.FwkStringUtil.isBlank(buyerUpdateDto.getGender())?buyer.getGenderCode():com.bamboocloud.cdp.user.sdk.util.RsaUtils.decryptByPrivateKey(buyerUpdateDto.getGender()))")
    Buyer toEntityForUpdate(BuyerUpdateBaseInfoDto buyerUpdateDto, @MappingTarget Buyer buyer);

    LoginBuyerBo toLoginBo(Buyer buyer);

    BuyerDto loginBoToDto(LoginBuyerBo loginBuyerBo);

    LoginBuyerBo toBoForUpdate(Buyer buyer, @MappingTarget LoginBuyerBo loginBuyerBo);

    @Mapping(target = "userId", source = "id")
    @Mapping(target = "id", ignore = true)
    UserLoginInfoWxMini baseLoginUserBoToUserLoginInfoDto(BaseLoginUserBo baseLoginUserBo);

    UserLoginInfoWxMini toEntityForUpdate(UserLoginInfoWxMini oldUserLoginInfoWxMini,
                                          @MappingTarget UserLoginInfoWxMini userLoginInfoWxMini);

    WxMiniExtractUserMobileInfoVo buyerWxMiniGetMobileVoToWxMiniExtractUserMobileInfoVo(BuyerWxMiniGetMobileVo buyerWxMiniGetMobileVo);

    @Mapping(target = "individualID", source = "id")
    @Mapping(target = "realName", source = "name")
    @Mapping(target = "phoneNumber", source = "mobile")
    @Mapping(target = "idCard", source = "certNumber")
    CreditSendBuyerInfoDto toCreditSendBuyerInfoDto(Buyer buyer);
}
