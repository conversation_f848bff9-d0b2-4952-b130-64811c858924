/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopBuyerViewService.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.service;

import com.bamboocloud.cdp.user.common.dto.buyer.shop.BuyerShopBuyerViewDto;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.buyerview.BuyerShopBuyerViewCreationVo;
import com.bamboocloud.cdp.user.common.vo.buyer.shop.buyerview.BuyerShopBuyerViewSearchVo;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerShopBuyerViewService {
    /**
     * 新增
     *
     * @param buyerShopBuyerViewCreationVo
     */
    void create(BuyerShopBuyerViewCreationVo buyerShopBuyerViewCreationVo);

    /**
     * 推出浏览
     * @param buyerShopBuyerViewCreationVo
     */
    void stopView(BuyerShopBuyerViewCreationVo buyerShopBuyerViewCreationVo);

    /**
     * 删除
     * @param ids
     */
    void bulkDelete(List<String> ids);

    /**
     * 分页查询
     * @param buyerShopBuyerViewSearchVo
     * @return
     */
    List<BuyerShopBuyerViewDto> search(BuyerShopBuyerViewSearchVo buyerShopBuyerViewSearchVo);
}
