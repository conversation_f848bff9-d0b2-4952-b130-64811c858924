/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerAuthServiceImpl.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertWithDrawQueryDslRepository;
import com.bamboocloud.cdp.user.buyer.distributionexpert.repository.BuyerDistributionExpertWithDrawRepository;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.ExpertWithDrawListVo;
import com.bamboocloud.cdp.user.sdk.constant.ShopExtPayFundWithDrawConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.BuyerDistributionExpertWithDrawDto;
import com.bamboocloud.cdp.user.sdk.domain.dto.buyer.distributionexpert.ExpertWithDrawPageDto;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.BuyerDistributionExpertWithDraw;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Service
@Slf4j
public class BuyerDistributionExpertWithDrawServiceImpl extends BaseBuyerService implements BuyerDistributionExpertWithDrawService {
    @Autowired
    private BuyerDistributionExpertWithDrawRepository buyerDistributionExpertWithDrawRepository;
    @Autowired
    private OperationLogUtil operationLogUtil;
    @PersistenceContext
    private EntityManager entityManager;
    @Autowired
    private BuyerDistributionExpertWithDrawQueryDslRepository buyerDistributionExpertWithDrawQueryDslRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerDistributionExpertWithDraw create(BuyerDistributionExpertWithDraw draw) {
        operationLogUtil.setCreateCommonInformation(draw, UserTypeConstant.BUYER);
        buyerDistributionExpertWithDrawRepository.saveAndFlush(draw);
        return draw;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerDistributionExpertWithDraw update(BuyerDistributionExpertWithDraw draw) {
        operationLogUtil.setUpdateCommonInformation(draw, UserTypeConstant.BUYER);
        buyerDistributionExpertWithDrawRepository.saveAndFlush(draw);
        entityManager.refresh(draw);
        return draw;
    }
    @Override
    public BuyerDistributionExpertWithDraw getByExtBizOrderNo(String extBizOrderNo) {
        return buyerDistributionExpertWithDrawRepository.findFirstByExtBizOrderNo(extBizOrderNo);
    }

    @Override
    public ExpertWithDrawPageDto withDrawList(String buyerId,ExpertWithDrawListVo param) {
        ExpertWithDrawPageDto pageDto=buyerDistributionExpertWithDrawQueryDslRepository.withDrawList(buyerId,param);
        pageDto.setTotalExtPayAmount(totalExtPayAmount(buyerId));
        return pageDto;
    }

    @Override
    public BigDecimal totalExtPayAmount(String buyerId) {
        ExpertWithDrawPageDto pageDtoTotal=buyerDistributionExpertWithDrawQueryDslRepository.withDrawList(buyerId,new ExpertWithDrawListVo(null,null,ShopExtPayFundWithDrawConstant.SHOP_EXT_PAY_FUND_WITH_DRAW_STATUS_SUCCESS.getCode()));
        BigDecimal totalAmount=pageDtoTotal.getWithDrawDtos().stream().map(BuyerDistributionExpertWithDrawDto::getExtPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
}