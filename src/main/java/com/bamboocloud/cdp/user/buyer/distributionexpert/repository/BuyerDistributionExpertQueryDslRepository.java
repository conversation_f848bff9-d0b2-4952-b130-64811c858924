/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaVendorConfigRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.distributionexpert.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.ExpertPartnerDto;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.ExpertPartnerPageListDto;
import com.bamboocloud.cdp.user.common.dto.buyer.distributionexpert.ExpertPartnerSummarizeDto;
import com.bamboocloud.cdp.user.common.vo.buyer.distributionexpert.ExpertWithPartnerRequest;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyer;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.QBuyerDistributionExpert;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.blazebit.persistence.querydsl.BlazeJPAQueryFactory;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;

/**
 *
 */
@Component
public class BuyerDistributionExpertQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Autowired
    private BlazeJPAQueryFactory blazeJpaQueryFactory;

    public ExpertPartnerPageListDto listByPid(String distributionExpertId, ExpertWithPartnerRequest expertWithPartnerRequest) {
        QBuyerDistributionExpert buyerDistributionExpert = QBuyerDistributionExpert.buyerDistributionExpert;
        QBuyer buyer = QBuyer.buyer;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(buyerDistributionExpert.pid.eq(distributionExpertId));
        builder.and(buyerDistributionExpert.deleted.isFalse());

        BlazeJPAQuery<ExpertPartnerDto> jpaQuery = blazeJpaQueryFactory.select(Projections.constructor(ExpertPartnerDto.class,
                buyerDistributionExpert.buyerId,
                buyerDistributionExpert.id,
                buyer.nickName,
                buyer.avatarUrl,
                buyerDistributionExpert.contributionIncome
            )).from(buyerDistributionExpert)
            .leftJoin(buyer).on(buyer.id.eq(buyerDistributionExpert.buyerId))
            .where(builder);

        ExpertPartnerPageListDto dto = new ExpertPartnerPageListDto();
        dto.setTotalCount(jpaQuery.fetchCount());
        if (!ObjectUtils.isEmpty(expertWithPartnerRequest.getOffset()) &&
            !ObjectUtils.isEmpty(expertWithPartnerRequest.getLimit())) {
            jpaQuery.limit(expertWithPartnerRequest.getLimit())
                .offset(expertWithPartnerRequest.getLimit() * expertWithPartnerRequest.getOffset());
        }
        dto.setExpertPartnerDtos(jpaQuery.orderBy(buyerDistributionExpert.contributionIncome.desc()).fetch());
        return dto;
    }

    public ExpertPartnerSummarizeDto getExpertPartnerSummarize(String distributionExpertId) {
        QBuyerDistributionExpert buyerDistributionExpert = QBuyerDistributionExpert.buyerDistributionExpert;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(buyerDistributionExpert.pid.eq(distributionExpertId));
        builder.and(buyerDistributionExpert.deleted.isFalse());

        long count = blazeJpaQueryFactory.select(buyerDistributionExpert.id).from(buyerDistributionExpert).where(builder).fetchCount();
        BigDecimal sum = blazeJpaQueryFactory.select(buyerDistributionExpert.contributionIncome.sum()).from(buyerDistributionExpert)
            .where(builder).fetchFirst();

        ExpertPartnerSummarizeDto dto = new ExpertPartnerSummarizeDto();
        dto.setTotalExpertNum(count);
        dto.setTotalContributionIncome(sum == null ? new BigDecimal("0.00") : sum);

        return dto;
    }
}
