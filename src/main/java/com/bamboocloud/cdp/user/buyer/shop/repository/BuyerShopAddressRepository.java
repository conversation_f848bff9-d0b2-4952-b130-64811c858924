/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopBuyerRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.shop.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.vendor.organization.ShopAddress;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Repository
public interface BuyerShopAddressRepository extends FwkBaseRepository<ShopAddress, Integer> {

    List<ShopAddress> findByShopIdInAndBusinessAddressIsTrue(List<String> shopIds);

    ShopAddress findFirstByShopIdAndBusinessAddressIsTrue(String shopId);
}
