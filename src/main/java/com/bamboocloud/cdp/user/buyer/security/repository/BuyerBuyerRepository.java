/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerRepository.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.buyer.security.repository;


import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.user.sdk.domain.entity.buyer.Buyer;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Repository
public interface BuyerBuyerRepository extends FwkBaseRepository<Buyer, String> {

    Buyer findByMobileAndDeletedIsFalse(String mobile);

    Buyer findByCertNumberAndDeletedIsFalse(String certNumber);

    Buyer findByWxMiniOpenId(String openId);

    Buyer findByMobile(String mobile);

    Buyer findByWxAppOpenId(String wxAppOpenId);

    Buyer findByWxUnionId(String wxUnionId);

    Buyer findFirstByCertNumber(String certNumber);

    Buyer findFirstByAppleId(String appleId);

    /**
     * 分页查询分销员邀请用户列表
     *
     * @param shareId  分享员用户ID
     * @param pageable 分页信息
     * @return
     */
    @Query("SELECT u FROM Buyer u WHERE u.shareId = :shareId")
    Page<Buyer> page(@Param("shareId") String shareId, Pageable pageable);

}
