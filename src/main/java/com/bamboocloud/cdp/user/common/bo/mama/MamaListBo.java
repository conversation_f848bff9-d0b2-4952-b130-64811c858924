/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaListBo.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.mama;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> <PERSON>
 * @description:
 */
@Data
@AllArgsConstructor
public class MamaListBo implements Serializable {
    private String id;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 名称
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 启用
     */
    private boolean enabled;

    /**
     * 操作人
     */
    private String updatedUserName;

    /**
     * 最后操作时间
     */
    private LocalDateTime updatedDate;
}
