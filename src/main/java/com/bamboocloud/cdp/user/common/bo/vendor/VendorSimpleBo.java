/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: LoginVendorBo.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.vendor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 已登录的商家
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VendorSimpleBo implements Serializable {
    /**
     * 名字
     */
    private String name;

    /**
     * 电话
     */
    private String mobile;

    /**
     * 证件号
     * notnull
     */
    private String certNumber;
}
