/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: BaseDeviceVo.java
 * @createdDate: 2022/08/12 18:08:12
 *
 */

package com.bamboocloud.cdp.user.common.vo.base;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 */
@Data
public class BaseDeviceVo implements Serializable {

    private String buyerId;

    /**
     * NotNull，阿里云设备推送Id
     */
    private String deviceId;

    /**
     * NotNull，设备类型，IOS/iOS，ANDROID/Android
     */
    private String deviceTypeCode;

    /**
     * 设备名称
     */
    private String deviceName;
}
