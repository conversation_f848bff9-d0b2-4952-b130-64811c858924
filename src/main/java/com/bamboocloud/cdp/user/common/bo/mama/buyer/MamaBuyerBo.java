/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: MamaBuyerBo.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.mama.buyer;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MamaBuyerBo implements Serializable {
    private String id;

    /**
     * 设备国家代码
     */
    private String mobileCountryCode;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 名称
     */
    private String name;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 性别
     */
    private String genderCode;

    /**
     * 头像Url
     */
    private String avatarUrl;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信openId
     */
    private String wxMiniOpenId;

    /**
     * 描述
     */
    private String description;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 注册时间
     */
    private LocalDateTime createdDate;

    /**
     * 是否为测试
     */
    private boolean testOnly;
}
