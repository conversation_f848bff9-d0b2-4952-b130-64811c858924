/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: EveryoneVendorSimpleBo.java
 * @createdDate: 2023/04/03 18:03:03
 *
 */

package com.bamboocloud.cdp.user.common.bo.everyone.vendor;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EveryoneVendorSimpleBo implements Serializable {
    private String name;

    private String mobile;

    private String certNumber;
}
