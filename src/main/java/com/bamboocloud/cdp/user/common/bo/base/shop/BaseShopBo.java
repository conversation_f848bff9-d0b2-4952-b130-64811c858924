/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BaseShopBo.java
 * @createdDate: 2023/03/02 16:21:02
 *
 */

package com.bamboocloud.cdp.user.common.bo.base.shop;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseShopBo implements Serializable {
    private String shopId;

    private String organizationId;
}
