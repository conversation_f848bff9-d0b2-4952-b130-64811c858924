package com.bamboocloud.cdp.user.common.vo.base.organization;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/12/5 14:54
 * @Description
 */
@Data
public class UnionPayOrganizationBankCreationVo implements Serializable {

    /**
     * 主体id
     */
    @NotBlank(message = "主体id 不能为空")
    private String organizationId;
    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号 不能为空")
    private String cardNo;
    /**
     * 银行卡预留手机
     */
    @NotBlank(message = "银行卡预留手机 不能为空")
    private String phone;
//    /**
//     * 开户人姓名
//     */
//    @NotBlank(message = "开户人姓名 不能为空")
//    private String name;
//    /**
//     * 身份证号
//     */
//    @NotBlank(message = "身份证号 不能为空")
//    private String identityNo;


}
