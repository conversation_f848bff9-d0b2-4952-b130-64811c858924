/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2024. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: TagBuyerGroupVo.java
 * @createdDate: 2024/01/16 12:54:16
 *
 */

package com.bamboocloud.cdp.user.common.vo.tag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagBuyerGroupVo implements Serializable {
    private static final long serialVersionUID = 1L;


    private Integer id;

    /**
     * 描述
     */
    private String description;

    /**
     * 标签二级
     */
    private List<TagBuyerTagVo> tagBuyerTagList;

}
