/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorOrganizationLegalPersonBo.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.vendor.organization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorOrganizationLegalPersonBo implements Serializable {
    private Integer id;

    /**
     * 经营主体Id， NotNull
     */
    private String organizationId;

    /**
     * 姓名， NotNull
     */
    private String name;

    /**
     * 证件类型。ID_CARD/身份证、INLAND_PASS_CERT/来往内地通行证、MAINLAND_PASS_CERT/来往大陆通行证、PASSPORT/护照
     * NotNull
     * Unique
     */
    private String certTypeCode;

    /**
     * 证件号码
     * NotNull
     */
    private String certNumber;

}
