/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-market-api
 * @file: TagMaterialGroupDto.java
 * @createdDate: 2022/09/09 11:17:09
 *
 */

package com.bamboocloud.cdp.user.common.vo.tag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagMaterialGroupDto implements Serializable {
    private static final long serialVersionUID = 1L;


    private Integer id;

    /**
     * 描述
     */
    private String description;

    /**
     * 标签
     */
    private List<TagMaterialTagDto> tagMaterialTagList;
}
