/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BaseVendorUpdateCertDto.java
 * @createdDate: 2022/09/26 16:51:26
 *
 */

package com.bamboocloud.cdp.user.common.vo.base.organization;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Data
public class BaseVendorUpdateCertDto implements Serializable {

    /**
     * 头像临时路径
     */
    private String avatarUrl;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 名称
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 证件类型。ID_CARD/身份证、INLAND_PASS_CERT/来往内地通行证、MAINLAND_PASS_CERT/来往大陆通行证、PASSPORT/护照
     */
    @NotBlank(message = "证件类型不能为空")
    private String certTypeCode;

    /**
     * 证件号码
     */
    @NotBlank(message = "证件号码不能为空")
    private String certNumber;

    /**
     * 证件正面照片临时路径
     */
    @NotBlank(message = "证件正面照片不能为空")
    private String certFrontImageUrl;

    /**
     * 证件背面照片临时路径
     */
    private String certRealImageUrl;

    /**
     * 手持证件照片临时路径
     */
    private String certPersonImageUrl;

    /**
     * 证件有效期
     */
    @NotBlank(message = "证件有效期不能为空")
    private String termEndDate;

    /**
     * 证件开始日期，
     */
    private LocalDate termStartDate;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 电话
     */
    private String mobile;
}
