/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorShopWxReviewStatusBo.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.vendor.shop;

import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorShopWxReviewStatusBo implements Serializable {

    private String id;

    /**
     * 类型
     */
    private BaseTypeDto organizationType;

    /**
     * NotNull, 组织机构审核状态
     * ORG_REVIEW_STATUS_WAIT_SUBMIT/待提交
     * ORG_REVIEW_STATUS_WAIT_REVIEW/待审核
     * ORG_REVIEW_STATUS_REJECTED/审核驳回
     * ORG_REVIEW_STATUS_APPROVED/审核通过
     */
    private BaseTypeDto organizationReviewStatus;

    /**
     * NotNull, 组织机构状态
     * ORG_STATUS_WAIT/待认证
     * ORG_STATUS_NORMAL/正常
     * ORG_STATUS_WAIT_REGULAR_REVIEW/正常待年检
     * ORG_STATUS_REGULAR_REVIEW_EXPIRED/年检逾期
     * ORG_STATUS_NOT_VALID/经营异常
     */
    private BaseTypeDto organizationStatus;

    /**
     * 审核驳回理由
     */
    private String organizationReviewRejectReason;

    /**
     * 组织机构有效期， 暂定一年(读取系统数据库设置)。例如有效期为2021年12月9日，
     * 在有效期前一个月（读取系统数据库设置），statusCode变为ORG_STATUS_WAIT_REVIEW ，
     * 过了有效期，statusCode变为ORG_STATUS_REVIEW_EXPIRED
     */
    @JsonFormat(pattern = SystemConstant.YYYY_MM_DD_HH_MM_SS_DATE)
    private LocalDateTime organizationStatusExpireDate;

    /**
     * 二级商户签约路径
     */
    private String signUrl;

    /**
     * 二级商户签约路径二维码
     */
    private String signQrCodeUrl;

    /**
     * 微信支付商驳回理由
     */
    private String wxPayApplymentRejectReason;

    /**
     * 微信支付商户申请状态
     * NOT_SUBMIT/未提交,
     * CHECKING/资料校验中,
     * ACCOUNT_NEED_VERIFY/待账户验证,
     * AUDITING/审核中,
     * REJECTED/已驳回,
     * NEED_SIGN/待签约,
     * FINISH/完成,
     * FROZEN/已冻结
     * CANCELED/已作废
     */
    private BaseTypeDto wxPayApplymentStatus;

    /**
     * 经营主体管理员电话
     */
    private String organizationVendorAdminMobile;

    /**
     * 是否可以修改经营主体
     */
    private boolean updateOrganization = true;

    /**
     * 二级商户待账户验证链接
     * 当申请状态为
     * ACCOUNT_NEED_VERIFY，且通过系统校验的申请单，将返回链接
     */
    private String legalPersonValidationUrl;

    /**
     * 二级商户待账户验证链接二维码
     */
    private String legalPersonValidationQrCodeUrl;

    /**
     * 默认为false, 平台创建的商户，是否由于缺失数据（例如法人/银行信息）跳过检查
     * 接入聚合支付平台后，平台UI要标示这些商户, 让管理人员注意补全资料，然后提交聚合支付平台审核。
     */
    private boolean mamaCreatedSkipValidation;

    /**
     * 默认为false, 是否平台创建的商户
     */
    private boolean mamaCreated;

    /**
     * 聚合支付子商户状态
     */
    private BaseTypeDto extPayUserStatus;

    /**
     * 聚合支付子商户状态驳回原因
     */
    private String extPayUserRejectReason;

    /**
     * 是否存在新生审核完成的店铺
     */
    private boolean alreadyExistsExtPayShop;

    /**
     * 签约状态
     */
    private String extPaySignStatusCode;

    /**
     * 影印件状态
     */
    private String extPayIDCardOcrStatusCode;


    /**
     * 通联银行卡绑定情况
     */
    private Boolean accountBindSuccess;

}

