/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2024. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: TagBuyerTagVo.java
 * @createdDate: 2024/01/16 12:54:16
 *
 */

package com.bamboocloud.cdp.user.common.vo.tag;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TagBuyerTagVo implements Serializable {

    private Integer id;

    /**
     * name
     */
    private String name;

    /**
     * 用户标签
     */
    private String buyerTag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 一级标签id
     */
    private Integer tagBuyerGroupId;
}
