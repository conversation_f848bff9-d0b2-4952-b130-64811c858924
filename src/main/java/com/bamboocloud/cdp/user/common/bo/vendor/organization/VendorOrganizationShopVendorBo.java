/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: VendorOrganizationShopVendorBo.java
 * @createdDate: 2022/07/25 17:51:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.vendor.organization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VendorOrganizationShopVendorBo implements Serializable {

    private Integer id;

    /**
     * 是否店主
     */
    private boolean admin;

    /**
     * 经营主体Id
     */
    private String organizationId;

    /**
     * 手机号
     */
    private String vendorMobile;

    /**
     * 经营主体类型 。eg：经营主体类型。COMPANY/企业, COMPANY_SELF_EMPLOYED/个体工商户,  INDIVIDUAL/个人
     * NotNull
     */
    private String organizationTypeCode;

    /**
     * 负责人姓名
     */
    private String vendorName;

    /**
     * 经营主体企业名称
     */
    private String organizationCompanyName;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 店铺名。 NotNull，unique
     */
    private String shopName;

    /**
     * NotNull ，logo图片路径 ，unique
     */
    private String shopLogoUrl;

    /**
     * 商家用户Id
     */
    private String vendorId;

    public VendorOrganizationShopVendorBo(Integer id, boolean admin, String organizationId, String vendorMobile,
                                          String vendorName, String shopId, String shopName, String shopLogoUrl,
                                          String vendorId) {
        this.id = id;
        this.admin = admin;
        this.organizationId = organizationId;
        this.vendorMobile = vendorMobile;
        this.vendorName = vendorName;
        this.shopId = shopId;
        this.shopName = shopName;
        this.shopLogoUrl = shopLogoUrl;
        this.vendorId = vendorId;
    }
}
