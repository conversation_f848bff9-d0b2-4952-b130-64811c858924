/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: BaseOrganizationLegalPersonCreationVo.java
 * @createdDate: 2022/10/14 11:43:14
 *
 */

package com.bamboocloud.cdp.user.common.vo.base.organization;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
public class BaseOrganizationLegalPersonCreationVo implements Serializable {
    /**
     * 姓名， NotNull
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 证件类型。ID_CARD/身份证、INLAND_PASS_CERT/来往内地通行证、MAINLAND_PASS_CERT/来往大陆通行证、PASSPORT/护照
     * NotNull
     * Unique
     */
    @NotBlank(message = "证件类型不能为空")
    private String certTypeCode;

    /**
     * 证件号码
     * NotNull
     */
    @NotBlank(message = "证件号码不能为空")
    private String certNumber;

    /**
     * NotNull，证件正面照片临时路径
     *
     */
    @NotBlank(message = "证件正面照片不能为空")
    private String certFrontImageUrl;

    /**
     * NotNull，证件背面照片临时路径  护照没有背面照
     *
     */
    private String certRealImageUrl;

    /**
     * NotNull，手持证件照片Base64
     *
     */
    private String certPersonImageUrl;

    /**
     * 证件居住地址，主体类型为企业时，需要填写
     */
    private String certAddress;

    /**
     * NotNull, 证件有效期结束日期
     */
    @NotBlank(message = "证件有效期不能为空")
    private String termEndDate;

    /**
     * 证件开始日期，
     */
    private LocalDate termStartDate;

    /**
     * 法人手机号
     */
    private String legalPhone;
}
