/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BuyerShopInfoBo.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.common.bo.buyer.shop;

import com.bamboocloud.cdp.user.sdk.domain.dto.base.BaseTypeDto;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Data
public class BuyerShopInfoBo implements Serializable {
    private String id;

    /**
     * 经营主体Id 。 NotNull，unique
     */
    private String organizationId;

    /**
     * 店铺名。 NotNull，unique
     */
    private String name;

    /**
     * 店铺编号, NotNull, 纯数字9位
     * unique
     */
    private Integer code;

    /**
     * 店铺简介
     */
    private String intro;

    /**
     * NotNull ，logo图片路径 ，unique
     */
    private String logoUrl;

    /**
     * 店铺状态
     */
    private String statusCode;

    private BaseTypeDto status;

    /**
     * 二维码 unique
     */
    private String qrCodeUrl;

    /**
     * 开店时间
     */
    private LocalDateTime startDate;

    public BuyerShopInfoBo(String id, String organizationId, String name, Integer code,
                           String intro, String logoUrl, String statusCode, String qrCodeUrl, LocalDateTime startDate) {
        this.id = id;
        this.organizationId = organizationId;
        this.name = name;
        this.code = code;
        this.intro = intro;
        this.logoUrl = logoUrl;
        this.statusCode = statusCode;
        this.qrCodeUrl = qrCodeUrl;
        this.startDate = startDate;
    }
}
