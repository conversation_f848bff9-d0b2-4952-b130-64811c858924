/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-user-api
 * @file: BaseUserWxMiniExtractUserMobileInfoDto.java
 * @createdDate: 2022/07/25 14:09:25
 *
 */

package com.bamboocloud.cdp.user.common.vo.base;


import com.bamboocloud.cdp.util.sdk.common.vo.security.WxMiniExtractUserMobileInfoVo;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR> Mo
 * @description:
 */
@Data
public class BaseUserWxMiniExtractUserMobileInfoVo extends WxMiniExtractUserMobileInfoVo {
    /**
     * ID
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 加密数据
     */
    @NotBlank(message = "encryptedData不能为空")
    private String encryptedData;

    /**
     * 偏移量
     */
    @NotBlank(message = "iv不能为空")
    private String iv;

    /**
     * 数字签名
     */
    private String signature;
}
