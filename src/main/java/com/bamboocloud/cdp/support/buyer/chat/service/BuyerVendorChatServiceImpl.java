/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerVendorChatServiceImpl.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseVendorService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.buyer.chat.repository.BuyerVendorChatRepository;
import com.bamboocloud.cdp.support.common.dto.vendor.chat.VendorChatListDto;
import com.bamboocloud.cdp.support.sdk.common.entity.vendor.chat.VendorChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.support.vendor.chat.repository.VendorChatQueryDslRepository;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerVendorChatServiceImpl extends BaseVendorService implements BuyerVendorChatService {

    @Autowired
    private VendorChatQueryDslRepository vendorChatQueryDslRepository;

    @Autowired
    private BuyerVendorChatRepository buyerVendorChatRepository;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private OperationLogUtil operationLogUtil;

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public VendorChat getByVendorIdAndShopIdAndUserIdAndUserTypeCode(String vendorId, String shopId, String userId, String userTypeCode) {
        return vendorChatQueryDslRepository.getByVendorIdAndShopIdAndUserIdAndUserTypeCode(vendorId, shopId, userId, userTypeCode);
    }

    @Override
    public void updateVendorChatForRedis(VendorChat vendorChat, ChatRecord chatRecord) {
        String vendorChatListDtoForRedis = (String) fwkCacheService.hGet(cacheConstant.getKeyVendorChat(
                        FwkStringUtil.isNotBlank(vendorChat.getShopId()) ? vendorChat.getShopId() : null, vendorChat.getVendorId()),
                vendorChat.getId().toString());
        if (FwkStringUtil.isNotBlank(vendorChatListDtoForRedis)) {
            VendorChatListDto vendorChatListDto = FwkJsonUtil.toObject(vendorChatListDtoForRedis, VendorChatListDto.class);
            vendorChatListDto.setTop(vendorChat.isTop());
            if (!ObjectUtils.isEmpty(chatRecord)) {
                vendorChatListDto.setMessage(chatRecord.getMessage());
                vendorChatListDto.setMessageDate(chatRecord.getSendDate());
                vendorChatListDto.setMessageTypeCode(chatRecord.getMessageTypeCode());
                vendorChatListDto.setSenderUserTypeCode(chatRecord.getSenderUserTypeCode());
            }
            fwkCacheService.hPut(cacheConstant.getKeyVendorChat(FwkStringUtil.isNotBlank(vendorChatListDto.getShopId()) ? vendorChatListDto.getShopId() : null, vendorChatListDto.getVendorId()),
                    vendorChatListDto.getId().toString(), FwkJsonUtil.toJsonString(vendorChatListDto));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VendorChat update(VendorChat vendorChat) {
        try {
            operationLogUtil.setUpdateCommonInformation(vendorChat, UserTypeConstant.VENDOR);
        } catch (Exception e) {
            vendorChat.setUpdatedUserType(UserTypeConstant.VENDOR);
            vendorChat.setUpdatedUserId(vendorChat.getVendorId());
        }
        vendorChat = buyerVendorChatRepository.saveAndFlush(vendorChat);
        entityManager.refresh(vendorChat);
        return vendorChat;
    }
}
