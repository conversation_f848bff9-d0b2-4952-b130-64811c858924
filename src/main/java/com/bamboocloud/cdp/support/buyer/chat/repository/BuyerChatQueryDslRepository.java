/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatQueryDslRepository.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.repository;

import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkDeleteVo;
import com.bamboocloud.cdp.support.sdk.common.constant.ChatRecordConstant;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.QBuyerChat;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Component
public class BuyerChatQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    @Transactional(rollbackOn = Exception.class)
    public void bulkDelete(BuyerChatRecordBulkDeleteVo buyerChatRecordBulkDeleteVo, LoginBuyerBo loginBuyer) {
        BooleanBuilder builder = new BooleanBuilder();
        QBuyerChat qBuyerChat = QBuyerChat.buyerChat;
        if (!CollectionUtils.isEmpty(buyerChatRecordBulkDeleteVo.getIds())) {
            builder.and(qBuyerChat.id.in(buyerChatRecordBulkDeleteVo.getIds()));
        }
        queryFactory.update(qBuyerChat)
                .set(qBuyerChat.deleted, true)
                .set(qBuyerChat.deletedDate, LocalDateTime.now())
                .set(qBuyerChat.deletedUserId, loginBuyer.getId())
                .set(qBuyerChat.deletedUserType, loginBuyer.getUserTypeCode())
                .set(qBuyerChat.deletedUserNickName, loginBuyer.getNickName())
                .set(qBuyerChat.updatedDate, LocalDateTime.now())
                .set(qBuyerChat.updatedUserId, loginBuyer.getId())
                .set(qBuyerChat.updatedUserType, loginBuyer.getUserTypeCode())
                .set(qBuyerChat.updatedUserNickName, loginBuyer.getNickName())
                .where(builder)
                .execute();
    }

    @Transactional(rollbackOn = Exception.class)
    public void updateDeleted(BuyerChat buyerChat, boolean deleted) {
        BooleanBuilder builder = new BooleanBuilder();
        QBuyerChat qBuyerChat = QBuyerChat.buyerChat;
        builder.and(qBuyerChat.id.eq(buyerChat.getId()));
        queryFactory.update(qBuyerChat)
                .set(qBuyerChat.deleted, deleted)
                .set(qBuyerChat.deletedDate, deleted ? LocalDateTime.now() : null)
                .set(qBuyerChat.deletedUserId, deleted ? buyerChat.getBuyerId() : null)
                .set(qBuyerChat.deletedUserType, deleted ? UserTypeConstant.BUYER : null)
                .set(qBuyerChat.updatedDate, LocalDateTime.now())
                .where(builder)
                .execute();
    }

    public BuyerChat getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(String buyerId, String shopId, String userId, String userTypeCode) {
        BooleanBuilder builder = new BooleanBuilder();
        QBuyerChat qBuyerChat = QBuyerChat.buyerChat;
        if (FwkStringUtil.isNotBlank(buyerId)) {
            builder.and(qBuyerChat.buyerId.eq(buyerId));
        }
        if (FwkStringUtil.isNotBlank(shopId) && !ChatRecordConstant.SHOP_ID.equals(shopId)) {
            builder.and(qBuyerChat.shopId.eq(shopId));
        }
        if (FwkStringUtil.isNotBlank(userId)) {
            builder.and(qBuyerChat.userId.eq(userId));
        }
        if (FwkStringUtil.isNotBlank(userTypeCode)) {
            builder.and(qBuyerChat.userTypeCode.eq(userTypeCode));
        }
        return queryFactory.select(qBuyerChat).from(qBuyerChat).where(builder).fetchFirst();
    }
}
