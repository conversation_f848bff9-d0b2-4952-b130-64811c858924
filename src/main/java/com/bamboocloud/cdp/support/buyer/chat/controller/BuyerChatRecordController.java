/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatRecordController.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.controller;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.support.buyer.chat.service.BuyerChatRecordService;
import com.bamboocloud.cdp.support.buyer.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkUpdateReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordCreationVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordUpdateMessageTypeCodeVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordUpdateMessageVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordUpdateReceiverReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerNotificationTypeVo;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.support.sdk.common.mongo.mapper.ChatRecordMapper;
import com.bamboocloud.cdp.support.sdk.common.mongo.service.ChatRecordService;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class BuyerChatRecordController extends BaseBuyerController {

    @Autowired
    private ChatRecordService chatRecordService;

    @Autowired
    private BuyerChatRecordService buyerChatRecordService;

    @Autowired
    private ChatRecordMapper chatRecordMapper;

    /**
     * 新增聊天记录
     *
     * @param buyerChatRecordCreationVo
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_CREATE_V1)
    public FwkApiResponse<String> create(@RequestBody BuyerChatRecordCreationVo buyerChatRecordCreationVo) throws IOException,
            IllegalAccessException, ClientException {
        log.debug("BuyerChatRecordController - create");
        ChatRecord chatRecord = chatRecordMapper.toEntityForCreation(buyerChatRecordCreationVo);
        chatRecordService.create(chatRecord);
        return FwkApiResponse.success();
    }

    /**
     * 修改为已读
     *
     * @param buyerChatRecordUpdateReceiverReadVo
     * @return
     * @throws IOException
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_UPDATE_RECEIVER_READ_V1)
    public FwkApiResponse<String> updateReceiverRead(
        @Validated @RequestBody BuyerChatRecordUpdateReceiverReadVo buyerChatRecordUpdateReceiverReadVo) {
        log.debug("BuyerChatRecordController - updateReceiverRead-1");
        try {
            buyerChatRecordService.updateReceiverRead(buyerChatRecordUpdateReceiverReadVo);
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.debug("BuyerChatRecordController - updateReceiverRead-2");
        return FwkApiResponse.success();
    }

    /**
     * 全部已读(alan写的，通知的已读API，并非聊天的)
     *
     * @return
     * @throws IOException
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_BATCH_UPDATE_RECEIVER_READ_V1)
    public FwkApiResponse<String> batchUpdateReceiverRead(@RequestBody BuyerNotificationTypeVo buyerNotificationTypeVo) {
        log.debug("BuyerChatRecordController - batchUpdateReceiverRead");
        buyerChatRecordService.batchUpdateReceiverRead(buyerNotificationTypeVo);
        return FwkApiResponse.success();
    }

    /**
     * 修改某一行为已读
     *
     * @param id
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_UPDATE_RECEIVER_READ_BY_ID_V1)
    public FwkApiResponse<String> updateReceiverReadById(@PathVariable String id) throws IOException, IllegalAccessException {
        log.debug("BuyerChatRecordController - updateReceiverReadById");
        buyerChatRecordService.updateReceiverReadById(id);
        return FwkApiResponse.success();
    }

    /**
     * 修改多行为已读
     *
     * @param buyerChatRecordBulkUpdateReadVo
     * @return
     * @throws IOException
     * @throws IllegalAccessException
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_BULK_UPDATE_RECEIVER_READ_V1)
    public FwkApiResponse<String> bulkUpdateReceiverRead(@Validated @RequestBody BuyerChatRecordBulkUpdateReadVo buyerChatRecordBulkUpdateReadVo) throws IOException, IllegalAccessException {
        log.debug("BuyerChatRecordController - bulkUpdateReceiverReadById");
        buyerChatRecordService.bulkUpdateReceiverRead(buyerChatRecordBulkUpdateReadVo);
        return FwkApiResponse.success();
    }

    /**
     * 根据Id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable String id) throws IOException {
        log.debug("BuyerChatRecordController - delete");
        buyerChatRecordService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 撤回消息
     *
     * @param id
     * @param buyerChatRecordUpdateMessageTypeCodeVo
     * @return
     * @throws IOException
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_PULL_OUT_V1)
    public FwkApiResponse<String> pullout(@PathVariable String id,
                                              @RequestBody BuyerChatRecordUpdateMessageTypeCodeVo buyerChatRecordUpdateMessageTypeCodeVo) throws IOException {
        log.debug("BuyerChatRecordController - pullOut");
        buyerChatRecordService.updateMessageTypeCode(id, buyerChatRecordUpdateMessageTypeCodeVo.getMessageTypeCode());
        return FwkApiResponse.success();
    }

    /**
     * 修改消息类型
     *
     * @param id
     * @param buyerChatRecordUpdateMessageTypeCodeVo
     * @return
     * @throws IOException
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_UPDATE_MESSAGE_TYPE_CODE_V1)
    public FwkApiResponse<String> updateMessageTypeCode(@PathVariable String id,
                                                            @RequestBody BuyerChatRecordUpdateMessageTypeCodeVo buyerChatRecordUpdateMessageTypeCodeVo) throws IOException {
        log.debug("BuyerChatRecordController - updateMessageTypeCode");
        buyerChatRecordService.updateMessageTypeCode(id, buyerChatRecordUpdateMessageTypeCodeVo.getMessageTypeCode());
        return FwkApiResponse.success();
    }

    /**
     * 修改消息
     *
     * @param id
     * @param buyerChatRecordUpdateMessageVo
     * @return
     * @throws IOException
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_UPDATE_MESSAGE_V1)
    public FwkApiResponse<String> updateMessage(@PathVariable String id,
                                                    @Validated @RequestBody BuyerChatRecordUpdateMessageVo buyerChatRecordUpdateMessageVo) throws IOException {
        log.debug("BuyerChatRecordController - updateMessageTypeCode");
        buyerChatRecordService.updateMessage(id, buyerChatRecordUpdateMessageVo.getMessage());
        return FwkApiResponse.success();
    }

    /**
     * 标记已读或者未读
     *
     * @param id
     * @return
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_RECORD_MARK_READ_V1)
    public FwkApiResponse<String> markRead(@PathVariable Integer id) {
        log.debug("BuyerChatRecordController - markRead");
        buyerChatRecordService.markRead(id);
        return FwkApiResponse.success();
    }
}
