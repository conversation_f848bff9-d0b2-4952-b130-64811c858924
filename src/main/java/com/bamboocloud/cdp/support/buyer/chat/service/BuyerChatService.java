/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatService.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;

import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkDeleteVo;
import com.bamboocloud.cdp.support.sdk.common.dto.base.chat.BaseChatRecordCreationDto;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.IOException;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerChatService {
    /**
     * 新增
     *
     * @param buyerChat
     * @return
     */
    BuyerChat create(BuyerChat buyerChat);

    /**
     * 修改
     *
     * @param buyerChat
     * @return
     */
    BuyerChat update(BuyerChat buyerChat);

    /**
     * 根据聊天信息创建聊天
     *
     * @param baseChatRecordCreationDto
     * @return
     * @throws JsonProcessingException
     */
    BuyerChat create(BaseChatRecordCreationDto baseChatRecordCreationDto) throws JsonProcessingException;

    /**
     * 根据Id删除
     *
     * @param id
     * @throws IOException
     */
    void delete(Integer id) throws IOException;

    /**
     * 批量删除
     *
     * @param buyerChatRecordBulkDeleteVo
     * @throws IOException
     */
    void bulkDelete(BuyerChatRecordBulkDeleteVo buyerChatRecordBulkDeleteVo) throws IOException;

    /**
     * 根据iD获取
     *
     * @param id
     * @return
     */
    BuyerChat get(Integer id);

    /**
     * 根据buyerId、shopId、userId、userTypeCode获取
     *
     * @param buyerId
     * @param shopId
     * @param userId
     * @param userTypeCode
     * @return
     */
    BuyerChat getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(String buyerId, String shopId, String userId, String userTypeCode);

    /**
     * 更新redis
     *
     * @param buyerChat
     * @param chatRecord
     */
    void updateBuyerChatForRedis(BuyerChat buyerChat, ChatRecord chatRecord);
}
