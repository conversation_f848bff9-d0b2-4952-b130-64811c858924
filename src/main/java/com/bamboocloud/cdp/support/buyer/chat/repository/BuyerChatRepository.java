/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatRepository.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import jakarta.persistence.LockModeType;
import org.springframework.data.jpa.repository.Lock;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Repository
public interface BuyerChatRepository extends FwkBaseRepository<BuyerChat, Integer> {
    @Lock(value = LockModeType.PESSIMISTIC_WRITE)
    BuyerChat findFirstByShopIdAndBuyerId(String shopId, String buyerId);

    @Lock(value = LockModeType.PESSIMISTIC_WRITE)
    BuyerChat findFirstByBuyerIdAndUserTypeCode(String buyerId, String userTypeCode);
}
