/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatRecordService.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;


import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkUpdateReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordUpdateReceiverReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerNotificationTypeVo;

import java.io.IOException;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerChatRecordService {

    /**
     * 修改为已读
     *
     * @param buyerChatRecordUpdateReceiverReadVo
     * @throws IOException
     */
    void updateReceiverRead(BuyerChatRecordUpdateReceiverReadVo buyerChatRecordUpdateReceiverReadVo) throws IOException;

    /**
     * 全部已读
     */
    void batchUpdateReceiverRead(BuyerNotificationTypeVo buyerNotificationTypeVo);

    /**
     * 软删除聊天记录
     *
     * @param shopId
     * @param buyerId
     * @throws IOException
     */
    void updateDeleted(String shopId, String buyerId) throws IOException;

    /**
     * 根據Id修改為已读
     *
     * @param id
     * @throws IOException
     * @throws IllegalAccessException
     */
    void updateReceiverReadById(String id) throws IOException, IllegalAccessException;

    /**
     * 批量修改为已读
     *
     * @param buyerChatRecordBulkUpdateReadVo
     * @throws IllegalAccessException
     * @throws IOException
     */
    void bulkUpdateReceiverRead(BuyerChatRecordBulkUpdateReadVo buyerChatRecordBulkUpdateReadVo) throws IllegalAccessException, IOException;

    /**
     * 删除
     *
     * @param id
     * @throws IOException
     */
    void delete(String id) throws IOException;

    /**
     * 修改消息类型
     *
     * @param id
     * @param messageTypeCode
     * @throws IOException
     */
    void updateMessageTypeCode(String id, String messageTypeCode) throws IOException;

    /**
     * 修改消息
     *
     * @param id
     * @param message
     * @throws IOException
     */
    void updateMessage(String id, String message) throws IOException;

    /**
     * 标记已读
     *
     * @param id
     */
    void markRead(Integer id);
}
