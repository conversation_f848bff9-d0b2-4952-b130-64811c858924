/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatRecordMapper.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.mapper;

import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordCreationVo;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuyerChatRecordMapper {
    ChatRecord toEntityForCreation(BuyerChatRecordCreationVo buyerChatRecordCreationVo);
}
