/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatRecordServiceImpl.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;

import com.alibaba.fastjson.JSONObject;
import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.constant.NotificationConstant;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatListDto;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatRecordBulkUpdateReadResponseDto;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatRecordUpdateMessageResponseDto;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatRecordUpdateReadResponseDto;
import com.bamboocloud.cdp.support.common.dto.notihistory.NotiHistoryRunSupDto;
import com.bamboocloud.cdp.support.common.dto.notihistory.NotiHistorySupDto;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkUpdateReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordUpdateReceiverReadVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerNotificationTypeVo;
import com.bamboocloud.cdp.support.config.socket.WebSocketServer;
import com.bamboocloud.cdp.support.sdk.common.constant.ChatRecordConstant;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.entity.vendor.chat.VendorChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.mongodb.bulk.BulkWriteResult;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.BulkOperations.BulkMode;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerChatRecordServiceImpl extends BaseBuyerService implements BuyerChatRecordService {


    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private BuyerChatService buyerChatService;

    @Autowired
    private BuyerVendorChatService buyerVendorChatService;


    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public void updateReceiverRead(BuyerChatRecordUpdateReceiverReadVo buyerChatRecordUpdateReceiverReadVo) throws IOException {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        // 使用 Criteria 构建查询条件
        Criteria criteria = Criteria.where(ChatRecordConstant.RECEIVER_USER_ID).is(loginBuyer.getId())
            .and(ChatRecordConstant.SHOP_ID).is(buyerChatRecordUpdateReceiverReadVo.getShopId())
            .and(ChatRecordConstant.SENDER_USER_TYPE_CODE).is(buyerChatRecordUpdateReceiverReadVo.getUserTypeCode())
            .and(ChatRecordConstant.RECEIVER_READ).is(false);

        Query query = new Query(criteria);
        List<ChatRecord> records = mongoTemplate.find(query, ChatRecord.class);

        if (!records.isEmpty()) {
            Update update = new Update().set(ChatRecordConstant.RECEIVER_READ, true);
            mongoTemplate.updateMulti(query, update, ChatRecord.class);

            updateBuyerChatNotReadTotalCount(loginBuyer.getId(), buyerChatRecordUpdateReceiverReadVo.getShopId(),
                buyerChatRecordUpdateReceiverReadVo.getUserId(), buyerChatRecordUpdateReceiverReadVo.getUserTypeCode(), true);
            BuyerChatRecordBulkUpdateReadResponseDto buyerChatRecordBulkUpdateReadResponseDto =
                new BuyerChatRecordBulkUpdateReadResponseDto();
            buyerChatRecordBulkUpdateReadResponseDto.setShopId(buyerChatRecordUpdateReceiverReadVo.getShopId());
            WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(buyerChatRecordBulkUpdateReadResponseDto),
                buyerChatRecordUpdateReceiverReadVo.getUserId());
        }
    }

    @Override
    public void batchUpdateReceiverRead(BuyerNotificationTypeVo buyerNotificationTypeVo) {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        if (ObjectUtils.isEmpty(buyerNotificationTypeVo) || FwkStringUtil.isBlank(buyerNotificationTypeVo.getTypeCode())) {
            Map<Object, Object> objectObjectMap = fwkCacheService.hGetAll(cacheConstant.getKeyBuyerChat(loginBuyer.getId()));
            if (!ObjectUtils.isEmpty(objectObjectMap)) {
                for (Object object : objectObjectMap.values()) {
                    BuyerChatListDto buyerChatListDto = FwkJsonUtil.toObject(object.toString(), BuyerChatListDto.class);
                    buyerChatListDto.setNotReadTotalCount(0);
                    fwkCacheService.hPut(cacheConstant.getKeyBuyerChat(buyerChatListDto.getBuyerId()),
                            buyerChatListDto.getId().toString(), FwkJsonUtil.toJsonString(buyerChatListDto));
                }
            }
            //清空所有类型的消息未读
            //获取key
            String key = cacheConstant.getKeyNotiHistoryPrefix(loginBuyer.getId(), "*", NotificationConstant.NOTIFICATION_CHANNEL_CODE_IN_APP.getCode(), "*");
            Set<String> keys = fwkCacheService.scanKeys(key);
            //从redis中移除这条记录
            if (!CollectionUtils.isEmpty(keys)) {
                fwkCacheService.delete(keys);
            }

        } else {
           /* String typeCode = buyerNotificationTypeVo.getTypeCode();
            String key = cacheConstant.getKeyNotiHistoryPrefix(loginBuyer.getId(), typeCode, NotificationConstant.NOTIFICATION_CHANNEL_CODE_IN_APP.getCode(), "*");
            Set<String> keys = fwkCacheService.scanKeys(key);
            //从redis中移除记录
            if (!CollectionUtils.isEmpty(keys)) {
                fwkCacheService.delete(keys);
            }*/
            String totalKey = cacheConstant.getKeyNotiHistoryPrefix(loginBuyer.getId(), "BUYER",
                NotificationConstant.NOTIFICATION_CHANNEL_CODE_IN_APP.getCode(), loginBuyer.getId());
            String rs = fwkCacheService.get(totalKey);
            if (FwkStringUtil.isNotBlank(rs)) {
                NotiHistoryRunSupDto notiHistorys = JSONObject.parseObject(rs, NotiHistoryRunSupDto.class);
                List<NotiHistorySupDto> notiHistories = notiHistorys.getNotiHistories();
                //将notiHistories中所有的typeCode都为typeCode的数据删除
                notiHistories.removeIf(notiHistorySupDto -> notiHistorySupDto.getTypeCode().equals(buyerNotificationTypeVo.getTypeCode()));
                //更新这个缓存
                fwkCacheService.set(totalKey, FwkJsonUtil.toJsonString(notiHistorys));
            }
        }
    }


    private void updateBuyerChatNotReadTotalCount(String buyerId, String shopId, String userId, String userTypeCode, boolean zero) {
        BuyerChat buyerChat = buyerChatService.getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(buyerId, shopId, userId, userTypeCode);
        if (ObjectUtils.isEmpty(buyerChat) && userTypeCode.equals(UserTypeConstant.MAMA)) {
            buyerChat = buyerChatService.getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(buyerId, null, null, userTypeCode);
        }
        if (!ObjectUtils.isEmpty(buyerChat)) {
            buyerChat.setNotReadTotalCount(zero ? 0 :
                    buyerChat.getNotReadTotalCount() > 0 ? buyerChat.getNotReadTotalCount() - 1 : 0);
            buyerChatService.update(buyerChat);
            //String buyerChatListDtoForRedis = (String) fwkCacheService.hGet(cacheConstant.getKeyBuyerChat(buyerId), buyerChat.getId().toString());
            //if (FwkStringUtil.isNotBlank(buyerChatListDtoForRedis)) {
            //    if (!ObjectUtils.isEmpty(buyerChat)) {
            //        BuyerChatListDto buyerChatListDto = FwkJsonUtil.toObject(buyerChatListDtoForRedis, BuyerChatListDto.class);
            //        if (!ObjectUtils.isEmpty(buyerChatListDto)) {
            //            buyerChatListDto.setNotReadTotalCount(zero ? 0 :
            //                    buyerChatListDto.getNotReadTotalCount() > 0 ? buyerChatListDto.getNotReadTotalCount() - 1 : 0);
            //            fwkCacheService.hPut(cacheConstant.getKeyBuyerChat(buyerChatListDto.getBuyerId()),
            //                    buyerChatListDto.getId().toString(), FwkJsonUtil.toJsonString(buyerChatListDto));
            //        }
            //    }
            //}
        }
    }

    @Override
    public void updateDeleted(String shopId, String buyerId) throws IOException {

        // 创建基础查询条件
        Criteria criteria = new Criteria();

        // 添加店铺ID的过滤条件（如果存在）
        if (shopId != null && !shopId.isEmpty()) {
            criteria.and("shopId").is(shopId);
        }

        // 创建 OR 条件：senderUserId == buyerId 或 receiverUserId == buyerId
        Criteria messageCriteria = new Criteria().orOperator(
            Criteria.where("senderUserId").is(buyerId),
            Criteria.where("receiverUserId").is(buyerId)
        );

        // 组合所有条件
        Query query = new Query(criteria.andOperator(messageCriteria));

        // 查询匹配的消息记录
        List<ChatRecord> chatRecords = mongoTemplate.find(query, ChatRecord.class);

        // 更新操作
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, ChatRecord.class);
        for (ChatRecord record : chatRecords) {
            Update update = new Update();

            // 判断当前记录的发送者是否为买家
            if (buyerId.equals(record.getSenderUserId())) {
                update.set("senderDeleted", true);
            } else {
                // 如果不是发送者，保留原值
                update.set("senderDeleted", record.isSenderDeleted());
            }

            // 判断当前记录的接收者是否为买家
            if (buyerId.equals(record.getReceiverUserId())) {
                update.set("receiverDeleted", true);
            } else {
                // 如果不是接收者，保留原值
                update.set("receiverDeleted", record.isReceiverDeleted());
            }

            // 添加到批量操作中
            bulkOps.updateOne(query, update);
            // 执行更新操作
            // mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(record.getId())), update, ChatRecord.class);
        }
        // 执行批量更新
        bulkOps.execute();
    }

    @Override
    public void updateReceiverReadById(String id) throws IOException, IllegalAccessException {

        // 1. 查询聊天记录
        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(id));

        ChatRecord chatRecord = mongoTemplate.findOne(query, ChatRecord.class);

        if (chatRecord == null) {
            throw new RuntimeException("Chat record not found");
        }

        // 2. 构造更新内容：receiverRead = true
        Update update = new Update();
        for (Field field : ChatRecord.class.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(chatRecord);
            if (fieldName.equals(ChatRecordConstant.RECEIVER_READ)) {
                update.set(fieldName, true);
            } else if (value != null) {
                update.set(fieldName, value);
            }
        }

        // 3. 执行更新
        mongoTemplate.updateFirst(query, update, ChatRecord.class);

        // 4. 减少未读消息总数
        updateBuyerChatNotReadTotalCount(getLoginBuyer().getId(), chatRecord.getShopId(), chatRecord.getSenderUserId(),
                chatRecord.getSenderUserTypeCode(), false);
        // 5. 构造响应 DTO 并推送 WebSocket 消息
        BuyerChatRecordUpdateReadResponseDto buyerChatRecordUpdateReadResponseDto =
                new BuyerChatRecordUpdateReadResponseDto();
        buyerChatRecordUpdateReadResponseDto.setId(chatRecord.getId());
        buyerChatRecordUpdateReadResponseDto.setShopId(chatRecord.getShopId());
        WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(buyerChatRecordUpdateReadResponseDto),
                chatRecord.getSenderUserId());
    }

    @Override
    public void bulkUpdateReceiverRead(BuyerChatRecordBulkUpdateReadVo buyerChatRecordBulkUpdateReadVo) throws IllegalAccessException, IOException {

        LoginBuyerBo loginBuyer = getLoginBuyer();

        // 1. 构造查询条件
        List<String> ids = buyerChatRecordBulkUpdateReadVo.getIds();
        Criteria criteria = Criteria.where("id").in(ids);
        Query query = new Query(criteria);

        // 2. 查询出所有需要更新的记录
        List<ChatRecord> chatRecords = mongoTemplate.find(query, ChatRecord.class);

        // 3. 初始化 BulkOperations
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkMode.UNORDERED, ChatRecord.class);

        // 4. 添加每个记录的更新操作
        for (ChatRecord chatRecord : chatRecords) {
            Update update = new Update().set("receiverRead", true); // 更新 receiverRead 字段
            Query updateQuery = new Query();
            bulkOps.updateMulti(updateQuery.addCriteria(Criteria.where("id").is(chatRecord.getId())), update);

            // 同步未读数
            updateBuyerChatNotReadTotalCount(loginBuyer.getId(), chatRecord.getShopId(),
                chatRecord.getSenderUserId(), chatRecord.getSenderUserTypeCode(), false);
        }

        // 5. 执行批量更新
        bulkOps.execute();

        // 6. 发送 WebSocket 消息

        BuyerChatRecordBulkUpdateReadResponseDto buyerChatRecordBulkUpdateReadResponseDto =
                new BuyerChatRecordBulkUpdateReadResponseDto();
        buyerChatRecordBulkUpdateReadResponseDto.setIds(buyerChatRecordBulkUpdateReadVo.getIds());
        buyerChatRecordBulkUpdateReadResponseDto.setShopId(chatRecords.get(0).getShopId());
        WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(buyerChatRecordBulkUpdateReadResponseDto),
                chatRecords.get(chatRecords.size() - 1).getSenderUserId());
    }

    @Override
    public void delete(String id) throws IOException {
        LoginBuyerBo loginBuyer = getLoginBuyer();

        // 构建查询条件
        Criteria criteria = Criteria.where("_id").is(id);
        Query query = new Query(criteria);

        // 查找所有匹配的聊天记录
        List<ChatRecord> chatRecords = mongoTemplate.find(query, ChatRecord.class);

        if (CollectionUtils.isEmpty(chatRecords)) {
            return;
        }

        // 准备批量更新操作
        List<Pair<Query, Update>> updates = new ArrayList<>();
        String receiverUserId = null, shopId = null, senderUserId = null, senderUserTypeCode = null;

        for (ChatRecord record : chatRecords) {
            // 构建每个记录的更新
            Update update = new Update();

            // 判断当前用户是发送方还是接收方
            if (record.getSenderUserId().equals(loginBuyer.getId())) {
                update.set("senderDeleted", true);
            }
            if (record.getReceiverUserId().equals(loginBuyer.getId())) {
                update.set("receiverDeleted", true);
            }

            // 保存需要的字段值
            receiverUserId = record.getReceiverUserId();
            shopId = record.getShopId();
            senderUserId = record.getSenderUserId();
            senderUserTypeCode = record.getSenderUserTypeCode();

            // 添加到批量更新列表
            updates.add(Pair.of(
                new Query(Criteria.where("_id").is(record.getId())),
                update
            ));
        }

        // 执行批量更新
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, ChatRecord.class);
        for (Pair<Query, Update> pair : updates) {
            bulkOps.updateOne(pair.getLeft(), pair.getRight());
        }
        bulkOps.execute();

        ChatRecord chatRecord = getChatRecord(shopId, loginBuyer.getId());
        BuyerChat buyerChat = buyerChatService.getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(loginBuyer.getId(),
            shopId, senderUserTypeCode.equals(UserTypeConstant.VENDOR) ? senderUserId : receiverUserId, null);
        if (!ObjectUtils.isEmpty(chatRecord)) {
            buyerChat.setMessage(chatRecord.getMessage());
            buyerChat.setMessageDate(chatRecord.getSendDate());
            buyerChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
            buyerChat.setSendMessageUserTypeCode(chatRecord.getSenderUserTypeCode());
            buyerChatService.update(buyerChat);
        }
    }


    @Override
    public void updateMessageTypeCode(String id, String messageTypeCode) throws IOException {
        // 构建查询条件
        Criteria criteria = Criteria.where("_id").is(id);
        Query query = Query.query(criteria);

        // 批量更新字段
        Update update = new Update()
            .set("messageTypeCode", messageTypeCode)
            .currentDate("updateTime"); // 可选的更新时间字段

        // 执行批量更新
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkMode.ORDERED, ChatRecord.class);
        bulkOps.updateMulti(query, update);
        BulkWriteResult result = bulkOps.execute();
        // 如果需要获取更新后的记录信息
        ChatRecord updatedRecord = mongoTemplate.findOne(query, ChatRecord.class);

        if (FwkStringUtil.isNotBlank(updatedRecord.getReceiverUserId()) && FwkStringUtil.isNotBlank(updatedRecord.getShopId())) {
            // 发送撤回消息给接受者
            sendReceiverUser(id, updatedRecord.getReceiverUserId(), updatedRecord.getShopId(), updatedRecord.getSenderUserId(),
                updatedRecord.getSenderUserTypeCode());
        }
    }

    @Override
    public void updateMessage(String id, String message) throws IOException {
        // 构建查询条件
        Criteria criteria = Criteria.where("_id").is(id);
        Query query = new Query(criteria);

        // 执行查询并更新
        Update update = new Update().set("message", message);

        // 使用批量更新操作
        BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.ORDERED, ChatRecord.class);
        bulkOps.updateMulti(query, update);
        BulkWriteResult result = bulkOps.execute();

        // 如果需要获取发送者ID和消息类型
        ChatRecord record = mongoTemplate.findOne(query, ChatRecord.class);

        if (record != null && ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_TRADE.equals(record.getMessageTypeCode())) {
            BuyerChatRecordUpdateMessageResponseDto responseDto = new BuyerChatRecordUpdateMessageResponseDto();
            responseDto.setId(id);
            responseDto.setMessage(message);
            responseDto.setMessageTypeCode(ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_TRADE);
            WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(responseDto), record.getSenderUserId());
        }
    }

    private void sendReceiverUser(String id, String receiverUserId, String shopId, String senderUserId, String senderUserTypeCode) throws IOException {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        ChatRecord chatRecord = getChatRecord(shopId, loginBuyer.getId());
        String userId = senderUserTypeCode.equals(UserTypeConstant.VENDOR) ? senderUserId : receiverUserId;
        BuyerChat buyerChat = buyerChatService.getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(loginBuyer.getId(),
                shopId, userId, null);
        if (!ObjectUtils.isEmpty(chatRecord)) {
            buyerChat.setMessage(chatRecord.getMessage());
            buyerChat.setMessageDate(chatRecord.getSendDate());
            buyerChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
            buyerChat.setSendMessageUserTypeCode(chatRecord.getSenderUserTypeCode());
            buyerChatService.update(buyerChat);
            //buyerChatService.updateBuyerChatForRedis(buyerChat, chatRecord);
        }
        VendorChat vendorChat = buyerVendorChatService.
                getByVendorIdAndShopIdAndUserIdAndUserTypeCode(userId, shopId, loginBuyer.getId(), null);
        if (!ObjectUtils.isEmpty(chatRecord)) {
            vendorChat.setMessage(chatRecord.getMessage());
            vendorChat.setMessageDate(chatRecord.getSendDate());
            vendorChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
            vendorChat.setSendMessageUserTypeCode(chatRecord.getSenderUserTypeCode());
            if (chatRecord.getMessageTypeCode().equals(ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_PULL_OUT)) {
                vendorChat.setNotReadTotalCount(vendorChat.getNotReadTotalCount() + 1);
            }
            buyerVendorChatService.update(vendorChat);
            //buyerVendorChatService.updateVendorChatForRedis(vendorChat, chatRecord);
        }
        BuyerChatRecordUpdateReadResponseDto buyerChatRecordBulkUpdateReadResponseDto = new
                BuyerChatRecordUpdateReadResponseDto();
        buyerChatRecordBulkUpdateReadResponseDto.setId(id);
        buyerChatRecordBulkUpdateReadResponseDto.setShopId(shopId);
        buyerChatRecordBulkUpdateReadResponseDto.setMessageTypeCode(ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_PULL_OUT);
        WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(buyerChatRecordBulkUpdateReadResponseDto),
                receiverUserId);
    }

    private ChatRecord getChatRecord(String shopId, String buyerId) {
        // 构建复合查询条件
        Criteria criteria = new Criteria();
        // 必须匹配shopId
        criteria.and("shopId").is(shopId);

        // 必须满足以下两个条件之一(MUST_PASS_ONE)
        Criteria orCriteria = new Criteria().orOperator(
            // 条件1: 接收者是buyerId且未被删除
            new Criteria().andOperator(
                Criteria.where("receiverUserId").is(buyerId),
                Criteria.where("receiverDeleted").is(false)
            ),
            // 条件2: 发送者是buyerId且未被删除
            new Criteria().andOperator(
                Criteria.where("senderUserId").is(buyerId),
                Criteria.where("senderDeleted").is(false)
            )
        );

        // 组合查询条件
        criteria.andOperator(orCriteria);

        // 构建查询并排序(按时间降序)，限制返回1条
        Query query = Query.query(criteria)
            .with(Sort.by(Sort.Direction.DESC, "timestamp")) // 假设有timestamp字段
            .limit(1);

        // 执行查询
        return mongoTemplate.findOne(query, ChatRecord.class);

    }

    @Override
    public void markRead(Integer id) {
        BuyerChat buyerChat = buyerChatService.get(id);
        if (!ObjectUtils.isEmpty(buyerChat)) {
            buyerChat.setNotReadTotalCount(buyerChat.getNotReadTotalCount() > 0 ? 0 : 1);
            buyerChatService.update(buyerChat);
            //String buyerChatListDtoForRedis = (String) fwkCacheService.hGet(cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId()),
            //        buyerChat.getId().toString());
            //if (FwkStringUtil.isNotBlank(buyerChatListDtoForRedis)) {
            //    BuyerChatListDto buyerChatListDto = FwkJsonUtil.toObject(buyerChatListDtoForRedis, BuyerChatListDto.class);
            //    buyerChatListDto.setNotReadTotalCount(buyerChatListDto.getNotReadTotalCount() > 0 ? 0 : 1);
            //    fwkCacheService.hPut(cacheConstant.getKeyBuyerChat(buyerChatListDto.getBuyerId()),
            //            buyerChatListDto.getId().toString(), FwkJsonUtil.toJsonString(buyerChatListDto));
            //}
        }
    }
}
