/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatController.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseBuyerController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.support.buyer.chat.mapper.BuyerChatMapper;
import com.bamboocloud.cdp.support.buyer.chat.service.BuyerChatService;
import com.bamboocloud.cdp.support.buyer.constant.BuyerRouteConstant;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatDto;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkDeleteVo;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordCreationVo;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class BuyerChatController extends BaseBuyerController {

    @Autowired
    private BuyerChatService buyerChatService;

    @Autowired
    private BuyerChatMapper buyerChatMapper;

    /**
     * 根据Id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping(BuyerRouteConstant.BUYER_CHAT_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Integer id) throws IOException {
        log.debug("BuyerChatController - delete");
        buyerChatService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 新增聊天
     *
     * @param buyerChatRecordCreationVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_CREATE_V1)
    public FwkApiResponse<BuyerChatDto> create(@Validated @RequestBody BuyerChatRecordCreationVo buyerChatRecordCreationVo) throws JsonProcessingException {
        log.debug("BuyerChatController - create");
        BuyerChat buyerChat = buyerChatService.create(buyerChatRecordCreationVo);
        return FwkApiResponse.success(buyerChatMapper.toDto(buyerChat));
    }

    /**
     * 批量删除
     *
     * @param buyerChatRecordBulkDeleteVo
     * @return
     */
    @PostMapping(BuyerRouteConstant.BUYER_CHAT_BULK_DELETE_V1)
    public FwkApiResponse<String> bulkDelete(@RequestBody BuyerChatRecordBulkDeleteVo buyerChatRecordBulkDeleteVo) throws IOException {
        log.debug("BuyerChatController - bulkDelete");
        buyerChatService.bulkDelete(buyerChatRecordBulkDeleteVo);
        return FwkApiResponse.success();
    }

    /**
     * 置顶或者取消置顶
     *
     * @param id
     * @return
     */
    @PutMapping(BuyerRouteConstant.BUYER_CHAT_UPDATE_TOP_V1)
    public FwkApiResponse<BuyerChatDto> updateTop(@PathVariable Integer id) {
        log.debug("BuyerChatController - updateTop");
        BuyerChat buyerChat = buyerChatService.get(id);
        buyerChat.setTop(!buyerChat.isTop());
        buyerChat = buyerChatService.update(buyerChat);
        return FwkApiResponse.success(buyerChatMapper.toDto(buyerChat));
    }
}
