/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatMapper.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.mapper;

import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatCreationDto;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatDto;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatListDto;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BuyerChatMapper {
    BuyerChatDto toDto(BuyerChat buyerChat);

    List<BuyerChatDto> toDtos(List<BuyerChat> buyerChats);

    BuyerChat toEntityForCreation(BuyerChatCreationDto buyerChatCreationDto);

    BuyerChatListDto toListDto(BuyerChat buyerChat);
}
