/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerChatServiceImpl.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;

import com.bamboocloud.cdp.boot.user.common.bo.buyer.LoginBuyerBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseBuyerService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.buyer.chat.mapper.BuyerChatMapper;
import com.bamboocloud.cdp.support.buyer.chat.repository.BuyerChatQueryDslRepository;
import com.bamboocloud.cdp.support.buyer.chat.repository.BuyerChatRepository;
import com.bamboocloud.cdp.support.common.dto.buyer.chat.BuyerChatListDto;
import com.bamboocloud.cdp.support.common.vo.buyer.chat.BuyerChatRecordBulkDeleteVo;
import com.bamboocloud.cdp.support.sdk.common.dto.base.chat.BaseChatRecordCreationDto;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.io.IOException;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class BuyerChatServiceImpl extends BaseBuyerService implements BuyerChatService {

    @Autowired
    private BuyerChatRepository buyerChatRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private OperationLogUtil operationLogUtil;

    @Autowired
    private BuyerChatQueryDslRepository buyerChatQueryDslRepository;

    @Autowired
    private BuyerChatMapper buyerChatMapper;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerChat create(BuyerChat buyerChat) {
        buyerChat.setCreatedUserType(UserTypeConstant.BUYER);
        buyerChat.setUpdatedUserType(UserTypeConstant.BUYER);
        buyerChat.setCreatedUserId(buyerChat.getBuyerId());
        buyerChat.setUpdatedUserId(buyerChat.getBuyerId());
        buyerChat = buyerChatRepository.saveAndFlush(buyerChat);
        entityManager.refresh(buyerChat);
        deleteNotReadCount(buyerChat.getBuyerId());
        return buyerChat;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerChat update(BuyerChat buyerChat) {
        buyerChat.setUpdatedUserType(UserTypeConstant.BUYER);
        buyerChat.setUpdatedUserId(buyerChat.getBuyerId());
        buyerChat = buyerChatRepository.saveAndFlush(buyerChat);
        entityManager.refresh(buyerChat);

        deleteNotReadCount(buyerChat.getBuyerId());
        //updateBuyerChatForRedis(buyerChat, null);
        return buyerChat;
    }

    @Override
    public void updateBuyerChatForRedis(BuyerChat buyerChat, ChatRecord chatRecord) {
        String keyBuyerChat = cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId());
        String buyerChatListDtoForRedis = (String) fwkCacheService.hGet(keyBuyerChat, buyerChat.getId().toString());
        if (FwkStringUtil.isNotBlank(buyerChatListDtoForRedis)) {
            BuyerChatListDto buyerChatListDto = FwkJsonUtil.toObject(buyerChatListDtoForRedis, BuyerChatListDto.class);
            if (!ObjectUtils.isEmpty(buyerChatListDto)) {
                buyerChatListDto.setTop(buyerChat.isTop());
                if (!ObjectUtils.isEmpty(chatRecord)) {
                    buyerChatListDto.setMessage(chatRecord.getMessage());
                    buyerChatListDto.setMessageDate(chatRecord.getSendDate());
                    buyerChatListDto.setMessageTypeCode(chatRecord.getMessageTypeCode());
                    buyerChatListDto.setSenderUserTypeCode(chatRecord.getSenderUserTypeCode());
                }
                fwkCacheService.hPut(keyBuyerChat, buyerChat.getId().toString(), FwkJsonUtil.toJsonString(buyerChatListDto));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BuyerChat create(BaseChatRecordCreationDto baseChatRecordCreationDto) throws JsonProcessingException {
        log.debug("BuyerChatServiceImpl - create");
        switch (baseChatRecordCreationDto.getSenderUserTypeCode()) {
            case UserTypeConstant.BUYER:
                log.debug(baseChatRecordCreationDto.getSenderUserTypeCode());
                // 更新聊天列表
                return updateBuyerChatBySenderUserTypeCodeIsBuyer(baseChatRecordCreationDto);
            case UserTypeConstant.VENDOR:
                log.debug(baseChatRecordCreationDto.getSenderUserTypeCode());
                // 更新聊天列表
                return updateBuyerChatBySenderUserTypeCodeIsVendor(baseChatRecordCreationDto);
            case UserTypeConstant.MAMA:
                log.debug(baseChatRecordCreationDto.getSenderUserTypeCode());
                // 更新聊天列表
                return updateBuyerChatBySenderUserTypeCodeIsMama(baseChatRecordCreationDto);
            default:
                return null;
        }
    }

    private BuyerChat updateBuyerChatBySenderUserTypeCodeIsMama(BaseChatRecordCreationDto baseChatRecordCreationDto) {
        BuyerChat buyerChat = null;
        if (baseChatRecordCreationDto.getReceiverUserTypeCode().equals(UserTypeConstant.BUYER)) {
            buyerChat =
                    buyerChatRepository.findFirstByBuyerIdAndUserTypeCode(baseChatRecordCreationDto.getReceiverUserId(),
                            baseChatRecordCreationDto.getSenderUserTypeCode());
            if (ObjectUtils.isEmpty(buyerChat)) {
                buyerChat = new BuyerChat();
                buyerChat.setBuyerId(baseChatRecordCreationDto.getReceiverUserId());
                buyerChat.setUserId(baseChatRecordCreationDto.getSenderUserId());
                buyerChat.setUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
                buyerChat.setShopId(baseChatRecordCreationDto.getShopId());
                setBuyerChatMessage(baseChatRecordCreationDto, buyerChat);
                buyerChat = create(buyerChat);
            } else {
                if (buyerChat.isDeleted()) {
                    buyerChat.setDeleted(false);
                }
                buyerChat.setUserId(baseChatRecordCreationDto.getSenderUserId());
                buyerChat.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
                buyerChat.setMessage(baseChatRecordCreationDto.getMessage());
                buyerChat.setMessageDate(LocalDateTime.now());
                buyerChat.setNotReadTotalCount(buyerChat.getNotReadTotalCount() + 1);
                buyerChat.setSendMessageUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
                buyerChat = update(buyerChat);
            }
            //saveBuyerChatListToRedis(baseChatRecordCreationDto, buyerChat);
        }
        return buyerChat;
    }

    /**
     * 删除用户未读数量缓存
     * @param buyerId
     */
    private void deleteNotReadCount(String buyerId) {
        String key = cacheConstant.getKeyBuyerChatNotReadTotalCount(buyerId);
        fwkCacheService.delete(key);
    }

    private BuyerChat updateBuyerChatBySenderUserTypeCodeIsVendor(BaseChatRecordCreationDto baseChatRecordCreationDto) {
        BuyerChat buyerChat = null;
        if (baseChatRecordCreationDto.getReceiverUserTypeCode().equals(UserTypeConstant.BUYER)) {
            buyerChat =
                    buyerChatRepository.findFirstByShopIdAndBuyerId(baseChatRecordCreationDto.getShopId(),
                            baseChatRecordCreationDto.getReceiverUserId());
            if (ObjectUtils.isEmpty(buyerChat)) {
                buyerChat = new BuyerChat();
                buyerChat.setBuyerId(baseChatRecordCreationDto.getReceiverUserId());
                buyerChat.setUserId(baseChatRecordCreationDto.getSenderUserId());
                buyerChat.setUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
                buyerChat.setShopId(baseChatRecordCreationDto.getShopId());
                setBuyerChatMessage(baseChatRecordCreationDto, buyerChat);
                buyerChat = create(buyerChat);
            } else {
                if (buyerChat.isDeleted()) {
                    buyerChat.setDeleted(false);
                }
                if (!buyerChat.getUserId().equals(baseChatRecordCreationDto.getSenderUserId())) {
                    buyerChat.setUserId(baseChatRecordCreationDto.getSenderUserId());
                }
                setBuyerChatMessage(baseChatRecordCreationDto, buyerChat);
                buyerChat = update(buyerChat);
            }
            //saveBuyerChatListToRedis(baseChatRecordCreationDto, buyerChat);
        }
        return buyerChat;
    }

    private BuyerChat updateBuyerChatBySenderUserTypeCodeIsBuyer(BaseChatRecordCreationDto baseChatRecordCreationDto) {
        BuyerChat buyerChat;
        // shopId为空，说明接受用户是Mama，用户只能和一个mama聊天，所以直接用userTypeCode查询
        buyerChat = UserTypeConstant.VENDOR.equals(baseChatRecordCreationDto.getReceiverUserTypeCode()) ?
                buyerChatRepository.findFirstByShopIdAndBuyerId(baseChatRecordCreationDto.getShopId(),
                        baseChatRecordCreationDto.getSenderUserId())
                : buyerChatRepository.findFirstByBuyerIdAndUserTypeCode(baseChatRecordCreationDto.getSenderUserId(),
                baseChatRecordCreationDto.getReceiverUserTypeCode());
        if (ObjectUtils.isEmpty(buyerChat)) {
            buyerChat = new BuyerChat();
            buyerChat.setBuyerId(baseChatRecordCreationDto.getSenderUserId());
            buyerChat.setUserId(baseChatRecordCreationDto.getReceiverUserId());
            buyerChat.setUserTypeCode(baseChatRecordCreationDto.getReceiverUserTypeCode());
            buyerChat.setShopId(FwkStringUtil.isNotBlank(baseChatRecordCreationDto.getShopId()) ?
                    baseChatRecordCreationDto.getShopId() : null);
            setBuyerChatMessage(baseChatRecordCreationDto, buyerChat);
            buyerChat = create(buyerChat);
        } else {
            if (UserTypeConstant.VENDOR.equals(baseChatRecordCreationDto.getReceiverUserTypeCode())) {
                if (buyerChat.isDeleted()) {
                    buyerChat.setDeleted(false);
                }
            } else {
                if (buyerChat.isDeleted()) {
                    buyerChat.setDeleted(false);
                }
                buyerChat.setUserId(baseChatRecordCreationDto.getReceiverUserId());
            }
            setBuyerChatMessage(baseChatRecordCreationDto, buyerChat);
            buyerChat = update(buyerChat);
        }
        //saveBuyerChatListToRedis(baseChatRecordCreationDto, buyerChat);
        return buyerChat;
    }

    private void setBuyerChatMessage(BaseChatRecordCreationDto baseChatRecordCreationDto, BuyerChat buyerChat) {
        buyerChat.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
        buyerChat.setMessage(baseChatRecordCreationDto.getMessage());
        buyerChat.setMessageDate(LocalDateTime.now());
        buyerChat.setSendMessageUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
        if (baseChatRecordCreationDto.getReceiverUserId().equals(buyerChat.getBuyerId())) {
            buyerChat.setNotReadTotalCount(buyerChat.getNotReadTotalCount() + 1);
        }
    }

    private void saveBuyerChatListToRedis(BaseChatRecordCreationDto baseChatRecordCreationDto, BuyerChat buyerChat) {
        String buyerChatListDtoForRedis = (String) fwkCacheService.hGet(cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId()),
                buyerChat.getId().toString());
        BuyerChatListDto buyerChatListDto;
        if (FwkStringUtil.isBlank(buyerChatListDtoForRedis)) {
            buyerChatListDto = getBuyerChatListDto(baseChatRecordCreationDto, buyerChat);
        } else {
            buyerChatListDto = FwkJsonUtil.toObject(buyerChatListDtoForRedis, BuyerChatListDto.class);
            if (ObjectUtils.isEmpty(buyerChatListDto)) {
                buyerChatListDto = getBuyerChatListDto(baseChatRecordCreationDto, buyerChat);
            } else {
                buyerChatListDto.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
                buyerChatListDto.setMessage(baseChatRecordCreationDto.getMessage());
                buyerChatListDto.setMessageDate(LocalDateTime.now());
                buyerChatListDto.setSenderUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
                if (baseChatRecordCreationDto.getReceiverUserId().equals(buyerChat.getBuyerId())) {
                    buyerChatListDto.setNotReadTotalCount(buyerChatListDto.getNotReadTotalCount() + 1);
                }
            }
        }
        fwkCacheService.hPut(cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId()), buyerChat.getId().toString(), FwkJsonUtil.toJsonString(buyerChatListDto));
    }

    private BuyerChatListDto getBuyerChatListDto(BaseChatRecordCreationDto baseChatRecordCreationDto, BuyerChat buyerChat) {
        BuyerChatListDto buyerChatListDto = buyerChatMapper.toListDto(buyerChat);
        buyerChatListDto.setSenderUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
        buyerChatListDto.setMessage(baseChatRecordCreationDto.getMessage());
        buyerChatListDto.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
        buyerChatListDto.setMessageDate(LocalDateTime.now());
        if (baseChatRecordCreationDto.getReceiverUserId().equals(buyerChat.getBuyerId())) {
            buyerChatListDto.setNotReadTotalCount(buyerChatListDto.getNotReadTotalCount() + 1);
        }
        return buyerChatListDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Integer id) throws IOException {
        BuyerChat buyerChat = get(id);
        operationLogUtil.setDeleteCommonInformation(buyerChat, UserTypeConstant.BUYER);
        buyerChat = update(buyerChat);
        //deleteBuyerChatForRedis(buyerChat);
    }

    private void deleteBuyerChatForRedis(BuyerChat buyerChat) {
        if (!ObjectUtils.isEmpty(buyerChat)) {
            fwkCacheService.hDelete(cacheConstant.getKeyBuyerChat(buyerChat.getBuyerId()), buyerChat.getId().toString());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bulkDelete(BuyerChatRecordBulkDeleteVo buyerChatRecordBulkDeleteVo) throws IOException {
        LoginBuyerBo loginBuyer = getLoginBuyer();
        //for (Integer id : buyerChatRecordBulkDeleteVo.getIds()) {
        //    BuyerChat buyerChat = get(id);
        //    deleteBuyerChatForRedis(buyerChat);
        //}
        buyerChatQueryDslRepository.bulkDelete(buyerChatRecordBulkDeleteVo, loginBuyer);
    }

    @Override
    public BuyerChat get(Integer id) {
        return buyerChatRepository.findById(id).orElseThrow();
    }

    @Override
    public BuyerChat getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(String buyerId, String shopId, String userId, String userTypeCode) {
        return buyerChatQueryDslRepository.getByBuyerIdAndShopIdAndUserIdAndUserTypeCode(buyerId, shopId, userId, userTypeCode);
    }
}
