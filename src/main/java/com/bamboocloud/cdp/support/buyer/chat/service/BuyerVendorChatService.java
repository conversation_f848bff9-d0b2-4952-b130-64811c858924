/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: BuyerVendorChatService.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.buyer.chat.service;

import com.bamboocloud.cdp.support.sdk.common.entity.vendor.chat.VendorChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface BuyerVendorChatService {

    /**
     * 根据shopId、vendorId、buyerId查看
     *
     * @param vendorId
     * @param shopId
     * @param userId
     * @param userTypeCode
     * @return
     */
    VendorChat getByVendorIdAndShopIdAndUserIdAndUserTypeCode(String vendorId, String shopId, String userId, String userTypeCode);

    /**
     * 更新redis
     *
     * @param vendorChat
     * @param chatRecord
     */
    void updateVendorChatForRedis(VendorChat vendorChat, ChatRecord chatRecord);

    /**
     * 修改
     *
     * @param vendorChat
     * @return
     */
    VendorChat update(VendorChat vendorChat);
}
