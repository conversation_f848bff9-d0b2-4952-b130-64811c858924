/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: Application.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support;

import com.bamboocloud.cdp.boot.security.annotation.EnableResourceServer;
import com.bamboocloud.cdp.framework.core.FwkApplication;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;

/**
 * 程序配置及启动入口
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.bamboocloud.cdp.**"})
@EnableCaching
@EnableFeignClients(basePackages = {"com.bamboocloud.cdp"})
@EnableAsync
@ComponentScan(basePackages = {
        "com.bamboocloud.cdp.framework.core",
        "com.bamboocloud.cdp.user",
        "com.bamboocloud.cdp.boot.user.common",
        "com.bamboocloud.cdp.support",
        "com.bamboocloud.cdp.market",
        "com.bamboocloud.cdp.util"
})
@EnableJpaRepositories(basePackages = {
        "com.bamboocloud.cdp.boot.user.common",
        "com.bamboocloud.cdp.framework.core",
        "com.bamboocloud.cdp.user",
        "com.bamboocloud.cdp.support",
        "com.bamboocloud.cdp.market"
})
@EntityScan(basePackages = {
        "com.bamboocloud.cdp.boot.user.common",
        "com.bamboocloud.cdp.framework.core",
        "com.bamboocloud.cdp.user",
        "com.bamboocloud.cdp.support",
        "com.bamboocloud.cdp.market"})
@EnableMethodSecurity
@EnableResourceServer
@Slf4j
public class SupportApiApplication extends FwkApplication {
    public static void main(String[] args) {
        SpringApplication.run(SupportApiApplication.class, args);
        log.info("support-api 启动完成 ... ...");
    }

}
