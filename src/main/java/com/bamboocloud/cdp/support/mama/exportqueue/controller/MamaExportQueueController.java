package com.bamboocloud.cdp.support.mama.exportqueue.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.support.common.dto.mama.exportqueue.MamaExportQueueDto;
import com.bamboocloud.cdp.support.common.dto.mama.exportqueue.MamaExportQueueTotalDto;
import com.bamboocloud.cdp.support.common.vo.mama.exportQueue.MamaExportQueueCreateVo;
import com.bamboocloud.cdp.support.common.vo.mama.exportQueue.MamaExportQueueSearchVo;
import com.bamboocloud.cdp.support.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.support.mama.exportqueue.mapper.MamaExportQueueMapper;
import com.bamboocloud.cdp.support.mama.exportqueue.service.MamaExportQueueService;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.exportqueue.ExportQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaExportQueueController extends BaseMamaController {

    @Autowired
    private MamaExportQueueService mamaExportQueueService;

    @Autowired
    private MamaExportQueueMapper mamaExportQueueMapper;

    /**
     * 创建导出队列
     *
     * @param mamaQuestionnaireCreationVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_EXPORT_QUEUE_CREATE_V1)
    public FwkApiResponse<MamaExportQueueDto> create(@Validated @RequestBody MamaExportQueueCreateVo mamaQuestionnaireCreationVo) {
        log.debug("MamaQuestionnaireController - create");
        ExportQueue exportQueue = mamaExportQueueService.create(mamaQuestionnaireCreationVo);
        //发送MQ到RUNNER  进行导出
        if (exportQueue != null) {
            mamaExportQueueService.sendMqExport(exportQueue.getId());
        }
        MamaExportQueueDto mamaExportQueueDto = mamaExportQueueMapper.toDto(exportQueue);
        return FwkApiResponse.success(mamaExportQueueDto);
    }

    /**
     * 分页查询导出队列
     *
     * @param mamaExportQueueSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).EXPORT_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_EXPORT_QUEUE_SEARCH_V1)
    public FwkApiResponse<MamaExportQueueTotalDto> search(@Validated @RequestBody MamaExportQueueSearchVo mamaExportQueueSearchVo) {
        log.debug("MamaQuestionnaireController - search");
        return FwkApiResponse.success(mamaExportQueueService.search(mamaExportQueueSearchVo));
    }

    /**
     * 删除导出队列
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).EXPORT_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_EXPORT_QUEUE_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("id") Long id) {
        log.debug("MamaQuestionnaireController - delete");
        mamaExportQueueService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 重新导出
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).EXPORT_REEXPORT.getCode())}")
    @GetMapping(MamaRouteConstant.MAMA_EXPORT_QUEUE_REPEAT_V1)
    public FwkApiResponse<String> repeat(@PathVariable("id") Long id) {
        log.debug("MamaQuestionnaireController - repeat");
        mamaExportQueueService.repeat(id);
        return FwkApiResponse.success();
    }


}
