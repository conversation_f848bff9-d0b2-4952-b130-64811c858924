/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaTicketHistoryController.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.ticket.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.support.common.vo.mama.ticket.MamaTicketHistoryCreationVo;
import com.bamboocloud.cdp.support.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.support.mama.ticket.mapper.MamaTicketHistoryMapper;
import com.bamboocloud.cdp.support.mama.ticket.service.MamaTicketHistoryService;
import com.bamboocloud.cdp.support.sdk.common.dto.mama.ticket.MamaTicketHistoryDto;
import com.bamboocloud.cdp.support.sdk.common.entity.ticket.TicketHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaTicketHistoryController extends BaseMamaController {

    @Autowired
    private MamaTicketHistoryMapper mamaTicketHistoryMapper;

    @Autowired
    private MamaTicketHistoryService mamaTicketHistoryService;

    /**
     * 新增处理记录
     *
     * @param mamaTicketHistoryCreationVo
     * @return
     */
    @PostMapping(MamaRouteConstant.MAMA_TICKET_HISTORY_CREATE_V1)
    private FwkApiResponse<MamaTicketHistoryDto> create(@Validated @RequestBody MamaTicketHistoryCreationVo mamaTicketHistoryCreationVo) {
        log.debug("MamaTicketHistoryController - create");
        TicketHistory ticketHistory = mamaTicketHistoryMapper.toEntityForCreation(mamaTicketHistoryCreationVo);
        ticketHistory = mamaTicketHistoryService.create(ticketHistory);
        return FwkApiResponse.success(mamaTicketHistoryMapper.toDto(ticketHistory));
    }
}
