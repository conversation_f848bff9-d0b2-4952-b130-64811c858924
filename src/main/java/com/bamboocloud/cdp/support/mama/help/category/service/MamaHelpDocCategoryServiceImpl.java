/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaHelpDocCategoryServiceImpl.java
 * @createdDate: 2023/05/26 10:18:26
 *
 */

package com.bamboocloud.cdp.support.mama.help.category.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.enums.ExceptionCodeEnum;
import com.bamboocloud.cdp.support.common.vo.mama.help.category.MamaHelpDocCategoryCreateTypeVo;
import com.bamboocloud.cdp.support.common.vo.mama.help.category.MamaHelpDocCategoryUpdateTypeVo;
import com.bamboocloud.cdp.support.mama.help.category.mapper.MamaHelpDocCategoryMapper;
import com.bamboocloud.cdp.support.mama.help.category.repository.MamaHelpDocCategoryRepository;
import com.bamboocloud.cdp.support.mama.help.doc.repository.MamaHelpDocRepository;
import com.bamboocloud.cdp.support.sdk.common.dto.base.help.category.HelpDocCategoryDto;
import com.bamboocloud.cdp.support.sdk.common.entity.help.HelpDoc;
import com.bamboocloud.cdp.support.sdk.common.entity.help.HelpDocCategory;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> He
 */
@Service
public class MamaHelpDocCategoryServiceImpl extends BaseMamaService implements MamaHelpDocCategoryService {
    @Autowired
    private MamaHelpDocCategoryMapper mamaHelpDocCategoryMapper;

    @Autowired
    private MamaHelpDocCategoryRepository mamaHelpDocCategoryRepository;

    @Autowired
    private MamaHelpDocRepository mamaHelpDocRepository;

    @Autowired
    private FileConstant fileConstant;

    @Autowired
    private IntegrationUtilService integrationUtilService;
    @Autowired
    private CacheConstant          cacheConstant;

    @Autowired
    private FwkAppProperty fwkAppProperty;

    @Autowired
    private OperationLogUtil operationLogUtil;

    @PersistenceContext
    private EntityManager entityManager;

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public HelpDocCategoryDto create(MamaHelpDocCategoryCreateTypeVo helpDocCategoryCreateTypeVo) {
//        if (helpDocCategoryCreateTypeVo != null && !CollectionUtils.isEmpty(helpDocCategoryCreateTypeVo.getHelpDocCategoryCreateVos())) {
//            //获取分类
//            List<Long> helpDocCategorieIds = helpDocCategoryQueryDslRepository.getByTypeCode(helpDocCategoryCreateTypeVo.getTypeCode());
//            //判断分类名称是否唯一
//            checkRepeatCategoryName(helpDocCategoryCreateTypeVo.getHelpDocCategoryCreateVos());
//            //创建要删除的分类
//            List<Long> helpDocCategoryListIds = new ArrayList<>();
//            List<MamaHelpDocCategoryCreationVo> helpDocCategoryCreateVoIdNotNulls = helpDocCategoryCreateTypeVo.getHelpDocCategoryCreateVos().stream().filter(x -> x.getId() != null).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(helpDocCategoryCreateVoIdNotNulls) && !CollectionUtils.isEmpty(helpDocCategorieIds)) {
//                //获取传过来的集合id
//                List<Long> ids = helpDocCategoryCreateVoIdNotNulls.stream().map(MamaHelpDocCategoryCreationVo::getId).collect(Collectors.toList());
//                List<Long> deleteIds = helpDocCategorieIds.stream().filter(x -> !ids.contains(x)).collect(Collectors.toList());
//                helpDocCategoryListIds.addAll(deleteIds);
//            } else if (CollectionUtils.isEmpty(helpDocCategoryCreateVoIdNotNulls) && !CollectionUtils.isEmpty(helpDocCategorieIds)) {
//                helpDocCategoryListIds.addAll(helpDocCategorieIds);
//            }
//
//            if (!CollectionUtils.isEmpty(helpDocCategorieIds)) {
//                //查找分类
//                List<HelpDocCategory> helpDocCategoryList = helpDocCategoryRepository.findAllByIdIn(helpDocCategorieIds);
//                helpDocCategoryRepository.deleteAll(helpDocCategoryList);
//            }
//            //获取OSS路径
//            List<MamaHelpDocCategoryCreationVo> helpDocCategoryCreateVoIdNulls = helpDocCategoryCreateTypeVo.getHelpDocCategoryCreateVos().stream().filter(x -> x.getId() == null).collect(Collectors.toList());
//            if (!CollectionUtils.isEmpty(helpDocCategoryCreateVoIdNulls)) {
//                for (MamaHelpDocCategoryCreationVo helpDocCategoryCreateVoIdNull : helpDocCategoryCreateVoIdNulls) {
//                    String url = uploadAttachmentToOss(helpDocCategoryCreateVoIdNull.getLogo(), null);
//                    helpDocCategoryCreateVoIdNull.setLogo(url);
//                }
//            }
//            //重新排序
//            Integer orderId = 1;
//            List<HelpDocCategory> helpDocCategoryList = mamaHelpDocCategoryMapper.toEntityList(helpDocCategoryCreateTypeVo.getHelpDocCategoryCreateVos());
//            for (HelpDocCategory helpDocCategory : helpDocCategoryList) {
//                helpDocCategory.setOrderId(orderId);
//                ++orderId;
//                helpDocCategory.setTypeCode(helpDocCategoryCreateTypeVo.getTypeCode());
//                //设置公共信息
//                operationLogUtil.setCreateCommonInformation(helpDocCategory, UserTypeConstant.MAMA);
//            }
//            //保存
//            List<HelpDocCategory> helpDocCategories = helpDocCategoryRepository.saveAll(helpDocCategoryList);
//
//            return mamaHelpDocCategoryMapper.toDtos(helpDocCategories);
//
//        } else {
//            return null;
//        }
//
//
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public HelpDocCategoryDto create(MamaHelpDocCategoryCreateTypeVo mamaHelpDocCategoryCreateTypeVo) {
        //判断分类名称是否唯一
        checkRepeatAdd(mamaHelpDocCategoryCreateTypeVo);
        HelpDocCategory helpDocCategory = mamaHelpDocCategoryMapper.toCreateEntity(mamaHelpDocCategoryCreateTypeVo);
        //获取OSS
        String url = uploadAttachmentToOss(mamaHelpDocCategoryCreateTypeVo.getLogo(), null);
        helpDocCategory.setLogo(url);
        helpDocCategory.setOrderId(0);
        //设置公共信息
        operationLogUtil.setCreateCommonInformation(helpDocCategory, UserTypeConstant.MAMA);
        mamaHelpDocCategoryRepository.saveAndFlush(helpDocCategory);
        entityManager.refresh(helpDocCategory);
        return mamaHelpDocCategoryMapper.toDto(helpDocCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MamaHelpDocCategoryUpdateTypeVo mamaHelpDocCategoryUpdateTypeVo) {
        HelpDocCategory helpDocCategory = mamaHelpDocCategoryRepository.findById(mamaHelpDocCategoryUpdateTypeVo.getId()).orElse(null);
        //判断分类名称是否唯一
        checkRepeatUpdate(mamaHelpDocCategoryUpdateTypeVo,helpDocCategory.getTypeCode());
        if (helpDocCategory != null) {
            helpDocCategory.setName(mamaHelpDocCategoryUpdateTypeVo.getName());
        }
        mamaHelpDocCategoryRepository.saveAndFlush(helpDocCategory);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(List<Long> ids) {
        //判断该分类是否已经绑定
        List<HelpDoc> helpDocs = mamaHelpDocRepository.findAllByHelpDocCategoryIdIn(ids);
        if (!CollectionUtils.isEmpty(helpDocs)) {
            //收集分类名称
            List<Long> helpCategoryIds = helpDocs.stream().map(HelpDoc::getHelpDocCategoryId).collect(Collectors.toList());
            List<HelpDocCategory> helpDocCategories = mamaHelpDocCategoryRepository.findAllByIdIn(helpCategoryIds);
            List<String> names = helpDocCategories.stream().map(HelpDocCategory::getName).collect(Collectors.toList());
            throw new BusinessException(ExceptionCodeEnum.HELP_DOC_CATEGORY_ID_ASSOCIATION, String.join(",", names) + "已关联问题，不允许删除");
        }
        List<HelpDocCategory> helpDocCategories = mamaHelpDocCategoryRepository.findAllByIdIn(ids);
        mamaHelpDocCategoryRepository.deleteAll(helpDocCategories);
    }

    @Override
    public List<HelpDocCategoryDto> search(String typeCode) {
        return mamaHelpDocCategoryMapper.toDtos(mamaHelpDocCategoryRepository.findAllByTypeCodeOrderByOrderIdAsc(typeCode));
    }

    @Override
    public void order(List<Long> ids) {
        List<HelpDocCategory> helpDocCategories = new ArrayList<>();
        //查询分类
        Integer count = 0;
        for (Long id : ids) {
            HelpDocCategory helpDocCategory = mamaHelpDocCategoryRepository.findById(id).orElse(null);
            if (helpDocCategory != null) {
                helpDocCategory.setOrderId(count);
                ++count;
                helpDocCategories.add(helpDocCategory);

            }
        }

        mamaHelpDocCategoryRepository.saveAll(helpDocCategories);

    }

    /**
     * 判断分类名称是否唯一
     */
    private void checkRepeatAdd(MamaHelpDocCategoryCreateTypeVo mamaHelpDocCategoryCreateTypeVo) {
        List<HelpDocCategory> helpDocCategories = mamaHelpDocCategoryRepository.findAllByNameAndTypeCode(mamaHelpDocCategoryCreateTypeVo.getName(), mamaHelpDocCategoryCreateTypeVo.getTypeCode());
        if (!CollectionUtils.isEmpty(helpDocCategories)) {
            throw new BusinessException(ExceptionCodeEnum.HELP_DOC_CATEGORY_TITLE_REPEAT);
        }
    }

    /**
     * 判断分类名称是否唯一
     */
    private void checkRepeatUpdate(MamaHelpDocCategoryUpdateTypeVo mamaHelpDocCategoryUpdateTypeVo,String typeCode) {
        List<HelpDocCategory> helpDocCategoryList = mamaHelpDocCategoryRepository.findAllByNameAndTypeCodeAndIdNot(mamaHelpDocCategoryUpdateTypeVo.getName(), typeCode,mamaHelpDocCategoryUpdateTypeVo.getId());
        if (!CollectionUtils.isEmpty(helpDocCategoryList)) {
            throw new BusinessException(ExceptionCodeEnum.HELP_DOC_CATEGORY_TITLE_REPEAT);
        }
    }

    /**
     * 保存附件
     *
     * @return String
     */
    private String uploadAttachmentToOss(String tempPath, String objectPath) {
        if (FwkStringUtil.isBlank(objectPath)) {
            objectPath = fileConstant.generateCommonPubModuleContentObjectNameV2("helpDocCategory");
        }
        return integrationUtilService.fileCopy(new FileParam(cacheConstant.getKeyPvtObsAuthSts(), objectPath, tempPath)).getData();

    }

}
