/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaNotificationAsyncService.java
 * @createdDate: 2023/01/10 09:48:10
 *
 */

package com.bamboocloud.cdp.support.mama.notification.service;

import com.bamboocloud.cdp.support.sdk.common.entity.ticket.Ticket;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface MamaNotificationAsyncService {
    /**
     * 保存Mama工作台店铺弹窗信息
     */
    void saveMamaTicketPopupNotification(Ticket ticket) throws InterruptedException;
}
