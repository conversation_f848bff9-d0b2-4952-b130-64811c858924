/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaTicketHistoryMapper.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.ticket.mapper;

import com.bamboocloud.cdp.support.common.vo.mama.ticket.MamaTicketHistoryCreationVo;
import com.bamboocloud.cdp.support.sdk.common.dto.mama.ticket.MamaTicketHistoryDto;
import com.bamboocloud.cdp.support.sdk.common.entity.ticket.TicketHistory;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@DecoratedWith(BaseMamaTicketHistoryMapperDecorator.class)
public interface MamaTicketHistoryMapper {
    TicketHistory toEntityForCreation(MamaTicketHistoryCreationVo mamaTicketHistoryCreationVo);

    MamaTicketHistoryDto toDto(TicketHistory ticketHistory);
}
