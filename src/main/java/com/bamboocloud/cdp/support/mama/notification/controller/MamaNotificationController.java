/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaNotificationController.java
 * @createdDate: 2023/01/09 16:46:09
 *
 */

package com.bamboocloud.cdp.support.mama.notification.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.constant.PopupNotificationConstant;
import com.bamboocloud.cdp.support.mama.chat.service.MamaChatService;
import com.bamboocloud.cdp.support.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.domain.dto.mama.notification.MamaNotificationPopupDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaNotificationController extends BaseMamaController {
    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;
    @Autowired
    private MamaChatService mamaChatService;

    /**
     * 运营端工作台的弹窗信息
     *
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_NOTIFICATION_LIST_POPUPS_V1)
    public FwkApiResponse<List<MamaNotificationPopupDto>> listPopups() {
        log.debug("MamaNotificationController - listPopups");
        String key = cacheConstant.getKeyMamaPopupNotificationPrefix()+ PopupNotificationConstant.KEY_POPUP;
        String value = fwkCacheService.get(key);
        List<MamaNotificationPopupDto> notificationPopups = new ArrayList<>();
        if (FwkStringUtil.isNotBlank(value)) {
            notificationPopups = FwkJsonUtil.toList(value, MamaNotificationPopupDto.class);
        }
        String keyMamaChatSumNotReadTotalCountPrefix = cacheConstant.getKeyMamaChatSumNotReadTotalCountPrefix("all");
        // 查询平台客服未读消息总数
        int countNotReadTotalCount = mamaChatService.countNotReadTotalCount();
        saveChatCountForRedis(notificationPopups, keyMamaChatSumNotReadTotalCountPrefix, countNotReadTotalCount);
        return FwkApiResponse.success(notificationPopups);
    }

    private void saveChatCountForRedis(List<MamaNotificationPopupDto> notificationPopups, String key, int count) {
        MamaNotificationPopupDto mamaNotificationPopupDto;
        if (fwkCacheService.hasKey(key)) {
            mamaNotificationPopupDto = FwkJsonUtil.toObject(fwkCacheService.get(key), MamaNotificationPopupDto.class);
            if (count > Integer.parseInt(mamaNotificationPopupDto.getKey())) {
                notificationPopups.add(mamaNotificationPopupDto);
            }
            mamaNotificationPopupDto.setKey(String.valueOf(count));
            mamaNotificationPopupDto.setNotificationDate(LocalDateTime.now());
        } else {
            mamaNotificationPopupDto = new MamaNotificationPopupDto(String.valueOf(count), LocalDateTime.now());
            notificationPopups.add(mamaNotificationPopupDto);
        }
        fwkCacheService.set(key, FwkJsonUtil.toJsonString(mamaNotificationPopupDto));
    }

    /**
     * 已读运营端工作台的弹窗消息
     *
     * @return
     */
    @PutMapping(MamaRouteConstant.MAMA_NOTIFICATION_BULK_READ_POPUPS_V1)
    public FwkApiResponse<String> bulkReadPopups() {
        log.debug("MamaNotificationController - bulkReadPopups");
        String key = cacheConstant.getKeyMamaPopupNotificationPrefix()+ PopupNotificationConstant.KEY_POPUP;
        if (fwkCacheService.hasKey(key)) {
            fwkCacheService.delete(key);
        }
        return FwkApiResponse.success();
    }
}
