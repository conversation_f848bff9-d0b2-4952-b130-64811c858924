package com.bamboocloud.cdp.support.mama.exportqueue.service;

import com.bamboocloud.cdp.support.common.dto.mama.exportqueue.MamaExportQueueTotalDto;
import com.bamboocloud.cdp.support.common.vo.mama.exportQueue.MamaExportQueueCreateVo;
import com.bamboocloud.cdp.support.common.vo.mama.exportQueue.MamaExportQueueSearchVo;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.exportqueue.ExportQueue;

/**
 * <AUTHOR> He
 */
public interface MamaExportQueueService {
    /**
     * 创建导出队列
     */
    ExportQueue create(MamaExportQueueCreateVo mamaExportQueueCreateVo);

    /**
     * 查询导出队列
     */
    MamaExportQueueTotalDto search(MamaExportQueueSearchVo mamaExportQueueSearchVo);

    /**
     * 删除导出队列
     */
    void delete(Long id);

    /**
     * 重新导出
     *
     * @param id
     * @return
     */
    void repeat(Long id);

    /**
     *  发送MQ到RUNNER  进行导出
     */
    void sendMqExport(Long id);
}
