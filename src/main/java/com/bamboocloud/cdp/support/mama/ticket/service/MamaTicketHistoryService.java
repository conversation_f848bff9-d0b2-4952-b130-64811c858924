/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaTicketHistoryService.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.ticket.service;

import com.bamboocloud.cdp.support.sdk.common.entity.ticket.TicketHistory;

/**
 * <AUTHOR>
 * @description:
 */
public interface MamaTicketHistoryService {

    /**
     * 根据id查看
     *
     * @param id
     * @return
     */
    TicketHistory get(Integer id);

    /**
     * 新增
     *
     * @param ticketHistory
     * @return
     */
    TicketHistory create(TicketHistory ticketHistory);

    /**
     * 根据工单Id删除
     *
     * @param ticketId
     */
    void deleteAllByTicketId(String ticketId);

}
