/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireController.java
 * @createdDate: 2023/02/28 17:37:28
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.dto.mama.questionnaire.MamaQuestionnairePageDto;
import com.bamboocloud.cdp.support.common.enums.ExceptionCodeEnum;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireCreationVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireSearchVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateStatusVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateVo;
import com.bamboocloud.cdp.support.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.support.mama.questionnaire.mapper.MamaQuestionnaireMapper;
import com.bamboocloud.cdp.support.mama.questionnaire.service.MamaQuestionnaireService;
import com.bamboocloud.cdp.support.sdk.common.constant.QuestionnaireConstant;
import com.bamboocloud.cdp.support.sdk.common.dto.base.questionnire.QuestionnaireMessagingDto;
import com.bamboocloud.cdp.support.sdk.common.dto.mama.questionnaire.MamaQuestionnaireDto;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.MessagingConstant;
import com.bamboocloud.cdp.user.sdk.constant.SystemConstant;
import com.bamboocloud.cdp.user.sdk.util.MessagingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> Shu
 * @description:
 */
@RestController
@Slf4j
public class MamaQuestionnaireController extends BaseMamaController {

    @Autowired
    private MamaQuestionnaireService mamaQuestionnaireService;

    @Autowired
    private MamaQuestionnaireMapper mamaQuestionnaireMapper;

    @Autowired
    private MessagingUtil messagingUtil;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    /**
     * 创建问卷
     *
     * @param mamaQuestionnaireCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_CREATE_V1)
    public FwkApiResponse<MamaQuestionnaireDto> create(@Validated @RequestBody MamaQuestionnaireCreationVo mamaQuestionnaireCreationVo) {
        log.debug("MamaQuestionnaireController - create");
        Questionnaire questionnaire = mamaQuestionnaireService.create(mamaQuestionnaireCreationVo);
        return FwkApiResponse.success(mamaQuestionnaireMapper.toDto(questionnaire));
    }

    /**
     * 编辑问卷
     *
     * @param mamaQuestionnaireUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_UPDATE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_UPDATE_V1)
    public FwkApiResponse<MamaQuestionnaireDto> update(@PathVariable Long id, @Validated @RequestBody MamaQuestionnaireUpdateVo mamaQuestionnaireUpdateVo) {
        log.debug("MamaQuestionnaireController - create");
        Questionnaire questionnaire = mamaQuestionnaireService.update(mamaQuestionnaireUpdateVo);
        return FwkApiResponse.success(mamaQuestionnaireMapper.toDto(questionnaire));
    }

    /**
     * 审核问卷
     *
     * @param id
     * @param mamaQuestionnaireUpdateStatusVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_APPROVE.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_APPROVE_V1)
    public FwkApiResponse<String> approve(@PathVariable Long id, @Validated @RequestBody MamaQuestionnaireUpdateStatusVo mamaQuestionnaireUpdateStatusVo) {
        log.debug("MamaQuestionnaireController - approve");
        if (FwkStringUtil.isBlank(mamaQuestionnaireUpdateStatusVo.getStatusCode())) {
            throw new BusinessException(ExceptionCodeEnum.QUESTIONNAIRE_STATUS_CODE_NOT_NULL);
        }
        Questionnaire questionnaire = mamaQuestionnaireService.get(mamaQuestionnaireUpdateStatusVo.getId());
        if (mamaQuestionnaireUpdateStatusVo.getStatusCode().equals(QuestionnaireConstant.QUESTIONNAIRE_STATUS_APPROVED.getCode())) {
            List<String> statusCodes = new ArrayList<>();
            statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_PENDING.getCode());
            statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_ONGOING.getCode());
            boolean existsByEndDateIsAfterAndStatusCodeInAndIdNot = mamaQuestionnaireService.existsByEndDateIsAfterAndStatusCodeInAndIdNot(questionnaire.getStartDate(), statusCodes, questionnaire.getId());
            if (existsByEndDateIsAfterAndStatusCodeInAndIdNot) {
                throw new BusinessException(ExceptionCodeEnum.QUESTIONNAIRE_DATE_ALREADY_SAME_TIME);
            }
        }
        questionnaire = mamaQuestionnaireMapper.toEntityForUpdate(mamaQuestionnaireUpdateStatusVo, questionnaire);
        questionnaire = mamaQuestionnaireService.update(questionnaire);
        // 审核过后若问卷调查状态为生效中时异步派发问卷调查
        if (questionnaire.getStatusCode().equals(QuestionnaireConstant.QUESTIONNAIRE_STATUS_ONGOING.getCode())) {
            QuestionnaireMessagingDto questionnaireMessagingDto = new QuestionnaireMessagingDto();
            questionnaireMessagingDto.setId(questionnaire.getId());
            questionnaireMessagingDto.setTag(MessagingConstant.SUPPORT_API_MESSAGING_TAG_CREATE_QUESTIONNAIRE_BUYER);
            messagingUtil.sendMessage(questionnaireMessagingDto, MessagingConstant.QUESTIONNAIRE_TOPIC +
                    SystemConstant.COLON + MessagingConstant.SUPPORT_API_MESSAGING_TAG_CREATE_QUESTIONNAIRE_BUYER);
        }
        return FwkApiResponse.success();
    }

    /**
     * 取消问卷
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_CANCEL.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_CANCEL_V1)
    public FwkApiResponse<String> cancel(@PathVariable Long id) {
        log.debug("MamaQuestionnaireController - cancel");
        Questionnaire questionnaire = mamaQuestionnaireService.get(id);
        if (!questionnaire.getStatusCode().equals(QuestionnaireConstant.QUESTIONNAIRE_STATUS_CANCELED.getCode())) {
            questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_CANCELED.getCode());
            questionnaire = mamaQuestionnaireService.update(questionnaire);
            // 用户的站内弹窗消息删除，不弹窗
            String key = cacheConstant.getKeyNotiPopupQuestionnaireBuyer("*") + ":" + questionnaire.getId();
            Set<String> keys = fwkCacheService.scanKeys(key);
            if (!FwkCollectionUtil.isEmpty(keys)) {
                fwkCacheService.delete(keys);
            }
        }
        return FwkApiResponse.success();
    }

    /**
     * 删除问卷
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable Long id) {
        log.debug("MamaQuestionnaireController - delete");
        mamaQuestionnaireService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 问卷列表
     *
     * @param mamaQuestionnaireSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).QUESTIONNAIRE_GET.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_SEARCH_V1)
    public FwkApiResponse<MamaQuestionnairePageDto> search(@Validated @RequestBody MamaQuestionnaireSearchVo mamaQuestionnaireSearchVo) {
        log.debug("MamaQuestionnaireController - search");
        MamaQuestionnairePageDto mamaQuestionnairePageDto = mamaQuestionnaireService.search(mamaQuestionnaireSearchVo);
        return FwkApiResponse.success(mamaQuestionnairePageDto);
    }

    /**
     * 判断问卷是否绑定等级
     */
    @GetMapping(MamaRouteConstant.MAMA_QUESTIONNAIRE_BUYER_MEMBER_MAMA_LEVEL_GET_V1)
    public FwkApiResponse<Long> getQuestionnaireBuyerMemberMamaLevel(@PathVariable(value = "levelId") Long levelId) {
        return FwkApiResponse.success(mamaQuestionnaireService.getQuestionnaireBuyerMemberMamaLevel(levelId));
    }
}
