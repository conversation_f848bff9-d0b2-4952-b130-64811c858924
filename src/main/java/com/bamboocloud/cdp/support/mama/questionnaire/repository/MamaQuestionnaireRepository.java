/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireRepository.java
 * @createdDate: 2023/02/17 16:26:17
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseRepository;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
@Repository
public interface MamaQuestionnaireRepository extends FwkBaseRepository<Questionnaire, Long> {
    Questionnaire findFirstByIdAndDeletedIsFalse(Long id);

    boolean existsByEndDateIsAfterAndStatusCodeInAndDeletedIsFalse(LocalDateTime endDate, List<String> statusCodes);

    boolean existsByEndDateIsAfterAndStatusCodeInAndIdNotAndDeletedIsFalse(LocalDateTime endDate, List<String> statusCodes, Long id);
}
