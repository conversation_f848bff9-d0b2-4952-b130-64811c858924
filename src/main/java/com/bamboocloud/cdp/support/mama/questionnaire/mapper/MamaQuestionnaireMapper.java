/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireMapper.java
 * @createdDate: 2023/02/17 16:45:17
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.mapper;

import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireCreationVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateStatusVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateVo;
import com.bamboocloud.cdp.support.sdk.common.dto.mama.questionnaire.MamaQuestionnaireDto;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR>
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface MamaQuestionnaireMapper {
    Questionnaire toEntityForCreation(MamaQuestionnaireCreationVo mamaQuestionnaireCreationVo);

    MamaQuestionnaireDto toDto(Questionnaire questionnaire);

    Questionnaire toEntityForUpdate(MamaQuestionnaireUpdateVo mamaQuestionnaireUpdateVo, @MappingTarget Questionnaire questionnaire);

    Questionnaire toEntityForUpdate(MamaQuestionnaireUpdateStatusVo mamaQuestionnaireUpdateStatusVo, @MappingTarget Questionnaire questionnaire);
}
