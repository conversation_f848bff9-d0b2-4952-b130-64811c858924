/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireServiceImpl.java
 * @createdDate: 2023/02/17 16:32:17
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.service;

import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.common.sdk.exception.BusinessException;
import com.bamboocloud.cdp.framework.core.config.property.FwkAppProperty;
import com.bamboocloud.cdp.framework.core.util.FwkCollectionUtil;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.market.sdk.common.dto.mama.member.MamaMemberMamaLevelDto;
import com.bamboocloud.cdp.market.sdk.integration.IntegrationMarketService;
import com.bamboocloud.cdp.support.common.dto.mama.questionnaire.MamaQuestionnairePageDto;
import com.bamboocloud.cdp.support.common.enums.ExceptionCodeEnum;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireCreationVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireSearchVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateVo;
import com.bamboocloud.cdp.support.mama.questionnaire.mapper.MamaQuestionnaireMapper;
import com.bamboocloud.cdp.support.mama.questionnaire.repository.MamaQuestionnaireBuyerMemberMamaLevelQueryDslRepository;
import com.bamboocloud.cdp.support.mama.questionnaire.repository.MamaQuestionnaireQueryDslRepository;
import com.bamboocloud.cdp.support.mama.questionnaire.repository.MamaQuestionnaireRepository;
import com.bamboocloud.cdp.support.sdk.common.constant.QuestionnaireConstant;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QuestionnaireBuyerMemberMamaLevel;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QuestionnaireCase;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QuestionnaireCaseAnswer;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import com.bamboocloud.cdp.user.sdk.util.IdUtil;
import com.bamboocloud.cdp.user.sdk.util.OperationLogUtil;
import com.bamboocloud.cdp.util.sdk.common.constant.FileConstant;
import com.bamboocloud.cdp.util.sdk.common.dto.file.FileParam;
import com.bamboocloud.cdp.util.sdk.integration.IntegrationUtilService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaQuestionnaireServiceImpl extends BaseMamaService implements MamaQuestionnaireService {
    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private MamaQuestionnaireRepository mamaQuestionnaireRepository;

    @Autowired
    private OperationLogUtil operationLogUtil;

    @Autowired
    private MamaQuestionnaireMapper mamaQuestionnaireMapper;

    @Autowired
    private IntegrationMarketService integrationMarketService;

    @Autowired
    private MamaQuestionnaireQueryDslRepository mamaQuestionnaireQueryDslRepository;

    @Autowired
    private IntegrationUtilService integrationUtilService;
    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private FwkAppProperty fwkAppProperty;

    @Autowired
    private MamaQuestionnaireBuyerMemberMamaLevelQueryDslRepository mamaQuestionnaireBuyerMemberMamaLevelQueryDslRepository;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Questionnaire create(Questionnaire questionnaire) {
        if (FwkStringUtil.isNotBlank(questionnaire.getBannerUrl())) {

            String bannerUrl = integrationUtilService.fileCopy(new FileParam(cacheConstant.getKeyPvtObsAuthSts(),
                    FileConstant.COMMON + FileConstant.PUBLIC_PATH + "questionnaire/" + IdUtil.generateUUID(),
                    questionnaire.getBannerUrl())).getData();

            questionnaire.setBannerUrl(bannerUrl);
        }
        questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_WAIT_REVIEW.getCode());
        operationLogUtil.setCreateCommonInformation(questionnaire, UserTypeConstant.MAMA);
        setBaseProperties(questionnaire);
        questionnaire = mamaQuestionnaireRepository.saveAndFlush(questionnaire);
        entityManager.refresh(questionnaire);
        return questionnaire;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Questionnaire create(MamaQuestionnaireCreationVo mamaQuestionnaireCreationVo) {
        //判断是否有新版本
        if (mamaQuestionnaireCreationVo != null && !CollectionUtils.isEmpty(mamaQuestionnaireCreationVo.getQuestionnaireBuyerMemberMamaLevels())) {
            for (MamaQuestionnaireCreationVo.MamaQuestionnaireBuyerMemberMamaLevelCreationVo questionnaireBuyerMemberMamaLevel : mamaQuestionnaireCreationVo.getQuestionnaireBuyerMemberMamaLevels()) {
                MamaMemberMamaLevelDto memberMamaLevel = integrationMarketService.getMemberMamaLevelById(
                        questionnaireBuyerMemberMamaLevel.getMemberMamaLevelId()).getData();
                if (memberMamaLevel != null && memberMamaLevel.getMemberMamaLevelId() != null) {
                    questionnaireBuyerMemberMamaLevel.setMemberMamaLevelId(memberMamaLevel.getMemberMamaLevelId());
                }
            }
        }
        List<String> statusCodes = new ArrayList<>();
        statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_PENDING.getCode());
        statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_ONGOING.getCode());
        boolean existsByEndDateIsAfterAndStatusCodeIn = mamaQuestionnaireRepository.existsByEndDateIsAfterAndStatusCodeInAndDeletedIsFalse(mamaQuestionnaireCreationVo.getStartDate(), statusCodes);
        if (existsByEndDateIsAfterAndStatusCodeIn) {
            throw new BusinessException(ExceptionCodeEnum.QUESTIONNAIRE_DATE_ALREADY_SAME_TIME);
        }
        Questionnaire questionnaire = mamaQuestionnaireMapper.toEntityForCreation(mamaQuestionnaireCreationVo);
        questionnaire = create(questionnaire);
        if (!ObjectUtils.isEmpty(mamaQuestionnaireCreationVo.getNotification())) {
            mamaQuestionnaireCreationVo.getNotification().setQuestionnaireId(questionnaire.getId());
            mamaQuestionnaireCreationVo.getNotification().setNotiBuyer(true);
            integrationMarketService.mamaCreateNotification(FwkJsonUtil.toJsonString(mamaQuestionnaireCreationVo.getNotification()));
        }
        return questionnaire;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Questionnaire update(Questionnaire questionnaire) {
        operationLogUtil.setUpdateCommonInformation(questionnaire, UserTypeConstant.MAMA);
        setBaseProperties(questionnaire);
        questionnaire = mamaQuestionnaireRepository.saveAndFlush(questionnaire);
        entityManager.refresh(questionnaire);
        return questionnaire;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Questionnaire update(MamaQuestionnaireUpdateVo mamaQuestionnaireUpdateVo) {
        //判断是否有新版本
        if (mamaQuestionnaireUpdateVo != null && !CollectionUtils.isEmpty(mamaQuestionnaireUpdateVo.getQuestionnaireBuyerMemberMamaLevels())) {
            for (MamaQuestionnaireUpdateVo.MamaQuestionnaireBuyerMemberMamaLevelUpdateVo questionnaireBuyerMemberMamaLevel : mamaQuestionnaireUpdateVo.getQuestionnaireBuyerMemberMamaLevels()) {
                MamaMemberMamaLevelDto memberMamaLevel = integrationMarketService.getMemberMamaLevelById(
                        questionnaireBuyerMemberMamaLevel.getMemberMamaLevelId()).getData();
                if (memberMamaLevel != null && memberMamaLevel.getMemberMamaLevelId() != null) {
                    questionnaireBuyerMemberMamaLevel.setMemberMamaLevelId(memberMamaLevel.getMemberMamaLevelId());
                }
            }
        }
        if (!ObjectUtils.isEmpty(mamaQuestionnaireUpdateVo.getStartDate())) {
            List<String> statusCodes = new ArrayList<>();
            statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_PENDING.getCode());
            statusCodes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_ONGOING.getCode());
            boolean existsByEndDateIsAfterAndStatusCodeInAndIdNot = existsByEndDateIsAfterAndStatusCodeInAndIdNot(mamaQuestionnaireUpdateVo.getStartDate(), statusCodes, mamaQuestionnaireUpdateVo.getId());
            if (existsByEndDateIsAfterAndStatusCodeInAndIdNot) {
                throw new BusinessException(ExceptionCodeEnum.QUESTIONNAIRE_DATE_ALREADY_SAME_TIME);
            }
        }
        Questionnaire questionnaire = get(mamaQuestionnaireUpdateVo.getId());
        if (FwkStringUtil.isNotBlank(mamaQuestionnaireUpdateVo.getBannerUrl())) {
            String bannerUrl = integrationUtilService.fileCopy(new FileParam(cacheConstant.getKeyPvtObsAuthSts(),
                    FwkStringUtil.isNotBlank(questionnaire.getBannerUrl()) ? questionnaire.getBannerUrl()
                            : FileConstant.COMMON + FileConstant.PUBLIC_PATH + "questionnaire/" + IdUtil.generateUUID(),
                    mamaQuestionnaireUpdateVo.getBannerUrl())).getData();

            mamaQuestionnaireUpdateVo.setBannerUrl(bannerUrl);
        }
        questionnaire = mamaQuestionnaireMapper.toEntityForUpdate(mamaQuestionnaireUpdateVo, questionnaire);
        questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_WAIT_REVIEW.getCode());
        questionnaire = update(questionnaire);
        if (!ObjectUtils.isEmpty(mamaQuestionnaireUpdateVo.getNotification())) {
            mamaQuestionnaireUpdateVo.getNotification().setQuestionnaireId(questionnaire.getId());
            integrationMarketService.mamaCreateNotification(FwkJsonUtil.toJsonString(mamaQuestionnaireUpdateVo.getNotification()));
        }
        return questionnaire;
    }

    private void setBaseProperties(Questionnaire questionnaire) {
        if (questionnaire.getStatusCode().equals(QuestionnaireConstant.QUESTIONNAIRE_STATUS_APPROVED.getCode())) {
            if (LocalDateTime.now().isBefore(questionnaire.getStartDate())) {
                questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_PENDING.getCode());
            } else if (LocalDateTime.now().isAfter(questionnaire.getStartDate())) {
                questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_ONGOING.getCode());
            }
            if (LocalDateTime.now().isAfter(questionnaire.getEndDate())) {
                questionnaire.setStatusCode(QuestionnaireConstant.QUESTIONNAIRE_STATUS_EXPIRED.getCode());
            }
        }
        if (!FwkCollectionUtil.isEmpty(questionnaire.getQuestionnaireCases())) {
            for (QuestionnaireCase questionnaireCase : questionnaire.getQuestionnaireCases()) {
                questionnaireCase.setOrderId(questionnaire.getQuestionnaireCases().indexOf(questionnaireCase) + 1);
                if (!FwkCollectionUtil.isEmpty(questionnaireCase.getQuestionnaireCaseAnswers())) {
                    for (QuestionnaireCaseAnswer questionnaireCaseAnswer : questionnaireCase.getQuestionnaireCaseAnswers()) {
                        questionnaireCaseAnswer.setOrderId(questionnaireCase.getQuestionnaireCaseAnswers().indexOf(questionnaireCaseAnswer) + 1);
                        questionnaireCaseAnswer.setQuestionnaireCase(questionnaireCase);
                    }
                }
                questionnaireCase.setQuestionnaire(questionnaire);
            }
        }
        if (!FwkCollectionUtil.isEmpty(questionnaire.getQuestionnaireBuyerMemberMamaLevels())) {
            for (QuestionnaireBuyerMemberMamaLevel questionnaireBuyerMemberMamaLevel : questionnaire.getQuestionnaireBuyerMemberMamaLevels()) {
                questionnaireBuyerMemberMamaLevel.setQuestionnaire(questionnaire);
            }
        }
    }

    @Override
    public Questionnaire get(Long id) {
        Questionnaire questionnaire = mamaQuestionnaireRepository.findFirstByIdAndDeletedIsFalse(id);
        if (ObjectUtils.isEmpty(questionnaire)) {
            throw new BusinessException(ExceptionCodeEnum.QUESTIONNAIRE_NOT_FOUND);
        }
        return questionnaire;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        Questionnaire questionnaire = get(id);
        questionnaire.setDeleted(true);
        operationLogUtil.setDeleteCommonInformation(questionnaire, UserTypeConstant.MAMA);
        update(questionnaire);
    }

    @Override
    public MamaQuestionnairePageDto search(MamaQuestionnaireSearchVo mamaQuestionnaireSearchVo) {
        return mamaQuestionnaireQueryDslRepository.search(mamaQuestionnaireSearchVo);
    }

    @Override
    public boolean existsByEndDateIsAfterAndStatusCodeInAndIdNot(LocalDateTime endDate, List<String> statusCodes, Long id) {
        return mamaQuestionnaireRepository.existsByEndDateIsAfterAndStatusCodeInAndIdNotAndDeletedIsFalse(endDate, statusCodes, id);
    }

    @Override
    public Long getQuestionnaireBuyerMemberMamaLevel(Long id) {
        //判断问卷是否绑定等级
        List<String> codes = new ArrayList<>();
        codes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_EXPIRED.getCode());
        codes.add(QuestionnaireConstant.QUESTIONNAIRE_STATUS_CANCELED.getCode());
        List<Questionnaire> questionnaires = mamaQuestionnaireBuyerMemberMamaLevelQueryDslRepository.getQuestionnaireIds(id, codes);
        if (!CollectionUtils.isEmpty(questionnaires)) {
            List<String> names = questionnaires.stream().map(Questionnaire::getName).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
            throw new BusinessException(ExceptionCodeEnum.MAMA_MEMBER_LEVEL_IS_USE_QUESTIONNAIRE, String.join(",", names + "," + "这些问卷已绑定该等级且未失效,不能删除"));
        } else {
            return id;
        }
    }

}
