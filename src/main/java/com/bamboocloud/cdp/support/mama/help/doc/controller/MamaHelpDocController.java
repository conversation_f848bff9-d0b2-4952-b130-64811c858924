/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaHelpDocController.java
 * @createdDate: 2023/05/06 17:55:06
 *
 */

package com.bamboocloud.cdp.support.mama.help.doc.controller;

import com.bamboocloud.cdp.boot.user.common.controller.BaseMamaController;
import com.bamboocloud.cdp.framework.core.domain.model.dto.FwkApiResponse;
import com.bamboocloud.cdp.support.common.dto.base.help.doc.HelpDocPageDto;
import com.bamboocloud.cdp.support.common.vo.mama.help.doc.MamaHelpDocCreationVo;
import com.bamboocloud.cdp.support.common.vo.mama.help.doc.MamaHelpDocSearchVo;
import com.bamboocloud.cdp.support.common.vo.mama.help.doc.MamaHelpDocUpdateVo;
import com.bamboocloud.cdp.support.mama.constant.MamaRouteConstant;
import com.bamboocloud.cdp.support.mama.help.doc.service.MamaHelpDocService;
import com.bamboocloud.cdp.support.sdk.common.dto.base.help.category.HelpDocCategoryDto;
import com.bamboocloud.cdp.support.sdk.common.dto.base.help.doc.HelpDocDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> He
 */
@RestController
@Slf4j
public class MamaHelpDocController extends BaseMamaController {

    @Autowired
    private MamaHelpDocService mamaHelpDocService;

    /**
     * 创建问题
     *
     * @param mamaHelpDocCreationVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).BUYER_HELP_DOC_EDIT.getCode()) ||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).VENDOR_HELP_DOC_CREATE.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_HELP_DOC_CREATE_V1)
    public FwkApiResponse<HelpDocDto> create(@RequestBody @Validated MamaHelpDocCreationVo mamaHelpDocCreationVo) {
        return FwkApiResponse.success(mamaHelpDocService.create(mamaHelpDocCreationVo));
    }

    /**
     * 删除问题
     *
     * @param id
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).BUYER_HELP_DOC_DELETE.getCode()) ||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).VENDOR_HELP_DOC_DELETE.getCode())}")
    @DeleteMapping(MamaRouteConstant.MAMA_HELP_DOC_DELETE_V1)
    public FwkApiResponse<String> delete(@PathVariable("id") Long id) {
        mamaHelpDocService.delete(id);
        return FwkApiResponse.success();
    }

    /**
     * 查询分类
     *
     * @param typeCode
     * @return
     */
    @GetMapping(MamaRouteConstant.MAMA_HELP_DOC_SEARCH_CATEGORY_V1)
    public FwkApiResponse<List<HelpDocCategoryDto>> listCategoriesByTypeCode(@PathVariable("typeCode") String typeCode) {
        return FwkApiResponse.success(mamaHelpDocService.listCategoriesByTypeCode(typeCode));
    }

    /**
     * 查询问题
     *
     * @param mamaHelpDocSearchVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).BUYER_HELP_DOC_SEARCH.getCode()) ||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).VENDOR_HELP_DOC_SEARCH.getCode())}")
    @PostMapping(MamaRouteConstant.MAMA_HELP_DOC_SEARCH_V1)
    public FwkApiResponse<HelpDocPageDto> search(@RequestBody @Validated MamaHelpDocSearchVo mamaHelpDocSearchVo) {
        return FwkApiResponse.success(mamaHelpDocService.search(mamaHelpDocSearchVo));
    }

    /**
     * 更新问题
     *
     * @param mamaHelpDocUpdateVo
     * @return
     */
    @PreAuthorize("{authentication.principal.username == " +
            "T(com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant).SUPER_ADMIN_NAME || " +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).BUYER_HELP_DOC_EDIT.getCode()) ||" +
            "hasAuthority(T(com.bamboocloud.cdp.user.sdk.constant.MamaPermissionConst).BUYER_HELP_DOC_EDIT.getCode())}")
    @PutMapping(MamaRouteConstant.MAMA_HELP_DOC_UPDATE_V1)
    public FwkApiResponse<String> update(@RequestBody @Validated MamaHelpDocUpdateVo mamaHelpDocUpdateVo) {
        mamaHelpDocService.update(mamaHelpDocUpdateVo);
        return FwkApiResponse.success();
    }

}
