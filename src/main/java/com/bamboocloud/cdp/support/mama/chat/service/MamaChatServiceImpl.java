/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaChatServiceImpl.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.chat.service;

import com.aliyuncs.exceptions.ClientException;
import com.bamboocloud.cdp.boot.user.common.bo.mama.LoginMamaBo;
import com.bamboocloud.cdp.boot.user.common.service.BaseMamaService;
import com.bamboocloud.cdp.framework.core.common.service.cache.FwkCacheService;
import com.bamboocloud.cdp.framework.core.util.FwkJsonUtil;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.dto.mama.chat.MamaChatDto;
import com.bamboocloud.cdp.support.common.vo.mama.chat.MamaChatChangeVo;
import com.bamboocloud.cdp.support.config.socket.WebSocketServer;
import com.bamboocloud.cdp.support.mama.chat.repository.MamaChatQueryDslRepository;
import com.bamboocloud.cdp.support.mama.chat.repository.MamaChatRepository;
import com.bamboocloud.cdp.support.mama.mapper.MamaChatMapper;
import com.bamboocloud.cdp.support.sdk.common.constant.ChatRecordConstant;
import com.bamboocloud.cdp.support.sdk.common.dto.base.chat.BaseChatRecordCreationDto;
import com.bamboocloud.cdp.support.sdk.common.dto.mama.chat.MamaChatChangeMamaDto;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.chat.MamaChat;
import com.bamboocloud.cdp.support.sdk.common.entity.vendor.chat.VendorChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;
import com.bamboocloud.cdp.support.sdk.common.mongo.mapper.ChatRecordMapper;
import com.bamboocloud.cdp.support.sdk.common.mongo.service.ChatRecordService;
import com.bamboocloud.cdp.user.sdk.constant.CacheConstant;
import com.bamboocloud.cdp.user.sdk.constant.UserTypeConstant;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import java.io.IOException;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Service
@Slf4j
public class MamaChatServiceImpl extends BaseMamaService implements MamaChatService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private MamaChatRepository mamaChatRepository;

    @Autowired
    private ChatRecordService chatRecordService;

    @Autowired
    private ChatRecordMapper chatRecordMapper;

    @Autowired
    private MamaVendorChatService mamaVendorChatService;

    @Autowired
    private MamaBuyerChatService mamaBuyerChatService;

    @Autowired
    private MamaChatMapper mamaChatMapper;

    @Autowired
    private FwkCacheService fwkCacheService;

    @Autowired
    private CacheConstant cacheConstant;

    @Autowired
    private MamaChatQueryDslRepository mamaChatQueryDslRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaChat create(MamaChat mamaChat) {
        mamaChat.setCreatedUserType(UserTypeConstant.MAMA);
        mamaChat.setUpdatedUserType(UserTypeConstant.MAMA);
        mamaChat.setCreatedUserId(mamaChat.getMamaId());
        mamaChat.setUpdatedUserId(mamaChat.getMamaId());
        mamaChat = mamaChatRepository.saveAndFlush(mamaChat);
        entityManager.refresh(mamaChat);
        return mamaChat;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaChat update(MamaChat mamaChat) {
        mamaChat.setUpdatedUserType(UserTypeConstant.MAMA);
        mamaChat.setUpdatedUserId(mamaChat.getMamaId());
        mamaChat = mamaChatRepository.saveAndFlush(mamaChat);
        entityManager.refresh(mamaChat);
        return mamaChat;
    }

    @Override
    public MamaChat get(Integer id) {
        return mamaChatRepository.findById(id).orElseThrow();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(Integer id) {
        MamaChat mamaChat = get(id);
        mamaChat.setChatFinished(true);
        mamaChat.setNotReadTotalCount(0);
        update(mamaChat);
    }

    @Override
    public MamaChat getByMamaIdAndShopIdAndUserIdAndUserTypeCode(String mamaId, String shopId, String userId, String userTypeCode) {
        return mamaChatQueryDslRepository.getByMamaIdAndShopIdAndUserIdAndUserTypeCode(mamaId, shopId, userId, userTypeCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeMama(MamaChatChangeVo mamaChatChangeVo) throws ClientException, IOException, IllegalAccessException {
        LoginMamaBo loginMama = getLoginMama();
        MamaChatChangeMamaDto mamaChatChangeMamaDto = new MamaChatChangeMamaDto();
        mamaChatChangeMamaDto.setShopId(mamaChatChangeVo.getShopId());
        mamaChatChangeMamaDto.setUserTypeCode(mamaChatChangeVo.getUserTypeCode());
        mamaChatChangeMamaDto.setUserId(mamaChatChangeMamaDto.getUserId());
        mamaChatChangeMamaDto.setOldMamaId(loginMama.getId());
        mamaChatChangeMamaDto.setNewMamaId(mamaChatChangeVo.getMamaId());
        //转接消息存聊天记录
        BaseChatRecordCreationDto baseChatRecordCreationDto = getBaseChatRecordCreationDto(mamaChatChangeVo, mamaChatChangeMamaDto);
        ChatRecord chatRecord = chatRecordMapper.toEntityForCreation(baseChatRecordCreationDto);
        chatRecord = chatRecordService.create(chatRecord);
        MamaChat mamaChat = mamaChatChangeVo.getUserTypeCode().equals(UserTypeConstant.VENDOR) ?
                mamaChatQueryDslRepository.getByMamaIdAndShopIdAndUserIdAndUserTypeCode(loginMama.getId(),
                        mamaChatChangeVo.getShopId(), mamaChatChangeVo.getUserId(),
                        UserTypeConstant.VENDOR) : mamaChatRepository.findFirstByMamaIdAndUserIdAndUserTypeCode(
                loginMama.getId(), mamaChatChangeVo.getUserId(), UserTypeConstant.BUYER);
        if (!ObjectUtils.isEmpty(mamaChat)) {
            finish(mamaChat.getId());
        }
        // 为转接人创建聊天列表
        createMamaChat(mamaChatChangeVo, baseChatRecordCreationDto);
        // 修改客户端端与平台聊天的客服
        updateMiniUserChat(mamaChatChangeVo, loginMama, chatRecord);
        try {
            WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(chatRecord),
                    mamaChatChangeVo.getUserId());
            WebSocketServer.sendInfo(FwkJsonUtil.toJsonString(chatRecord),
                    mamaChatChangeVo.getMamaId());
        } catch (IOException e) {
            log.warn("", e);
            log.warn("用户" + mamaChatChangeVo.getUserId() + ",不在线！");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MamaChat create(BaseChatRecordCreationDto baseChatRecordCreationDto) {
        log.debug("MamaChatServiceImpl - create");
        MamaChat mamaChat = null;
        if (UserTypeConstant.MAMA.equals(baseChatRecordCreationDto.getReceiverUserTypeCode())) {
            log.debug(baseChatRecordCreationDto.getReceiverUserTypeCode());
            mamaChat = UserTypeConstant.VENDOR.equals(baseChatRecordCreationDto.getSenderUserTypeCode()) ?
                    mamaChatRepository.findFirstByMamaIdAndUserIdAndShopId(baseChatRecordCreationDto.getReceiverUserId(),
                            baseChatRecordCreationDto.getSenderUserId(), baseChatRecordCreationDto.getShopId()) :
                    mamaChatRepository.findFirstByMamaIdAndUserId(baseChatRecordCreationDto.getReceiverUserId(),
                            baseChatRecordCreationDto.getSenderUserId());
            if (ObjectUtils.isEmpty(mamaChat)) {
                mamaChat = new MamaChat();
                mamaChat.setMamaId(baseChatRecordCreationDto.getReceiverUserId());
                mamaChat.setUserId(baseChatRecordCreationDto.getSenderUserId());
                mamaChat.setUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
                if (baseChatRecordCreationDto.getSenderUserTypeCode().equals(UserTypeConstant.VENDOR)) {
                    mamaChat.setShopId(baseChatRecordCreationDto.getShopId());
                }
                setMamaChatMessage(baseChatRecordCreationDto, mamaChat);
                mamaChat = create(mamaChat);
            } else {
                mamaChat.setDeleted(false);
                mamaChat.setChatFinished(false);
                setMamaChatMessage(baseChatRecordCreationDto, mamaChat);
                mamaChat = update(mamaChat);
            }
        }
        if (UserTypeConstant.MAMA.equals(baseChatRecordCreationDto.getSenderUserTypeCode())) {
            mamaChat = UserTypeConstant.VENDOR.equals(baseChatRecordCreationDto.getReceiverUserTypeCode()) ?
                    mamaChatRepository.findFirstByMamaIdAndUserIdAndShopId(baseChatRecordCreationDto.getSenderUserId(),
                            baseChatRecordCreationDto.getReceiverUserId(), baseChatRecordCreationDto.getShopId()) :
                    mamaChatRepository.findFirstByMamaIdAndUserId(baseChatRecordCreationDto.getSenderUserId(),
                            baseChatRecordCreationDto.getReceiverUserId());
            mamaChat.setDeleted(false);
            mamaChat.setChatFinished(false);
            setMamaChatMessage(baseChatRecordCreationDto, mamaChat);
            mamaChat = update(mamaChat);
        }
        if (!ObjectUtils.isEmpty(mamaChat)) {
            // 将非当前mama的平台与buyer或shop的聊天全部修改为关闭
            mamaChatQueryDslRepository.updateChatFinishedByIdNotAndUserTypeCodeAndUserIdAndShopId(true, mamaChat.getId(), mamaChat.getUserTypeCode(),
                    mamaChat.getUserTypeCode().equals(UserTypeConstant.BUYER) ? mamaChat.getUserId() : null, mamaChat.getUserTypeCode().equals(UserTypeConstant.VENDOR) ? mamaChat.getShopId() : null);
        }
        return mamaChat;
    }

    private void setMamaChatMessage(BaseChatRecordCreationDto baseChatRecordCreationDto, MamaChat mamaChat) {
        mamaChat.setMessageTypeCode(baseChatRecordCreationDto.getMessageTypeCode());
        mamaChat.setMessage(baseChatRecordCreationDto.getMessage());
        mamaChat.setMessageDate(LocalDateTime.now());
        mamaChat.setSendMessageUserTypeCode(baseChatRecordCreationDto.getSenderUserTypeCode());
        if (baseChatRecordCreationDto.getReceiverUserId().equals(mamaChat.getMamaId())) {
            mamaChat.setNotReadTotalCount(mamaChat.getNotReadTotalCount() + 1);
        }
    }

    @Override
    public void updateMamaChatForRedis(BaseChatRecordCreationDto baseChatRecordCreationDto, MamaChat mamaChat) {
        MamaChatDto mamaChatDto = mamaChatMapper.toDto(mamaChat, baseChatRecordCreationDto);
        if (!ObjectUtils.isEmpty(mamaChatDto)) {
            fwkCacheService.hPut(cacheConstant.getKeyMamaChat(mamaChatDto.getUserTypeCode(), mamaChatDto.getMamaId()),
                    mamaChatDto.getId().toString(), FwkJsonUtil.toJsonString(mamaChatDto));
        }
    }

    @Override
    public int countNotReadTotalCount() {
        Integer sumNotReadTotalCount = mamaChatQueryDslRepository.sumNotReadTotalCount(null);
        return ObjectUtils.isEmpty(sumNotReadTotalCount) ? 0 : sumNotReadTotalCount;
    }

    private BaseChatRecordCreationDto getBaseChatRecordCreationDto(MamaChatChangeVo mamaChatChangeVo, MamaChatChangeMamaDto mamaChatChangeMamaDto) {
        BaseChatRecordCreationDto baseChatRecordCreationDto = new BaseChatRecordCreationDto();
        baseChatRecordCreationDto.setShopId(FwkStringUtil.isNotBlank(mamaChatChangeVo.getShopId()) ?
                mamaChatChangeVo.getShopId() : ChatRecordConstant.SHOP_ID);
        baseChatRecordCreationDto.setMessageTypeCode(ChatRecordConstant.CHAT_RECORD_MESSAGE_TYPE_CHANGE_MAMA);
        baseChatRecordCreationDto.setMessage(FwkJsonUtil.toJsonString(mamaChatChangeMamaDto));
        baseChatRecordCreationDto.setReceiverUserId(mamaChatChangeVo.getUserId());
        baseChatRecordCreationDto.setReceiverUserTypeCode(mamaChatChangeVo.getUserTypeCode());
        baseChatRecordCreationDto.setSenderUserId(mamaChatChangeVo.getMamaId());
        baseChatRecordCreationDto.setSenderUserTypeCode(UserTypeConstant.MAMA);
        return baseChatRecordCreationDto;
    }

    private void createMamaChat(MamaChatChangeVo mamaChatChangeVo, BaseChatRecordCreationDto baseChatRecordCreationDto) {
        MamaChat mamaChat = mamaChatChangeVo.getUserTypeCode().equals(UserTypeConstant.VENDOR) ?
                mamaChatQueryDslRepository.getByMamaIdAndShopIdAndUserIdAndUserTypeCode(mamaChatChangeVo.getMamaId(),
                        mamaChatChangeVo.getShopId(), mamaChatChangeVo.getUserId(),
                        UserTypeConstant.VENDOR) : mamaChatRepository.findFirstByMamaIdAndUserIdAndUserTypeCode(
                mamaChatChangeVo.getMamaId(), mamaChatChangeVo.getUserId(), UserTypeConstant.BUYER);
        if (ObjectUtils.isEmpty(mamaChat)) {
            mamaChat = new MamaChat();
            mamaChat.setShopId(mamaChatChangeVo.getShopId());
            mamaChat.setUserId(mamaChatChangeVo.getUserId());
            mamaChat.setMamaId(mamaChatChangeVo.getMamaId());
            mamaChat.setUserTypeCode(mamaChatChangeVo.getUserTypeCode());
            setMamaChatMessage(baseChatRecordCreationDto, mamaChat);
            create(mamaChat);
        } else {
            mamaChat.setChatFinished(false);
            if (mamaChat.isDeleted()) {
                mamaChat.setDeleted(false);
            }
            setMamaChatMessage(baseChatRecordCreationDto, mamaChat);
            update(mamaChat);
        }
    }

    private void updateMiniUserChat(MamaChatChangeVo mamaChatChangeVo, LoginMamaBo loginMama, ChatRecord chatRecord) {
        if (mamaChatChangeVo.getUserTypeCode().equals(UserTypeConstant.VENDOR)) {
            VendorChat vendorChat = mamaVendorChatService.getByShopIdAndVendorIdAndUserTypeCode(mamaChatChangeVo.getShopId(),
                    mamaChatChangeVo.getUserId(), loginMama.getId(), UserTypeConstant.MAMA);
            if (!ObjectUtils.isEmpty(vendorChat)) {
                vendorChat.setUserId(mamaChatChangeVo.getMamaId());
                if (!ObjectUtils.isEmpty(chatRecord)) {
                    vendorChat.setMessage(chatRecord.getMessage());
                    vendorChat.setMessageDate(chatRecord.getSendDate());
                    vendorChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
                }
                mamaVendorChatService.update(vendorChat);
            }
        } else {
            BuyerChat buyerChat = mamaBuyerChatService.getByBuyerIdAndUserIdAndUserTypeCode(mamaChatChangeVo.getUserId(),
                    loginMama.getId(), UserTypeConstant.MAMA);
            if (!ObjectUtils.isEmpty(buyerChat)) {
                buyerChat.setUserId(mamaChatChangeVo.getMamaId());
                if (!ObjectUtils.isEmpty(chatRecord)) {
                    buyerChat.setMessage(chatRecord.getMessage());
                    buyerChat.setMessageDate(chatRecord.getSendDate());
                    buyerChat.setMessageTypeCode(chatRecord.getMessageTypeCode());
                    buyerChat.setSendMessageUserTypeCode(chatRecord.getSenderUserTypeCode());
                }
                mamaBuyerChatService.update(buyerChat);
            }
        }
    }
}
