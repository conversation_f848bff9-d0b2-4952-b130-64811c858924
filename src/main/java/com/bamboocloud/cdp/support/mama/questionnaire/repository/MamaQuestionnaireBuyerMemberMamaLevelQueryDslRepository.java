package com.bamboocloud.cdp.support.mama.questionnaire.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QQuestionnaire;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QQuestionnaireBuyerMemberMamaLevel;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> He
 */
@Component
public class MamaQuestionnaireBuyerMemberMamaLevelQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public List<Questionnaire> getQuestionnaireIds(Long levelId, List<String> codes) {
        QQuestionnaireBuyerMemberMamaLevel qQuestionnaireBuyerMemberMamaLevel = QQuestionnaireBuyerMemberMamaLevel.questionnaireBuyerMemberMamaLevel;
        QQuestionnaire questionnaire = QQuestionnaire.questionnaire;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qQuestionnaireBuyerMemberMamaLevel.memberMamaLevelId.eq(levelId));
        builder.and(questionnaire.statusCode.notIn(codes));
        builder.and(questionnaire.deleted.isFalse());
        return queryFactory.select(questionnaire).from(questionnaire).join(qQuestionnaireBuyerMemberMamaLevel).on(questionnaire.id.eq(qQuestionnaireBuyerMemberMamaLevel.questionnaire.id))
                .where(builder).fetch();
    }
}
