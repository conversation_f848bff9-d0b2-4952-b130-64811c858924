package com.bamboocloud.cdp.support.mama.exportqueue.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.dto.mama.exportqueue.MamaExportQueueDto;
import com.bamboocloud.cdp.support.common.dto.mama.exportqueue.MamaExportQueueTotalDto;
import com.bamboocloud.cdp.support.common.vo.mama.exportQueue.MamaExportQueueSearchVo;
import com.bamboocloud.cdp.support.sdk.common.entity.mama.exportqueue.QExportQueue;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> He
 */
@Component
public class MamaExportQueueQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public MamaExportQueueTotalDto search(MamaExportQueueSearchVo mamaExportQueueSearchVo) {
        MamaExportQueueTotalDto mamaExportQueueTotalDto = new MamaExportQueueTotalDto();
        BooleanBuilder builder = new BooleanBuilder();
        QExportQueue qExportQueue = QExportQueue.exportQueue;
        if (FwkStringUtil.isNotBlank(mamaExportQueueSearchVo.getName())) {
            builder.and(qExportQueue.name.contains(mamaExportQueueSearchVo.getName()));
        }
        if (FwkStringUtil.isNotBlank(mamaExportQueueSearchVo.getStatusCode())) {
            builder.and(qExportQueue.statusCode.eq(mamaExportQueueSearchVo.getStatusCode()));
        }

        JPAQuery<MamaExportQueueDto> jpaQuery = queryFactory.select(Projections.constructor(MamaExportQueueDto.class,
                qExportQueue.id,
                qExportQueue.name,
                qExportQueue.moduleCode,
                qExportQueue.createdDate,
                qExportQueue.completedDate,
                qExportQueue.statusCode,
                qExportQueue.fileUrl)).from(qExportQueue).where(builder).orderBy(qExportQueue.createdDate.desc());
        mamaExportQueueTotalDto.setTotalCount(jpaQuery.fetchCount());
        if (!ObjectUtils.isEmpty(mamaExportQueueSearchVo.getLimit()) && !ObjectUtils.isEmpty(mamaExportQueueSearchVo.getLimit())) {
            jpaQuery.offset((long) mamaExportQueueSearchVo.getOffset() * mamaExportQueueSearchVo.getLimit()).limit(mamaExportQueueSearchVo.getLimit());
        }
        mamaExportQueueTotalDto.setMamaExportQueueDtos(jpaQuery.fetch());
        return mamaExportQueueTotalDto;
    }
}
