/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaBuyerChatService.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.chat.service;

import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.BuyerChat;
import com.bamboocloud.cdp.support.sdk.common.mongo.entity.ChatRecord;

/**
 * <AUTHOR> Shu
 * @description:
 */
public interface MamaBuyerChatService {

    /**
     * 修改
     *
     * @param buyerChat
     * @return
     */
    BuyerChat update(BuyerChat buyerChat);

    /**
     * 查看
     *
     * @param buyerId
     * @param userId
     * @param userTypeCode
     * @return
     */
    BuyerChat getByBuyerIdAndUserIdAndUserTypeCode(String buyerId, String userId, String userTypeCode);

    /**
     * 更新redis
     *
     * @param buyerChat
     * @param chatRecord
     */
    void updateBuyerChatForRedis(BuyerChat buyerChat, ChatRecord chatRecord);

    /**
     * 查询是否存在商家未复消息
     *
     * @return
     */
    int countBuyerChatsByVendorNotReply();
}
