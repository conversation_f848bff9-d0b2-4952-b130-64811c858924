/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-general-api
 * @file: MamaBuyerChatQueryDslRepository.java
 * @createdDate: 2022/07/26 16:21:26
 *
 */

package com.bamboocloud.cdp.support.mama.chat.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.support.sdk.common.entity.buyer.chat.QBuyerChat;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Component
public class MamaBuyerChatQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public List<String> listBuyerIdsByUserTypeCode(String userTypeCode) {
        QBuyerChat qBuyerChat = QBuyerChat.buyerChat;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qBuyerChat.deleted.eq(false));
        builder.and(qBuyerChat.userTypeCode.eq(userTypeCode));
        return queryFactory.select(qBuyerChat.buyerId).distinct().from(qBuyerChat).where(builder).fetch();
    }
}
