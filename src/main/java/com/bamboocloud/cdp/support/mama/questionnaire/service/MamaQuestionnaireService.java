/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireService.java
 * @createdDate: 2023/02/17 16:28:17
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.service;

import com.bamboocloud.cdp.support.common.dto.mama.questionnaire.MamaQuestionnairePageDto;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireCreationVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireSearchVo;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireUpdateVo;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.Questionnaire;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 */
public interface MamaQuestionnaireService {
    /**
     * 新增
     *
     * @param questionnaire
     * @return
     */
    Questionnaire create(Questionnaire questionnaire);

    /**
     * 根据Vo创建
     *
     * @param mamaQuestionnaireCreationVo
     * @return
     */
    Questionnaire create(MamaQuestionnaireCreationVo mamaQuestionnaireCreationVo);

    /**
     * 修改
     *
     * @param questionnaire
     * @return
     */
    Questionnaire update(Questionnaire questionnaire);

    /**
     * 根据Vo修改
     *
     * @param mamaQuestionnaireUpdateVo
     * @return
     */
    Questionnaire update(MamaQuestionnaireUpdateVo mamaQuestionnaireUpdateVo);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    Questionnaire get(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 问卷列表
     *
     * @param mamaQuestionnaireSearchVo
     * @return
     */
    MamaQuestionnairePageDto search(MamaQuestionnaireSearchVo mamaQuestionnaireSearchVo);

    /**
     * 是否存在发放时间与现有问卷重叠
     * @param endDate
     * @param statusCodes
     * @param id
     * @return
     */
    boolean existsByEndDateIsAfterAndStatusCodeInAndIdNot(LocalDateTime endDate, List<String> statusCodes, Long id);

    /**
     * 判断问卷是否绑定等级
     */
    Long getQuestionnaireBuyerMemberMamaLevel(Long id);
}
