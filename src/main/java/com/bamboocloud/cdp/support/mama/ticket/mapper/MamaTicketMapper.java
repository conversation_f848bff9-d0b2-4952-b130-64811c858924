/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2022. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-support-api
 * @file: MamaTicketMapper.java
 * @createdDate: 2022/07/26 15:31:26
 *
 */

package com.bamboocloud.cdp.support.mama.ticket.mapper;

import com.bamboocloud.cdp.support.common.dto.mama.ticket.MamaTicketDto;
import com.bamboocloud.cdp.support.common.vo.mama.ticket.MamaTicketUpdateVo;
import com.bamboocloud.cdp.support.sdk.common.entity.ticket.Ticket;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
@DecoratedWith(BaseMamaTicketMapperDecorator.class)
public interface MamaTicketMapper {
    Ticket toEntityForUpdate(MamaTicketUpdateVo mamaTicketUpdateVo, @MappingTarget Ticket ticket);

    MamaTicketDto toDto(Ticket ticket);
}
