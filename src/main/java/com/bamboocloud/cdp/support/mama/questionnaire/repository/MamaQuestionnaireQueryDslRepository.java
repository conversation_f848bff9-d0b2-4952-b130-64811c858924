/*
 * @company: 深证竹云科技股份有限公司
 * @website: www.bamboocloud.com
 * @copyright: Copyright (c) 2023. bamboocloud Information Technology Co., Ltd. All rights reserved.
 * @project: cdp-sale-api
 * @file: MamaQuestionnaireQueryDslRepository.java
 * @createdDate: 2023/03/09 14:38:09
 *
 */

package com.bamboocloud.cdp.support.mama.questionnaire.repository;

import com.bamboocloud.cdp.framework.core.common.base.repository.FwkBaseQuerydslRepository;
import com.bamboocloud.cdp.framework.core.util.FwkStringUtil;
import com.bamboocloud.cdp.support.common.dto.mama.questionnaire.MamaQuestionnaireListDto;
import com.bamboocloud.cdp.support.common.dto.mama.questionnaire.MamaQuestionnairePageDto;
import com.bamboocloud.cdp.support.common.vo.mama.questionnaire.MamaQuestionnaireSearchVo;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QQuestionnaire;
import com.bamboocloud.cdp.support.sdk.common.entity.questionnaire.QQuestionnaireBuyer;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR> Shu
 * @description:
 */
@Component
public class MamaQuestionnaireQueryDslRepository extends FwkBaseQuerydslRepository {
    @Autowired
    private JPAQueryFactory queryFactory;

    public MamaQuestionnairePageDto search(MamaQuestionnaireSearchVo mamaQuestionnaireSearchVo) {
        BooleanBuilder builder = new BooleanBuilder();
        QQuestionnaire questionnaire = QQuestionnaire.questionnaire;
        QQuestionnaireBuyer qQuestionnaireBuyer = QQuestionnaireBuyer.questionnaireBuyer;
        builder.and(questionnaire.deleted.isFalse());
        if (FwkStringUtil.isNotBlank(mamaQuestionnaireSearchVo.getName())) {
            builder.and(questionnaire.name.contains(mamaQuestionnaireSearchVo.getName()));
        }
        if (FwkStringUtil.isNotBlank(mamaQuestionnaireSearchVo.getStatusCode())) {
            builder.and(questionnaire.statusCode.eq(mamaQuestionnaireSearchVo.getStatusCode()));
        }
        if (FwkStringUtil.isNotBlank(mamaQuestionnaireSearchVo.getId())) {
            builder.and(questionnaire.id.stringValue().contains(mamaQuestionnaireSearchVo.getId()));
        }
        if (!ObjectUtils.isEmpty(mamaQuestionnaireSearchVo.getCreatedStartDate())) {
            builder.and(questionnaire.createdDate.goe(mamaQuestionnaireSearchVo.getCreatedStartDate()));
        }
        if (!ObjectUtils.isEmpty(mamaQuestionnaireSearchVo.getCreatedEndDate())) {
            builder.and(questionnaire.createdDate.loe(mamaQuestionnaireSearchVo.getCreatedEndDate()));
        }
        MamaQuestionnairePageDto mamaQuestionnairePageDto = new MamaQuestionnairePageDto();
        mamaQuestionnairePageDto.setTotalCount(queryFactory.select(questionnaire).from(questionnaire).where(builder).fetchCount());
        JPAQuery<MamaQuestionnaireListDto> jpaQuery = queryFactory.select(Projections.constructor(MamaQuestionnaireListDto.class,
                        questionnaire.id,
                        questionnaire.name,
                        questionnaire.description,
                        qQuestionnaireBuyer.count(),
                        questionnaire.statusCode,
                        questionnaire.createdDate)).from(questionnaire)
                .leftJoin(qQuestionnaireBuyer).on(questionnaire.id.eq(qQuestionnaireBuyer.questionnaireId).and(qQuestionnaireBuyer.submitted.isTrue()))
                .groupBy(questionnaire.id,
                        questionnaire.name,
                        questionnaire.description,
                        questionnaire.statusCode,
                        questionnaire.createdDate)
                .where(builder);
        if (!ObjectUtils.isEmpty(mamaQuestionnaireSearchVo.getLimit())) {
            jpaQuery.limit(mamaQuestionnaireSearchVo.getLimit());
            if (!ObjectUtils.isEmpty(mamaQuestionnaireSearchVo.getOffset())) {
                jpaQuery.offset(mamaQuestionnaireSearchVo.getLimit() * mamaQuestionnaireSearchVo.getOffset());
            }
        }
        mamaQuestionnairePageDto.setQuestionnaires(jpaQuery.orderBy(questionnaire.createdDate.desc()).fetch());
        return mamaQuestionnairePageDto;
    }
}
