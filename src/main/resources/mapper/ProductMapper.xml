<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bamboocloud.cdp.sale.v2.dao.mapper.IProductMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.bamboocloud.cdp.sale.v2.dao.entity.ProductEntity">
    <id column="id" property="id"/>
    <result column="shopId" property="shopId"/>
    <result column="productCategoryId" property="productCategoryId"/>
    <result column="typeCode" property="typeCode"/>
    <result column="name" property="name"/>
    <result column="description" property="description"/>
    <result column="version" property="version"/>
    <result column="predefinedShowStartDate" property="predefinedShowStartDate"/>
    <result column="onShow" property="onShow"/>
    <result column="showStartDate" property="showStartDate"/>
    <result column="showEndDate" property="showEndDate"/>
    <result column="createdUserType" property="createdUserType"/>
    <result column="createdUserId" property="createdUserId"/>
    <result column="createdUserName" property="createdUserName"/>
    <result column="createdUserNickName" property="createdUserNickName"/>
    <result column="createdDate" property="createdDate"/>
    <result column="updatedUserType" property="updatedUserType"/>
    <result column="updatedUserId" property="updatedUserId"/>
    <result column="updatedUserName" property="updatedUserName"/>
    <result column="updatedUserNickName" property="updatedUserNickName"/>
    <result column="updatedDate" property="updatedDate"/>
    <result column="deleted" property="deleted"/>
    <result column="deletedUserType" property="deletedUserType"/>
    <result column="deletedUserId" property="deletedUserId"/>
    <result column="deletedUserName" property="deletedUserName"/>
    <result column="deletedUserNickName" property="deletedUserNickName"/>
    <result column="deletedDate" property="deletedDate"/>
    <result column="qrCodeUrl" property="qrCodeUrl"/>
    <result column="subTypeCode" property="subTypeCode"/>
    <result column="mamaOwnedFixedPricePointOnly" property="mamaOwnedFixedPricePointOnly"/>
    <result column="materialTag" property="materialTag"/>
    <result column="containSaleAttr" property="containSaleAttr"/>
    <result column="stockQuantity" property="stockQuantity"/>
    <result column="costPrice" property="costPrice"/>
    <result column="point" property="point"/>
    <result column="pointMama" property="pointMama"/>
    <result column="salePrice" property="salePrice"/>
    <result column="salePriceMama" property="salePriceMama"/>
    <result column="statusCode" property="statusCode"/>
    <result column="reviewRejectReason" property="reviewRejectReason"/>
    <result column="purchaseNote" property="purchaseNote"/>
    <result column="lockQuantity" property="lockQuantity"/>
    <result column="exposedId" property="exposedId"/>
    <result column="distributionProportion" property="distributionProportion"/>
    <result column="distribute" property="distribute"/>
    <result column="distributeDate" property="distributeDate"/>
    <result column="distributeCount" property="distributeCount"/>
    <result column="distributeFee" property="distributeFee"/>
  </resultMap>

  <!-- 通用查询结果列 -->
  <sql id="Base_Column_List">
    id
    , shopId, productCategoryId, typeCode, name, description, version, predefinedShowStartDate, onShow, showStartDate, showEndDate, createdUserType, createdUserId, createdUserName, createdUserNickName, createdDate, updatedUserType, updatedUserId, updatedUserName, updatedUserNickName, updatedDate, deleted, deletedUserType, deletedUserId, deletedUserName, deletedUserNickName, deletedDate, qrCodeUrl, subTypeCode, mamaOwnedFixedPricePointOnly, materialTag, containSaleAttr, stockQuantity, costPrice, point, pointMama, salePrice, salePriceMama, statusCode, reviewRejectReason, purchaseNote, lockQuantity,  exposedId,distributionProportion,distribute,distributeDate,distributeCount,distributeFee
  </sql>
  <update id="updateStatus">
    update Product set statusCode=#{statusCode},version=version+1 where id in
    <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
  </update>

  <select id="findProductIds" resultType="java.lang.String">
    select a.productId from ProductEquityBox a inner join Product b on a.productId=b.id where 1=1
    <if test="request.activityId != null">
      and a.activityId=#{request.activityId}
    </if>
    <if test="request.id != null">
      and a.id = #{request.id}
    </if>
  </select>
  <select id="search"
    resultType="com.bamboocloud.cdp.sale.common.dto.mama.product.MamaBuyerProductListDto">
    select
    distinct
    product.id as productId,
    product.name,
    productCategory.name as productCategoryName,
    product.shopId,
    productFixedPrice.price,
    productBuyer.favoriteDate,
    product.productMainImage
    from Product product
    left join ProductBuyer productBuyer on product.id=productBuyer.productId
    left join ProductCategory productCategory on product.productCategoryId=productCategory.id
    left join ProductFixedPrice productFixedPrice on product.id=productFixedPrice.productId
    WHERE productBuyer.favorite=1
    <if test="searchVo.buyerId != null and searchVo.buyerId !='' ">
      AND productBuyer.buyerId=#{searchVo.buyerId}
    </if>

  </select>
  <select id="getSimpleInfo"
    resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.MamaProductSimpleDto">
    select product.id as productId,
           product.name,
           productFile.fileUrl as productFile,
           product.salePrice price,
           product.productMainImage
    from Product product
           left Join ProductFile productFile on product.id = productFile.productId
    where product.id = #{productId} limit 1
  </select>
  <select id="getByDispShopProductCategoryId" resultType="java.lang.String">
    select qProduct.id
    from Product qProduct
    where qProduct.deleted = 0
      and (exists (select 1
                   from DispShopProductCategoryProduct dispshoppr2
                   where qProduct.id = dispshoppr2.productId
                     and dispshoppr2.dispShopProductCategoryId = #{dispShopProductCategoryId}))
      limit 1

  </select>
  <select id="getVirtualProductByShopIdAndSubTypeCode" resultType="java.lang.String">
    select qProduct.id
    from Product qProduct
           left join ProductCategory qProductCategory on qProduct.productCategoryId = qProductCategory.id
    where qProductCategory.systemInternalUse = 1
      and qProduct.shopId = #{shopId}
      and qProduct.subTypeCode = #{subTypeCode} limit 1
  </select>

  <select id="getBuyerSimpleInfo"
    resultType="com.bamboocloud.cdp.sale.sdk.domain.dto.product.BuyerProductSimpleDto"
    parameterType="java.lang.String">
    select t1.id as productId,
           t1.name as name,
           t2.fileUrl as productFile,
           t2.violation as violation,
           t1.salePrice as salePrice,
           t1.salePriceMama as salePriceMama,
           t1.containSaleAttr  as containSaleAttr,
           t1.productMainImage as productMainImage

    from Product t1
           left join ProductFile t2 on t1.id = t2.productId
    where t1.id = #{productId} limit 1

  </select>
  <select id="getShopIdById" resultType="java.lang.String" parameterType="java.lang.String">
    select shopId
    from Product
    where id = #{productId}
      AND deleted = false
      AND statusCode = 'FIXED_PRICE_SALE_ONGOING'

  </select>


  <update id="updateOnShow">
    update Product
    set onShow      =#{onShow},
        showEndDate =now(),
        version= version + 1
    where id = #{id}
  </update>


  <update id="addDistributeCount">
    update Product
    set distributeCount = distributeCount + #{productQuantity}
    where id = #{productId}
  </update>

  <update id="minusDistributeCount">
    update Product
    set distributeCount = distributeCount - #{productQuantity}
    where id = #{productId}
  </update>

  <select id="getByIds" resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.BuyerProductDto"
    parameterType="java.util.List">
    select id,
    name,
    typeCode,
    description,shopId
    from Product
    where id in
    <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>

  </select>
  <select id="searchBuyer" resultType="com.bamboocloud.cdp.sale.v2.convert.BuyerProductPageBo">
    select
    t1.id as productId,
    t1.name,
    t1.typeCode,
    t1.shopId,
    t1.productCategoryId,
    t1.subTypeCode,
    t1.containSaleAttr,
    t1.stockQuantity,
    t1.point,
    t1.pointMama,
    t1.salePrice,
    t1.salePriceMama,
    t1.mamaOwnedFixedPricePointOnly,
    t1.statusCode,
    t1.updatedDate,
    t1.version,
    t1.productMainImage
    from Product t1
    <if test="searchVo.tagRuleIds  != null and searchVo.tagRuleIds.size> 0 ">
      left join TagRuleCalcPoolMaterialProduct t3 on t1.id = t3.productId
      and (
      select max(id) from TagRuleCalcPoolMaterialProduct tp
      where t1.id = tp.productId
      and tp.tagRuleId in
      <foreach collection="searchVo.tagRuleIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
      )
    </if>
    where t1.deleted =false
    and t1.statusCode ='FIXED_PRICE_SALE_ONGOING'
    and t1.mamaOwnedFixedPricePointOnly = #{searchVo.mamaOwnedFixedPricePointOnly}
    <if test="searchVo.typeCode  != null and searchVo.typeCode  != ''">
      and t1.typeCode =#{searchVo.typeCode}
    </if>
    <if test="searchVo.typeCode  != null and searchVo.typeCode  != ''">
      and t1.typeCode =#{searchVo.typeCode}
    </if>
    <if test="searchVo.productIds  != null and searchVo.productIds.size> 0 ">
      and t1.id in
      <foreach collection="searchVo.productIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <if test="searchVo.shopId  != null and searchVo.shopId  != ''">
      and t1.shopId =#{searchVo.shopId}
    </if>
    <if test="searchVo.name  != null and searchVo.name  != ''">
      and t1.name like concat('%',#{searchVo.name},'%')
    </if>

    <if test="searchVo.productCategoryId  != null and searchVo.productCategoryId  != ''">
      and t1.productCategoryId =#{searchVo.productCategoryId}
    </if>

    <if test="neShopIds  != null and neShopIds.size> 0 ">
      and t1.id not in
      <foreach collection="neShopIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <if test="searchVo.tagRuleIds  != null and searchVo.tagRuleIds.size> 0 ">
      and t3.id in
      <foreach collection="searchVo.tagRuleIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <if test="searchVo.notInTagRuleIds  != null and searchVo.notInTagRuleIds.size> 0 ">
      and t1.id not in
      (
      select trcpp.productId
      from TagRuleCalcPoolMaterialProduct trcpp
      where trcpp.tagRuleId in
      <foreach collection="searchVo.notInTagRuleIds" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
      )
    </if>

    <if test="searchVo.dispShopProductCategoryId  != null">
      and (
      exists(
      select 1
      from DispShopProductCategoryProduct dpcp
      where dpcp.dispShopProductCategoryId = #{searchVo.dispShopProductCategoryId}
      )
      )
    </if>
    <if test="searchVo.subTypeCodes  != null and searchVo.subTypeCodes.size> 0 ">
      and t1.subTypeCode in
      <foreach collection="searchVo.subTypeCodes" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <choose>
      <when test="searchVo.tagRuleIds  != null and searchVo.tagRuleIds.size> 0 ">
        order by t3.weight desc
      </when>
      <otherwise>
        order by t1.updatedDate desc
      </otherwise>
    </choose>


  </select>
  <select id="countByStatusCode"
    resultType="com.bamboocloud.cdp.sale.common.dto.vendor.product.VendorProductCountListDto">
    select a.statusCode,count(*) as totalCount from Product a where deleted = 0

    <if test="vendorProductCountVo.getName  != null and vendorProductCountVo.getName !=''">
      and a.name like CONCAT('%',#{vendorProductCountVo.getName},'%')
    </if>

    <if test="vendorProductCountVo.shopId  != null and vendorProductCountVo.shopId !=''">
      and a.shopId = #{vendorProductCountVo.shopId}
    </if>

    <if test="vendorProductCountVo.productCategoryId  != null and vendorProductCountVo.productCategoryId !=''">
      and a.productCategoryId = #{vendorProductCountVo.productCategoryId}
    </if>

    <if test="vendorProductCountVo.dispShopProductCategoryId  != null and vendorProductCountVo.dispShopProductCategoryId !=''">
      and a.productCategoryId = #{vendorProductCountVo.dispShopProductCategoryId}
    </if>

    <if test="vendorProductCountVo.typeCode  != null and vendorProductCountVo.typeCode !=''">
      and a.typeCode = #{vendorProductCountVo.typeCode}
    </if>
    <if test="vendorProductCountVo.subTypeCodes  != null and vendorProductCountVo.subTypeCodes.size() >0">
      and a.subTypeCode in
      <foreach collection="vendorProductCountVo.subTypeCodes" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    group by a.statusCode
  </select>
  <select id="getBuyerProductView"
    resultType="com.bamboocloud.cdp.sale.common.dto.buyer.product.buyerview.BuyerProductBuyerViewDto">
    select a.id                           as productId,
           a.name                         as productName,
           b.name                         as categoryName,
           c.fileUrl                      as fileUrl,
           c.typeCode                     as fileTypeCode,
           c.violation                    as violation,
           a.salePrice                    as salePrice,
           a.mamaOwnedFixedPricePointOnly as mamaOwnedFixedPricePointOnly,
           a.point                        as mamaOwnedFixedPricePointOnly,
           a.pointMama                    as pointMama,
           a.salePriceMama                as salePriceMama,
           a.subTypeCode                  as subTypeCode
    from Product a
           left join ProductCategory b on a.productCategoryId = b.id
           left join ProductFile c on a.id = c.productId
    where a.id = #{productId}
      and c.typeCode = 'IMAGE' limit 1
  </select>


  <select id="getAllProductCategory" resultType="java.lang.Integer">
    select distinct (productCategoryId) from Product
        where deleted = 0
      <if test="param.productIds  == null or param.productIds.size() == 0">
          and mamaOwnedFixedPricePointOnly = false
      </if>
      <if test="param.productIds  != null and param.productIds.size() >0">
          and id in
          <foreach collection="param.productIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="param.neShopIds  != null and param.neShopIds.size() >0">
          and shopId not in
          <foreach collection="param.neShopIds" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="param.civilProduct  != null ">
          and civilProduct = #{param.civilProduct}
      </if>
    <if test="param.distribute  != null ">
      and distribute = #{param.distribute}
    </if>
    <if test="param.subTypeCodes  != null and param.subTypeCodes.size> 0 ">
      and subTypeCode in
      <foreach collection="param.subTypeCodes" item="item" index="index" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="param.productPointPlatformUsage !=null">
      and subTypeCode = 'FIXED_PRICE_POINT'
      and exists(select 1 from productfixedpricepoint pp where Product.id = pp.productId and (pp.productPointPlatformUsage &amp; #{param.productPointPlatformUsage} = #{param.productPointPlatformUsage}))
    </if>
  </select>

  <select id="page4Hotel" resultType="com.bamboocloud.cdp.sale.domain.vo.ProductListVO">
    select
      p.id, p.name, p.onShow status, p.createdDate createTime, p.updatedDate updateTime,
      rt.id roomTypeId, rt.name roomTypeName
    from product p
    left join t_hotel_room_type rt on p.exposedId=rt.id
    where
      p.deleted=0
      and rt.deleted=0
      and p.shopId=#{param.shopId}
      and p.typeCode=#{param.typeCode}
      and p.onShow=#{param.status}
      <if test="param.name!=null and param.name!=''">
        and p.name like concat('%',#{param.name},'%')
      </if>
      <if test="param.rootTypeName!=null and param.rootTypeName!=''">
        and rt.name like concat('%',#{param.rootTypeName},'%')
      </if>
    order by p.createdDate desc
  </select>

  <select id="list4Hotel" resultType="com.bamboocloud.cdp.sale.domain.vo.ProductList4cDTO">
    select
        p.id, p.name, p.ext_info extInfo,
        MAX(CASE WHEN cs.calendar = #{param.fromDate} THEN cs.price ELSE NULL END)  price,
        CASE
          WHEN COUNT(DISTINCT cs.calendar) <![CDATA[ < ]]> #{param.day} or min(cs.stock) <![CDATA[ < ]]> #{param.roomNumber}
          THEN 0
          ELSE min(cs.stock)
        END AS stock,
        rt.id roomTypeId, rt.name roomTypeName, rt.bed_type bedType, rt.append_bed_num appendBedNum,
        rt.area, rt.has_window hasWindow, rt.window_detail_dict_id windowDetailDictId, rt.floor, rt.smoking_status smokingStatus
    from product p
    left join t_hotel_room_type rt on p.exposedId=rt.id
    left join t_hotel_calendar_stock cs on cs.product_id=p.id
    where
        p.deleted=0 and p.onShow=1 and p.typeCode=#{param.typeCode}
        and rt.deleted=0 and cs.price>0
        and cs.calendar>= #{param.fromDate} and cs.calendar <![CDATA[ < ]]> #{param.toDate}
        <if test="param.shopId!=null and param.shopId!=''">
          and rt.shop_id=#{param.shopId}
        </if>
        <if test="param.productId!=null and param.productId!=''">
          and p.id=#{param.productId}
        </if>
    group by p.id
    order by cs.stock desc, p.id desc
  </select>
  <select id="getAlcoholRedemptionCount" resultType="com.bamboocloud.cdp.user.sdk.domain.dto.mama.shop.MamaShopCombineDto$AlcoholRedemptionCountRes">
    SELECT p.shopId,
           SUM(IFNULL(stock.stockQuantity, p.stockQuantity) * IFNULL(pf.give_alcohol_redemption_num, 0)) AS stockNum
    FROM product p
           LEFT JOIN productfixedprice pf ON pf.productId = p.id AND pf.give_alcohol_redemption = true
           LEFT JOIN productstock stock ON stock.productId = p.id
    WHERE pf.id IS NOT NULL
      and p.subTypeCode = 'FIXED_PRICE_OFFLINE'
      and p.typeCode = 'FIXED_PRICE'
      <foreach collection="shopIds" item="item" index="index" open="and p.shopId in (" separator="," close=")">
        #{item}
      </foreach>
    GROUP BY p.shopId;
  </select>
</mapper>
