[[resources-mama-chatRecord]]
== 聊天记录

[[resources-mama-chatRecord-operation]]
=== 聊天记录

包含聊天记录已读等API。


[[resources-mama-chatRecord-updateReceiverRead]]
==== 修改消息为已读

===== PUT /support-api/v1/mamas/chatRecords/actions/updateReceiverRead

operation::MamaChatRecordAppTest/testUpdateReceiverRead[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']

[[resources-mama-chatRecord-updateReceiverReadById]]
==== 修改一条消息为已读（根据Id）

===== PUT /support-api/v1/mamas/chatRecords/actions/updateReceiverRead/{id}

operation::MamaChatRecordAppTest/testUpdateReceiverReadById[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-mama-chatRecord-bulkUpdateReceiverRead]]
==== 批量修改消息为已读（根据Id数组）

===== POST /support-api/v1/mamas/chatRecords/actions/bulkUpdateReceiverRead

operation::MamaChatRecordAppTest/testBulkUpdateReceiverRead[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']


[[resources-mama-chat-changeMama]]
==== 转接客服

===== POST /support-api/v1/mamas/chats/actions/changeMama

operation::MamaChatAppTest/testChangeMama[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']


[[resources-mama-chat-finish]]
==== 关闭会话
===== PUT /support-api/v1/mamas/chats/actions/finish/{id}
operation::MamaChatAppTest/testFinish[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-mama-chatRecord-pullout]]
==== 撤回一条聊天记录
===== PUT /support-api/v1/mamas/chatRecords/actions/pullout/{id}
operation::VendorChatRecordAppTest/testPullout[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']
