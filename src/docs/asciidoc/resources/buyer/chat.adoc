[[resources-buyer-chat]]
== 聊天列表

[[resources-buyer-chat-operation]]
=== 聊天列表

包含新增、删除聊天列表等API。

[[resources-buyer-chat-create]]
==== 新增聊天列表

===== POST /support-api/v1/buyers/chats

operation::BuyerChatAppTest/testCreate[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']


[[resources-buyer-chat-updateTop]]
==== 修改聊天列表置顶、取消置顶

===== PUT /support-api/v1/buyers/chats/actions/updateTop/{id}

operation::BuyerChatAppTest/testUpdateTop[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chat-delete]]
==== 删除聊天列表

===== DELETE /support-api/v1/buyers/chats/{id}

operation::BuyerChatAppTest/testDelete[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-buyer-chat-bulkDelete]]
==== 批量删除聊天列表

===== POST /support-api/v1/buyers/chats/actions/bulkDelete

operation::BuyerChatAppTest/testBulkDelete[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chatRecord]]
== 聊天记录

[[resources-buyer-chatRecord-operation]]
=== 聊天记录

包含聊天记录查找等API。


[[resources-buyer-chatRecord-updateReceiverRead]]
==== 修改消息为已读

===== POST /support-api/v1/buyers/chatRecords/actions/updateReceiverRead

operation::BuyerChatRecordAppTest/testUpdateReceiverRead[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']

[[resources-buyer-chatRecord-batchUpdateReceiverRead]]
==== 全部已读

===== POST /support-api/v1/buyers/chatRecords/actions/batchUpdateReceiverRead

operation::BuyerChatRecordAppTest/testBatchUpdateReceiverRead[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chatRecord-updateReceiverReadById]]
==== 修改一条消息为已读（根据Id）

===== PUT /support-api/v1/buyers/chatRecords/actions/updateReceiverReadById/{id}

operation::BuyerChatRecordAppTest/testUpdateReceiverReadById[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']

[[resources-buyer-chatRecord-markRead]]
==== 标记已读/未读

===== PUT /support-api/v1/buyers/chatRecords/actions/markRead/{id}

operation::BuyerChatRecordAppTest/testMarkRead[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chatRecord-bulkUpdateReceiverRead]]
==== 批量修改消息为已读（根据Id数组）

===== POST /support-api/v1/buyers/chatRecords/actions/bulkUpdateReceiverRead

operation::BuyerChatRecordAppTest/testBulkUpdateReceiverRead[snippets='request-headers,request-fields,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chatRecord-updateMessageTypeCode]]
==== 修改消息类型

===== PUT /support-api/v1/buyers/chatRecords/actions/updateMessageTypeCode/{id}

operation::BuyerChatRecordAppTest/testUpdateMessageTypeCode[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']


[[resources-buyer-chatRecord-updateMessage]]
==== 修改消息

===== PUT /support-api/v1/buyers/chatRecords/actions/updateMessage/{id}

operation::BuyerChatRecordAppTest/testUpdateMessage[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']



[[resources-buyer-chatRecord-delete]]
==== 删除一条聊天记录

===== DELETE /support-api/v1/buyers/chatRecords/{id}

operation::BuyerChatRecordAppTest/testDelete[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-buyer-chatRecord-pullout]]
==== 撤回一条聊天记录

===== PUT /support-api/v1/buyers/chatRecords/actions/pullout/{id}

operation::BuyerChatRecordAppTest/testPullout[snippets='path-parameters,request-headers,request-fields,response-fields,response-headers,curl-request,http-response']
