[[resources-everyone-questionnaire]]
== 问卷管理

[[resources-everyone-questionnaire-operation]]
=== 问卷管理

包含查询问卷状态列表、查询发放对象列表和查询题型列表等API。

[[resources-everyone-questionnaire-listStatus]]
==== 查询问卷状态列表
===== GET /support-api/v1/everyone/questionnaires/actions/listStatus
operation::EveryoneQuestionnaireAppTest/testListStatus[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-everyone-questionnaire-listLimitBuyers]]
==== 查询发放对象列表
===== GET /support-api/v1/everyone/questionnaires/actions/listLimitBuyers
operation::EveryoneQuestionnaireAppTest/testListLimitBuyers[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-everyone-questionnaire-listCaseTypes]]
==== 查询题型列表
===== GET /support-api/v1/everyone/questionnaires/actions/listCaseTypes
operation::EveryoneQuestionnaireAppTest/testListCaseTypes[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']


[[resources-everyone-questionnaire-listNotificationVariables]]
==== 查询问卷通知变量列表
===== GET /support-api/v1/everyone/questionnaires/actions/listNotificationVariables
operation::EveryoneQuestionnaireAppTest/testListNotificationVariables[snippets='path-parameters,request-headers,response-fields,response-headers,curl-request,http-response']

