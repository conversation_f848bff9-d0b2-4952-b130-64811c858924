buildscript {

    def getCurrentGitBranchName = {
        new ByteArrayOutputStream().withStream { os ->
            exec {
                executable = "git"
                args = ["rev-parse", "--abbrev-ref", "HEAD"]
                standardOutput = os
            }
            return os.toString()
        }
    }
    ext {
        /** Todo: 项目开始阶段，暂时不指向Master分支
         agileActGradleHome = "https://code.agileact.com/agileact-public/gradle-script-project/raw/master/"
         agileActGradleHome = "https://code.agileact.com/agileact-public/gradle-script-bbc/-/raw/v1.0.3/"
         agileActGradleHome = "https://code.agileact.com/agileact-public/gradle-script-project/-/raw/develop/"
         */
        osName = System.getProperty("os.name").toLowerCase()
        if (osName.contains("windows")|| osName.contains("mac")) {
            agileActGradleHome = "../glyb-cdp-script-huawei/"
        } else {
            agileActGradleHome = "glyb-cdp-script-huawei/"
        }
        println "agileActGradleHome is ${agileActGradleHome}"

        // 先从Gitlab CI中取branch名字 --- Gitlab CI中运行getCurrentGitBranchName()，会取不到branch name.
        currentGitBranchName = "${CI_COMMIT_REF_NAME}"
        println "currentGitBranchName-ci is ${currentGitBranchName}"
        containOrigin = false
        // 本地非Gitlab环境运行时，取branch name
        if (currentGitBranchName.startsWith("NOT_SET")) {
            try {
                currentGitBranchName = "${branchName}"
                containOrigin = true
            } catch (Exception e) {
                currentGitBranchName = getCurrentGitBranchName()
            }
        }
        println "currentGitBranchName is ${currentGitBranchName}"

        env = "_dev"
        List<String> proBranchPrefixes = Arrays.asList("origin/RE-pre", "origin/main", "origin/hotfix","RE-pre","main","hotfix");

        if (currentGitBranchName != null) { // 防止空指针异常
            for (String prefix : proBranchPrefixes) {
                if (currentGitBranchName.startsWith(prefix)) {
                    env = "_pro";
                    break; // 找到匹配后退出循环
                }
            }
        }
        println "env is ${env}"
    }
    apply from: 'support-sdk/config/gradle/appBaseVersion.gradle'
    apply from: agileActGradleHome + 'bamboocloud/bbc/bbcSdkVersion.gradle'
    apply from: agileActGradleHome + 'bamboocloud/bbc/bbcBasicVersion' + env + '.gradle'
//    apply from: agileActGradleHome + 'bamboocloud/bbc/support/api/bbcSupportApiVersion.gradle'
    apply from: 'config/gradle/bbcSupportApiVersion.gradle'

    apply from: 'config/gradle/repository.gradle'

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        //        classpath("org.asciidoctor:asciidoctor-gradle-plugin:${asciiDoctorPluginVersion}")
        classpath("org.asciidoctor:asciidoctor-gradle-jvm:${asciiDoctorGradleJvmVersion}")
        classpath("gradle.plugin.com.github.johnrengelman:shadow:${shadowVersion}")
    }
}

apply from: agileActGradleHome + 'bamboocloud/bbc/bbcBasicDependency.gradle'
//apply from: agileActGradleHome + 'bamboocloud/bbc/support/api/bbcSupportApiDependency.gradle'
apply from: 'config/gradle/bbcSupportApiDependency.gradle'

apply from: agileActGradleHome + 'general/build.gradle'
apply from: 'config/gradle/repository.gradle'
apply from: 'config/gradle/upload.gradle'
